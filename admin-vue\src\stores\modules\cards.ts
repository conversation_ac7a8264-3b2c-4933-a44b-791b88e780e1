import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { CardService } from '@/api'
import type { Card, SortState, CardType } from '@/types'

export const useCardsStore = defineStore('cards', () => {
  // 状态
  const cards = ref<Card[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 生成相关状态
  const isGenerating = ref(false)
  const generatedCards = ref<string[]>([])
  const generateProgress = ref(0)
  const generateTotal = ref(0)
  
  // 搜索和筛选状态
  const searchTerm = ref('')
  const selectedCards = ref<string[]>([])
  
  // 排序状态
  const sortState = ref<SortState>({
    field: 'createdAt',
    direction: 'desc'
  })

  // 计算属性 - 过滤和排序后的卡密数据
  const filteredCards = computed(() => {
    let result = [...cards.value]

    // 搜索过滤
    if (searchTerm.value) {
      const term = searchTerm.value.toLowerCase()
      result = result.filter(card => 
        card.code.toLowerCase().includes(term) ||
        CardService.getCardTypeName(card.type).toLowerCase().includes(term) ||
        card.status.toLowerCase().includes(term) ||
        (card.usedBy && card.usedBy.toLowerCase().includes(term))
      )
    }

    // 排序
    if (sortState.value.field) {
      result.sort((a, b) => {
        const field = sortState.value.field!
        let valueA: any = (a as any)[field]
        let valueB: any = (b as any)[field]

        if (valueA === valueB) return 0
        
        const modifier = sortState.value.direction === 'asc' ? 1 : -1
        return valueA > valueB ? modifier : -modifier
      })
    }

    return result
  })

  // 卡密类型状态
  const cardTypes = ref<CardType[]>(CardService.CARD_TYPES)
  const isLoadingTypes = ref(false)

  // 动作 - 加载套餐类型
  const loadCardTypes = async () => {
    if (isLoadingTypes.value) return

    isLoadingTypes.value = true
    try {
      const types = await CardService.getPackageTypes()
      cardTypes.value = types
    } catch (err) {
      console.warn('加载套餐类型失败，使用默认配置:', err)
      cardTypes.value = CardService.CARD_TYPES
    } finally {
      isLoadingTypes.value = false
    }
  }

  // 计算属性 - 是否全选
  const isAllSelected = computed(() => {
    return filteredCards.value.length > 0 && 
           selectedCards.value.length === filteredCards.value.length
  })

  // 动作 - 加载卡密列表
  const loadCards = async () => {
    isLoading.value = true
    error.value = null

    try {
      const data = await CardService.getCards()
      cards.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载卡密失败'
    } finally {
      isLoading.value = false
    }
  }

  // 动作 - 生成卡密预览
  const generatePreview = async (type: string): Promise<string> => {
    try {
      return await CardService.generatePreview(type)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '生成预览失败'
      throw err
    }
  }

  // 动作 - 批量生成卡密 (使用新API的批量功能)
  const generateCards = async (type: string, count: number, customCode?: string) => {
    isGenerating.value = true
    generateProgress.value = 0
    generateTotal.value = count
    generatedCards.value = []
    error.value = null

    try {
      if (count === 1 && customCode) {
        // 单个自定义卡密生成
        const code = await CardService.generateCard({
          packageType: type,
          type,
          customCode
        })
        generatedCards.value = [code]
      } else {
        // 批量生成
        const codes = await CardService.generateCards(type, count)
        generatedCards.value = codes
      }

      generateProgress.value = generatedCards.value.length

      // 重新加载卡密列表
      await loadCards()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '生成卡密失败'
      throw err
    } finally {
      isGenerating.value = false
    }
  }

  // 动作 - 编辑卡密
  const editCard = async (oldCode: string, newCode: string, type: string) => {
    try {
      await CardService.editCard({ oldCode, newCode, type })
      
      // 更新本地数据
      const cardIndex = cards.value.findIndex(card => card.code === oldCode)
      if (cardIndex !== -1) {
        cards.value[cardIndex].code = newCode
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '编辑卡密失败'
      throw err
    }
  }

  // 动作 - 删除卡密
  const deleteCard = async (code: string) => {
    try {
      await CardService.deleteCard(code)
      
      // 从本地数据中移除
      const cardIndex = cards.value.findIndex(card => card.code === code)
      if (cardIndex !== -1) {
        cards.value.splice(cardIndex, 1)
      }
      
      // 从选中列表中移除
      const selectedIndex = selectedCards.value.indexOf(code)
      if (selectedIndex !== -1) {
        selectedCards.value.splice(selectedIndex, 1)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除卡密失败'
      throw err
    }
  }

  // 动作 - 批量删除卡密
  const deleteSelectedCards = async () => {
    const codes = [...selectedCards.value]
    
    for (const code of codes) {
      try {
        await deleteCard(code)
      } catch (err) {
        console.error(`删除卡密 ${code} 失败:`, err)
      }
    }
    
    selectedCards.value = []
  }

  // 动作 - 设置排序
  const setSort = (field: string) => {
    if (sortState.value.field === field) {
      sortState.value.direction = sortState.value.direction === 'asc' ? 'desc' : 'asc'
    } else {
      sortState.value.field = field
      sortState.value.direction = 'asc'
    }
  }

  // 动作 - 设置搜索
  const setSearch = (term: string) => {
    searchTerm.value = term
  }

  // 动作 - 切换卡密选中状态
  const toggleCardSelection = (code: string) => {
    const index = selectedCards.value.indexOf(code)
    if (index === -1) {
      selectedCards.value.push(code)
    } else {
      selectedCards.value.splice(index, 1)
    }
  }

  // 动作 - 全选/取消全选
  const toggleSelectAll = () => {
    if (isAllSelected.value) {
      selectedCards.value = []
    } else {
      selectedCards.value = filteredCards.value.map(card => card.code)
    }
  }

  // 动作 - 清空生成结果
  const clearGeneratedCards = () => {
    generatedCards.value = []
    generateProgress.value = 0
    generateTotal.value = 0
  }

  // 动作 - 清除错误
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    cards,
    isLoading,
    error,
    isGenerating,
    generatedCards,
    generateProgress,
    generateTotal,
    searchTerm,
    selectedCards,
    sortState,
    // 计算属性
    filteredCards,
    isAllSelected,
    // 状态
    cardTypes,
    isLoadingTypes,
    // 动作
    loadCards,
    loadCardTypes,
    generatePreview,
    generateCards,
    editCard,
    deleteCard,
    deleteSelectedCards,
    setSort,
    setSearch,
    toggleCardSelection,
    toggleSelectAll,
    clearGeneratedCards,
    clearError
  }
})
