import{d as o,z as r,A as e,B as t}from"./index-bBUuTVMS.js";const l={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},k=o({name:"CardOutline",render:function(s,n){return t(),r("svg",l,n[0]||(n[0]=[e("rect",{x:"48",y:"96",width:"416",height:"320",rx:"56",ry:"56",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),e("path",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"60",d:"M48 192h416"},null,-1),e("path",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"60",d:"M128 300h48v20h-48z"},null,-1)]))}});export{k as C};
