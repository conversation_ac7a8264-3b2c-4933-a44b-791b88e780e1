<template>
  <div class="usage-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <n-icon :component="BarChartOutline" />
        用量统计
      </h2>
      <p class="page-description">查看用户使用情况和统计数据</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <StatsCard
        :value="globalStats.totalUsers"
        label="总用户数"
        :icon="PeopleOutline"
        color="primary"
        :loading="isLoadingGlobalStats"
      />
      <StatsCard
        :value="globalStats.totalCharsUsed"
        label="历史总字符数"
        :icon="DocumentTextOutline"
        color="success"
        format="number"
        :loading="isLoadingGlobalStats"
      />
      <StatsCard
        :value="globalStats.monthlyCharsUsed"
        label="本月字符数"
        :icon="TrendingUpOutline"
        color="warning"
        format="number"
        :loading="isLoadingGlobalStats"
      />
      <StatsCard
        :value="globalStats.vipUsersCount"
        label="VIP用户数"
        :icon="StarOutline"
        color="error"
        :loading="isLoadingGlobalStats"
      />
    </div>

    <!-- 控制区域 -->
    <div class="controls-section">
      <div class="controls-left">
        <SearchInput
          v-model="searchTerm"
          placeholder="搜索用户名..."
          @search="handleSearch"
        />
        
        <FilterButtons
          v-model="activeFilter"
          :options="filterOptions"
          label="筛选条件："
          @change="handleFilterChange"
        />
      </div>
      
      <div class="controls-right">
        <n-select
          v-model:value="pageSize"
          :options="pageSizeOptions"
          style="width: 120px"
          @update:value="handlePageSizeChange"
        />
        
        <n-button
          type="primary"
          @click="handleRefresh"
          :loading="isLoading"
        >
          <template #icon>
            <n-icon :component="RefreshOutline" />
          </template>
          刷新数据
        </n-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <DataTable
        :data="filteredUsageData"
        :columns="tableColumns"
        :loading="isLoading"
        row-key="username"
        :pagination="false"
      />
      
      <!-- 分页控制 -->
      <div class="pagination-section">
        <n-button
          :disabled="!canLoadMore || isLoading"
          @click="loadMore"
          :loading="isLoading"
        >
          加载更多
        </n-button>
        
        <span class="pagination-info">
          已显示 {{ filteredUsageData.length }} 条数据
          {{ canLoadMore ? '，点击加载更多' : '，已全部加载' }}
        </span>
      </div>
    </div>

    <!-- 错误提示 -->
    <n-alert
      v-if="error"
      type="error"
      :title="error"
      closable
      @close="clearError"
      style="margin-top: 16px"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onActivated, onBeforeUnmount, h, ref } from 'vue'
import {
  NIcon,
  NButton,
  NAlert,
  NTag,
  NSelect
} from 'naive-ui'
import {
  BarChartOutline,
  PeopleOutline,
  DocumentTextOutline,
  TrendingUpOutline,
  StarOutline,
  RefreshOutline
} from '@vicons/ionicons5'
import { DataTable, SearchInput, StatsCard, FilterButtons } from '@/components/common'
import { useUsersStore } from '@/stores'
import type { TableColumn } from '@/types'

// 状态管理
const usersStore = useUsersStore()

// 响应式数据
const pageSize = ref(200)

// 计算属性
const filteredUsageData = computed(() => usersStore.filteredUsageData)
const isLoading = computed(() => usersStore.isLoadingUsage)
const error = computed(() => usersStore.usageError)
const usageStats = computed(() => usersStore.usageStats)
const canLoadMore = computed(() => usersStore.usagePagination.hasMore)

// 全局统计相关
const globalStats = computed(() => usersStore.globalStats)
const isLoadingGlobalStats = computed(() => usersStore.isLoadingGlobalStats)
const globalStatsError = computed(() => usersStore.globalStatsError)

const searchTerm = computed({
  get: () => usersStore.usageFilter.searchTerm,
  set: (value: string) => usersStore.setUsageSearch(value)
})

const activeFilter = computed({
  get: () => usersStore.usageFilter.activeFilter,
  set: (value: string) => usersStore.setUsageFilter(value)
})

// 筛选选项
const filterOptions = [
  { value: 'all', label: '全部用户' },
  { value: 'active', label: '有使用记录' },
  { value: 'inactive', label: '无使用记录' },
  { value: 'vip', label: 'VIP用户' },
  { value: 'non-vip', label: '非VIP用户' }
]

// 页面大小选项
const pageSizeOptions = [
  { label: '50 条/页', value: 50 },
  { label: '100 条/页', value: 100 },
  { label: '200 条/页', value: 200 },
  { label: '500 条/页', value: 500 },
  { label: '1000 条/页', value: 1000 }
]

// 表格列配置
const tableColumns: TableColumn[] = [
  {
    key: 'username',
    title: '用户名',
    sortable: true,
    width: 150
  },
  {
    key: 'usage.totalChars',
    title: '历史总字符',
    sortable: true,
    width: 150,
    render: (row) => {
      const chars = row.usage.totalChars
      let color = '#10b981'
      
      if (chars > 1000000) {
        color = '#ef4444'
      } else if (chars > 500000) {
        color = '#f59e0b'
      }
      
      return h('span', { 
        style: `font-weight: 600; color: ${color}; font-family: monospace;` 
      }, chars.toLocaleString())
    }
  },
  {
    key: 'usage.monthlyChars',
    title: '本月字符',
    sortable: true,
    width: 150,
    render: (row) => {
      const chars = row.usage.monthlyChars
      let color = '#10b981'
      
      if (chars > 100000) {
        color = '#ef4444'
      } else if (chars > 50000) {
        color = '#f59e0b'
      }
      
      return h('span', { 
        style: `font-weight: 600; color: ${color}; font-family: monospace;` 
      }, chars.toLocaleString())
    }
  },
  {
    key: 'usage.monthlyResetAt',
    title: '月度重置时间',
    sortable: true,
    width: 180,
    render: (row) => {
      return new Date(row.usage.monthlyResetAt).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  },
  {
    key: 'createdAt',
    title: '注册时间',
    sortable: true,
    width: 180,
    render: (row) => {
      return new Date(row.createdAt).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  },
  {
    key: 'vip',
    title: 'VIP状态',
    sortable: true,
    width: 160,
    render: (row) => {
      if (row.vip) {
        const expireDate = new Date(row.vip.expireAt).toLocaleDateString()
        const isExpired = row.vip.expireAt < Date.now()
        
        return h(NTag, {
          type: isExpired ? 'error' : 'success',
          size: 'small'
        }, {
          default: () => `${expireDate} (${row.vip.type})`
        })
      } else {
        return h(NTag, {
          type: 'default',
          size: 'small'
        }, {
          default: () => '非VIP'
        })
      }
    }
  }
]

// 处理搜索
const handleSearch = (value: string) => {
  usersStore.setUsageSearch(value)
}

// 处理筛选变化
const handleFilterChange = (value: string) => {
  usersStore.setUsageFilter(value)
}

// 处理页面大小变化
const handlePageSizeChange = (value: number) => {
  pageSize.value = value
  handleRefresh()
}

// 处理刷新
const handleRefresh = async () => {
  try {
    // 并行刷新全局统计和用量数据
    await Promise.all([
      usersStore.loadGlobalStats(),
      usersStore.loadUsageData({ limit: pageSize.value }, true)
    ])
  } catch (error) {
    console.error('数据刷新失败:', error)
  }
}

// 加载更多
const loadMore = async () => {
  try {
    await usersStore.loadUsageData({
      limit: pageSize.value,
      cursor: usersStore.usagePagination.nextCursor
    }, false)
  } catch (error) {
    console.error('加载更多失败:', error)
  }
}

// 清除错误
const clearError = () => {
  usersStore.clearErrors()
}

// 组件挂载时加载数据
onMounted(async () => {
  // 并行加载全局统计和用量数据
  await Promise.all([
    usersStore.loadGlobalStats(),
    usersStore.loadUsageData({ limit: pageSize.value }, true)
  ])
})

// 组件激活时不自动加载数据，改为手动刷新模式
onActivated(() => {
  // 使用缓存数据，需要时请手动刷新
})

// 组件卸载前清理
onBeforeUnmount(() => {
  usersStore.clearErrors()
})
</script>

<style scoped>
.usage-view {
  width: 100%;
  margin: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.controls-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  gap: 16px;
  flex-wrap: wrap;
}

.controls-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.controls-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.pagination-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
}

/* 响应式 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .controls-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .controls-left,
  .controls-right {
    justify-content: center;
  }
  
  .page-title {
    font-size: 20px;
  }
}
</style>
