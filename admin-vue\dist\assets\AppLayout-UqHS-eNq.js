import{d as L,h as u,Q as Ee,a as Me,R as Te,S as ue,n as te,c as m,b as w,r as M,u as ce,k as Y,j as h,m as se,p as J,f as v,e as I,y as Le,t as ae,x as q,g as ee,i as no,T as Q,q as ge,U as lo,N as io,V as Ne,o as ao,W as co,z as ie,A as E,B as Z,X as so,Y as uo,C as vo,P as mo,J as ve,F as W,G as _,H as me,E as U,Z as ke,w as ho,v as po,M as fo,L as go,_ as bo,$ as Co,a0 as xo}from"./index-bBUuTVMS.js";import{s as yo,S as $e,d as Fe,C as zo,f as he,u as be,c as K,k as ye,V as Io,N as oe,B as wo,_ as So}from"./_plugin-vue_export-helper-JcRYbv4V.js";import{d as Ao,t as Ho,N as Ro,k as pe,a as Po,c as fe,u as To,V as No}from"./Dropdown-CPvaWprP.js";import{P as ko}from"./PersonOutline-R4Ifj_Oy.js";import{P as _o}from"./PeopleOutline-SQXC-ssz.js";import{B as Bo}from"./BarChartOutline-mQUuYBKn.js";import{C as Oo}from"./CardOutline-B-jn5H-Z.js";const Eo=L({name:"ChevronDownFilled",render(){return u("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},u("path",{d:"M3.20041 5.73966C3.48226 5.43613 3.95681 5.41856 4.26034 5.70041L8 9.22652L11.7397 5.70041C12.0432 5.41856 12.5177 5.43613 12.7996 5.73966C13.0815 6.0432 13.0639 6.51775 12.7603 6.7996L8.51034 10.7996C8.22258 11.0668 7.77743 11.0668 7.48967 10.7996L3.23966 6.7996C2.93613 6.51775 2.91856 6.0432 3.20041 5.73966Z",fill:"currentColor"}))}});function Mo(e){const{baseColor:t,textColor2:o,bodyColor:r,cardColor:c,dividerColor:i,actionColor:a,scrollbarColor:d,scrollbarColorHover:s,invertedColor:b}=e;return{textColor:o,textColorInverted:"#FFF",color:r,colorEmbedded:a,headerColor:c,headerColorInverted:b,footerColor:a,footerColorInverted:b,headerBorderColor:i,headerBorderColorInverted:b,footerBorderColor:i,footerBorderColorInverted:b,siderBorderColor:i,siderBorderColorInverted:b,siderColor:c,siderColorInverted:b,siderToggleButtonBorder:`1px solid ${i}`,siderToggleButtonColor:t,siderToggleButtonIconColor:o,siderToggleButtonIconColorInverted:o,siderToggleBarColor:Te(r,d),siderToggleBarColorHover:Te(r,s),__invertScrollbar:"true"}}const ze=Ee({name:"Layout",common:Me,peers:{Scrollbar:yo},self:Mo});function Lo(e,t,o,r){return{itemColorHoverInverted:"#0000",itemColorActiveInverted:t,itemColorActiveHoverInverted:t,itemColorActiveCollapsedInverted:t,itemTextColorInverted:e,itemTextColorHoverInverted:o,itemTextColorChildActiveInverted:o,itemTextColorChildActiveHoverInverted:o,itemTextColorActiveInverted:o,itemTextColorActiveHoverInverted:o,itemTextColorHorizontalInverted:e,itemTextColorHoverHorizontalInverted:o,itemTextColorChildActiveHorizontalInverted:o,itemTextColorChildActiveHoverHorizontalInverted:o,itemTextColorActiveHorizontalInverted:o,itemTextColorActiveHoverHorizontalInverted:o,itemIconColorInverted:e,itemIconColorHoverInverted:o,itemIconColorActiveInverted:o,itemIconColorActiveHoverInverted:o,itemIconColorChildActiveInverted:o,itemIconColorChildActiveHoverInverted:o,itemIconColorCollapsedInverted:e,itemIconColorHorizontalInverted:e,itemIconColorHoverHorizontalInverted:o,itemIconColorActiveHorizontalInverted:o,itemIconColorActiveHoverHorizontalInverted:o,itemIconColorChildActiveHorizontalInverted:o,itemIconColorChildActiveHoverHorizontalInverted:o,arrowColorInverted:e,arrowColorHoverInverted:o,arrowColorActiveInverted:o,arrowColorActiveHoverInverted:o,arrowColorChildActiveInverted:o,arrowColorChildActiveHoverInverted:o,groupTextColorInverted:r}}function $o(e){const{borderRadius:t,textColor3:o,primaryColor:r,textColor2:c,textColor1:i,fontSize:a,dividerColor:d,hoverColor:s,primaryColorHover:b}=e;return Object.assign({borderRadius:t,color:"#0000",groupTextColor:o,itemColorHover:s,itemColorActive:ue(r,{alpha:.1}),itemColorActiveHover:ue(r,{alpha:.1}),itemColorActiveCollapsed:ue(r,{alpha:.1}),itemTextColor:c,itemTextColorHover:c,itemTextColorActive:r,itemTextColorActiveHover:r,itemTextColorChildActive:r,itemTextColorChildActiveHover:r,itemTextColorHorizontal:c,itemTextColorHoverHorizontal:b,itemTextColorActiveHorizontal:r,itemTextColorActiveHoverHorizontal:r,itemTextColorChildActiveHorizontal:r,itemTextColorChildActiveHoverHorizontal:r,itemIconColor:i,itemIconColorHover:i,itemIconColorActive:r,itemIconColorActiveHover:r,itemIconColorChildActive:r,itemIconColorChildActiveHover:r,itemIconColorCollapsed:i,itemIconColorHorizontal:i,itemIconColorHoverHorizontal:b,itemIconColorActiveHorizontal:r,itemIconColorActiveHoverHorizontal:r,itemIconColorChildActiveHorizontal:r,itemIconColorChildActiveHoverHorizontal:r,itemHeight:"42px",arrowColor:c,arrowColorHover:c,arrowColorActive:r,arrowColorActiveHover:r,arrowColorChildActive:r,arrowColorChildActiveHover:r,colorInverted:"#0000",borderColorHorizontal:"#0000",fontSize:a,dividerColor:d},Lo("#BBB",r,"#FFF","#AAA"))}const Fo=Ee({name:"Menu",common:Me,peers:{Tooltip:Ho,Dropdown:Ao},self:$o}),je=te("n-layout-sider"),Ie={type:String,default:"static"},jo=m("layout",`
 color: var(--n-text-color);
 background-color: var(--n-color);
 box-sizing: border-box;
 position: relative;
 z-index: auto;
 flex: auto;
 overflow: hidden;
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
`,[m("layout-scroll-container",`
 overflow-x: hidden;
 box-sizing: border-box;
 height: 100%;
 `),w("absolute-positioned",`
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 `)]),Ko={embedded:Boolean,position:Ie,nativeScrollbar:{type:Boolean,default:!0},scrollbarProps:Object,onScroll:Function,contentClass:String,contentStyle:{type:[String,Object],default:""},hasSider:Boolean,siderPlacement:{type:String,default:"left"}},Ke=te("n-layout");function Ve(e){return L({name:e?"LayoutContent":"Layout",props:Object.assign(Object.assign({},Y.props),Ko),setup(t){const o=M(null),r=M(null),{mergedClsPrefixRef:c,inlineThemeDisabled:i}=ce(t),a=Y("Layout","-layout",jo,ze,t,c);function d(x,S){if(t.nativeScrollbar){const{value:A}=o;A&&(S===void 0?A.scrollTo(x):A.scrollTo(x,S))}else{const{value:A}=r;A&&A.scrollTo(x,S)}}J(Ke,t);let s=0,b=0;const k=x=>{var S;const A=x.target;s=A.scrollLeft,b=A.scrollTop,(S=t.onScroll)===null||S===void 0||S.call(t,x)};Fe(()=>{if(t.nativeScrollbar){const x=o.value;x&&(x.scrollTop=b,x.scrollLeft=s)}});const H={display:"flex",flexWrap:"nowrap",width:"100%",flexDirection:"row"},f={scrollTo:d},N=h(()=>{const{common:{cubicBezierEaseInOut:x},self:S}=a.value;return{"--n-bezier":x,"--n-color":t.embedded?S.colorEmbedded:S.color,"--n-text-color":S.textColor}}),R=i?se("layout",h(()=>t.embedded?"e":""),N,t):void 0;return Object.assign({mergedClsPrefix:c,scrollableElRef:o,scrollbarInstRef:r,hasSiderStyle:H,mergedTheme:a,handleNativeElScroll:k,cssVars:i?void 0:N,themeClass:R?.themeClass,onRender:R?.onRender},f)},render(){var t;const{mergedClsPrefix:o,hasSider:r}=this;(t=this.onRender)===null||t===void 0||t.call(this);const c=r?this.hasSiderStyle:void 0,i=[this.themeClass,e&&`${o}-layout-content`,`${o}-layout`,`${o}-layout--${this.position}-positioned`];return u("div",{class:i,style:this.cssVars},this.nativeScrollbar?u("div",{ref:"scrollableElRef",class:[`${o}-layout-scroll-container`,this.contentClass],style:[this.contentStyle,c],onScroll:this.handleNativeElScroll},this.$slots):u($e,Object.assign({},this.scrollbarProps,{onScroll:this.onScroll,ref:"scrollbarInstRef",theme:this.mergedTheme.peers.Scrollbar,themeOverrides:this.mergedTheme.peerOverrides.Scrollbar,contentClass:this.contentClass,contentStyle:[this.contentStyle,c]}),this.$slots))}})}const _e=Ve(!1),Vo=Ve(!0),Do=m("layout-header",`
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 box-sizing: border-box;
 width: 100%;
 background-color: var(--n-color);
 color: var(--n-text-color);
`,[w("absolute-positioned",`
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 `),w("bordered",`
 border-bottom: solid 1px var(--n-border-color);
 `)]),Uo={position:Ie,inverted:Boolean,bordered:{type:Boolean,default:!1}},Go=L({name:"LayoutHeader",props:Object.assign(Object.assign({},Y.props),Uo),setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:o}=ce(e),r=Y("Layout","-layout-header",Do,ze,e,t),c=h(()=>{const{common:{cubicBezierEaseInOut:a},self:d}=r.value,s={"--n-bezier":a};return e.inverted?(s["--n-color"]=d.headerColorInverted,s["--n-text-color"]=d.textColorInverted,s["--n-border-color"]=d.headerBorderColorInverted):(s["--n-color"]=d.headerColor,s["--n-text-color"]=d.textColor,s["--n-border-color"]=d.headerBorderColor),s}),i=o?se("layout-header",h(()=>e.inverted?"a":"b"),c,e):void 0;return{mergedClsPrefix:t,cssVars:o?void 0:c,themeClass:i?.themeClass,onRender:i?.onRender}},render(){var e;const{mergedClsPrefix:t}=this;return(e=this.onRender)===null||e===void 0||e.call(this),u("div",{class:[`${t}-layout-header`,this.themeClass,this.position&&`${t}-layout-header--${this.position}-positioned`,this.bordered&&`${t}-layout-header--bordered`],style:this.cssVars},this.$slots)}}),Wo=m("layout-sider",`
 flex-shrink: 0;
 box-sizing: border-box;
 position: relative;
 z-index: 1;
 color: var(--n-text-color);
 transition:
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 min-width .3s var(--n-bezier),
 max-width .3s var(--n-bezier),
 transform .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 background-color: var(--n-color);
 display: flex;
 justify-content: flex-end;
`,[w("bordered",[v("border",`
 content: "";
 position: absolute;
 top: 0;
 bottom: 0;
 width: 1px;
 background-color: var(--n-border-color);
 transition: background-color .3s var(--n-bezier);
 `)]),v("left-placement",[w("bordered",[v("border",`
 right: 0;
 `)])]),w("right-placement",`
 justify-content: flex-start;
 `,[w("bordered",[v("border",`
 left: 0;
 `)]),w("collapsed",[m("layout-toggle-button",[m("base-icon",`
 transform: rotate(180deg);
 `)]),m("layout-toggle-bar",[I("&:hover",[v("top",{transform:"rotate(-12deg) scale(1.15) translateY(-2px)"}),v("bottom",{transform:"rotate(12deg) scale(1.15) translateY(2px)"})])])]),m("layout-toggle-button",`
 left: 0;
 transform: translateX(-50%) translateY(-50%);
 `,[m("base-icon",`
 transform: rotate(0);
 `)]),m("layout-toggle-bar",`
 left: -28px;
 transform: rotate(180deg);
 `,[I("&:hover",[v("top",{transform:"rotate(12deg) scale(1.15) translateY(-2px)"}),v("bottom",{transform:"rotate(-12deg) scale(1.15) translateY(2px)"})])])]),w("collapsed",[m("layout-toggle-bar",[I("&:hover",[v("top",{transform:"rotate(-12deg) scale(1.15) translateY(-2px)"}),v("bottom",{transform:"rotate(12deg) scale(1.15) translateY(2px)"})])]),m("layout-toggle-button",[m("base-icon",`
 transform: rotate(0);
 `)])]),m("layout-toggle-button",`
 transition:
 color .3s var(--n-bezier),
 right .3s var(--n-bezier),
 left .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 cursor: pointer;
 width: 24px;
 height: 24px;
 position: absolute;
 top: 50%;
 right: 0;
 border-radius: 50%;
 display: flex;
 align-items: center;
 justify-content: center;
 font-size: 18px;
 color: var(--n-toggle-button-icon-color);
 border: var(--n-toggle-button-border);
 background-color: var(--n-toggle-button-color);
 box-shadow: 0 2px 4px 0px rgba(0, 0, 0, .06);
 transform: translateX(50%) translateY(-50%);
 z-index: 1;
 `,[m("base-icon",`
 transition: transform .3s var(--n-bezier);
 transform: rotate(180deg);
 `)]),m("layout-toggle-bar",`
 cursor: pointer;
 height: 72px;
 width: 32px;
 position: absolute;
 top: calc(50% - 36px);
 right: -28px;
 `,[v("top, bottom",`
 position: absolute;
 width: 4px;
 border-radius: 2px;
 height: 38px;
 left: 14px;
 transition: 
 background-color .3s var(--n-bezier),
 transform .3s var(--n-bezier);
 `),v("bottom",`
 position: absolute;
 top: 34px;
 `),I("&:hover",[v("top",{transform:"rotate(12deg) scale(1.15) translateY(-2px)"}),v("bottom",{transform:"rotate(-12deg) scale(1.15) translateY(2px)"})]),v("top, bottom",{backgroundColor:"var(--n-toggle-bar-color)"}),I("&:hover",[v("top, bottom",{backgroundColor:"var(--n-toggle-bar-color-hover)"})])]),v("border",`
 position: absolute;
 top: 0;
 right: 0;
 bottom: 0;
 width: 1px;
 transition: background-color .3s var(--n-bezier);
 `),m("layout-sider-scroll-container",`
 flex-grow: 1;
 flex-shrink: 0;
 box-sizing: border-box;
 height: 100%;
 opacity: 0;
 transition: opacity .3s var(--n-bezier);
 max-width: 100%;
 `),w("show-content",[m("layout-sider-scroll-container",{opacity:1})]),w("absolute-positioned",`
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 `)]),qo=L({props:{clsPrefix:{type:String,required:!0},onClick:Function},render(){const{clsPrefix:e}=this;return u("div",{onClick:this.onClick,class:`${e}-layout-toggle-bar`},u("div",{class:`${e}-layout-toggle-bar__top`}),u("div",{class:`${e}-layout-toggle-bar__bottom`}))}}),Yo=L({name:"LayoutToggleButton",props:{clsPrefix:{type:String,required:!0},onClick:Function},render(){const{clsPrefix:e}=this;return u("div",{class:`${e}-layout-toggle-button`,onClick:this.onClick},u(Le,{clsPrefix:e},{default:()=>u(zo,null)}))}}),Xo={position:Ie,bordered:Boolean,collapsedWidth:{type:Number,default:48},width:{type:[Number,String],default:272},contentClass:String,contentStyle:{type:[String,Object],default:""},collapseMode:{type:String,default:"transform"},collapsed:{type:Boolean,default:void 0},defaultCollapsed:Boolean,showCollapsedContent:{type:Boolean,default:!0},showTrigger:{type:[Boolean,String],default:!1},nativeScrollbar:{type:Boolean,default:!0},inverted:Boolean,scrollbarProps:Object,triggerClass:String,triggerStyle:[String,Object],collapsedTriggerClass:String,collapsedTriggerStyle:[String,Object],"onUpdate:collapsed":[Function,Array],onUpdateCollapsed:[Function,Array],onAfterEnter:Function,onAfterLeave:Function,onExpand:[Function,Array],onCollapse:[Function,Array],onScroll:Function},Zo=L({name:"LayoutSider",props:Object.assign(Object.assign({},Y.props),Xo),setup(e){const t=q(Ke),o=M(null),r=M(null),c=M(e.defaultCollapsed),i=be(ae(e,"collapsed"),c),a=h(()=>he(i.value?e.collapsedWidth:e.width)),d=h(()=>e.collapseMode!=="transform"?{}:{minWidth:he(e.width)}),s=h(()=>t?t.siderPlacement:"left");function b(T,y){if(e.nativeScrollbar){const{value:z}=o;z&&(y===void 0?z.scrollTo(T):z.scrollTo(T,y))}else{const{value:z}=r;z&&z.scrollTo(T,y)}}function k(){const{"onUpdate:collapsed":T,onUpdateCollapsed:y,onExpand:z,onCollapse:G}=e,{value:D}=i;y&&K(y,!D),T&&K(T,!D),c.value=!D,D?z&&K(z):G&&K(G)}let H=0,f=0;const N=T=>{var y;const z=T.target;H=z.scrollLeft,f=z.scrollTop,(y=e.onScroll)===null||y===void 0||y.call(e,T)};Fe(()=>{if(e.nativeScrollbar){const T=o.value;T&&(T.scrollTop=f,T.scrollLeft=H)}}),J(je,{collapsedRef:i,collapseModeRef:ae(e,"collapseMode")});const{mergedClsPrefixRef:R,inlineThemeDisabled:x}=ce(e),S=Y("Layout","-layout-sider",Wo,ze,e,R);function A(T){var y,z;T.propertyName==="max-width"&&(i.value?(y=e.onAfterLeave)===null||y===void 0||y.call(e):(z=e.onAfterEnter)===null||z===void 0||z.call(e))}const j={scrollTo:b},V=h(()=>{const{common:{cubicBezierEaseInOut:T},self:y}=S.value,{siderToggleButtonColor:z,siderToggleButtonBorder:G,siderToggleBarColor:D,siderToggleBarColorHover:de}=y,F={"--n-bezier":T,"--n-toggle-button-color":z,"--n-toggle-button-border":G,"--n-toggle-bar-color":D,"--n-toggle-bar-color-hover":de};return e.inverted?(F["--n-color"]=y.siderColorInverted,F["--n-text-color"]=y.textColorInverted,F["--n-border-color"]=y.siderBorderColorInverted,F["--n-toggle-button-icon-color"]=y.siderToggleButtonIconColorInverted,F.__invertScrollbar=y.__invertScrollbar):(F["--n-color"]=y.siderColor,F["--n-text-color"]=y.textColor,F["--n-border-color"]=y.siderBorderColor,F["--n-toggle-button-icon-color"]=y.siderToggleButtonIconColor),F}),$=x?se("layout-sider",h(()=>e.inverted?"a":"b"),V,e):void 0;return Object.assign({scrollableElRef:o,scrollbarInstRef:r,mergedClsPrefix:R,mergedTheme:S,styleMaxWidth:a,mergedCollapsed:i,scrollContainerStyle:d,siderPlacement:s,handleNativeElScroll:N,handleTransitionend:A,handleTriggerClick:k,inlineThemeDisabled:x,cssVars:V,themeClass:$?.themeClass,onRender:$?.onRender},j)},render(){var e;const{mergedClsPrefix:t,mergedCollapsed:o,showTrigger:r}=this;return(e=this.onRender)===null||e===void 0||e.call(this),u("aside",{class:[`${t}-layout-sider`,this.themeClass,`${t}-layout-sider--${this.position}-positioned`,`${t}-layout-sider--${this.siderPlacement}-placement`,this.bordered&&`${t}-layout-sider--bordered`,o&&`${t}-layout-sider--collapsed`,(!o||this.showCollapsedContent)&&`${t}-layout-sider--show-content`],onTransitionend:this.handleTransitionend,style:[this.inlineThemeDisabled?void 0:this.cssVars,{maxWidth:this.styleMaxWidth,width:he(this.width)}]},this.nativeScrollbar?u("div",{class:[`${t}-layout-sider-scroll-container`,this.contentClass],onScroll:this.handleNativeElScroll,style:[this.scrollContainerStyle,{overflow:"auto"},this.contentStyle],ref:"scrollableElRef"},this.$slots):u($e,Object.assign({},this.scrollbarProps,{onScroll:this.onScroll,ref:"scrollbarInstRef",style:this.scrollContainerStyle,contentStyle:this.contentStyle,contentClass:this.contentClass,theme:this.mergedTheme.peers.Scrollbar,themeOverrides:this.mergedTheme.peerOverrides.Scrollbar,builtinThemeOverrides:this.inverted&&this.cssVars.__invertScrollbar==="true"?{colorHover:"rgba(255, 255, 255, .4)",color:"rgba(255, 255, 255, .3)"}:void 0}),this.$slots),r?r==="bar"?u(qo,{clsPrefix:t,class:o?this.collapsedTriggerClass:this.triggerClass,style:o?this.collapsedTriggerStyle:this.triggerStyle,onClick:this.handleTriggerClick}):u(Yo,{clsPrefix:t,class:o?this.collapsedTriggerClass:this.triggerClass,style:o?this.collapsedTriggerStyle:this.triggerStyle,onClick:this.handleTriggerClick}):null,this.bordered?u("div",{class:`${t}-layout-sider__border`}):null)}}),re=te("n-menu"),we=te("n-submenu"),Se=te("n-menu-item-group"),Be=[I("&::before","background-color: var(--n-item-color-hover);"),v("arrow",`
 color: var(--n-arrow-color-hover);
 `),v("icon",`
 color: var(--n-item-icon-color-hover);
 `),m("menu-item-content-header",`
 color: var(--n-item-text-color-hover);
 `,[I("a",`
 color: var(--n-item-text-color-hover);
 `),v("extra",`
 color: var(--n-item-text-color-hover);
 `)])],Oe=[v("icon",`
 color: var(--n-item-icon-color-hover-horizontal);
 `),m("menu-item-content-header",`
 color: var(--n-item-text-color-hover-horizontal);
 `,[I("a",`
 color: var(--n-item-text-color-hover-horizontal);
 `),v("extra",`
 color: var(--n-item-text-color-hover-horizontal);
 `)])],Jo=I([m("menu",`
 background-color: var(--n-color);
 color: var(--n-item-text-color);
 overflow: hidden;
 transition: background-color .3s var(--n-bezier);
 box-sizing: border-box;
 font-size: var(--n-font-size);
 padding-bottom: 6px;
 `,[w("horizontal",`
 max-width: 100%;
 width: 100%;
 display: flex;
 overflow: hidden;
 padding-bottom: 0;
 `,[m("submenu","margin: 0;"),m("menu-item","margin: 0;"),m("menu-item-content",`
 padding: 0 20px;
 border-bottom: 2px solid #0000;
 `,[I("&::before","display: none;"),w("selected","border-bottom: 2px solid var(--n-border-color-horizontal)")]),m("menu-item-content",[w("selected",[v("icon","color: var(--n-item-icon-color-active-horizontal);"),m("menu-item-content-header",`
 color: var(--n-item-text-color-active-horizontal);
 `,[I("a","color: var(--n-item-text-color-active-horizontal);"),v("extra","color: var(--n-item-text-color-active-horizontal);")])]),w("child-active",`
 border-bottom: 2px solid var(--n-border-color-horizontal);
 `,[m("menu-item-content-header",`
 color: var(--n-item-text-color-child-active-horizontal);
 `,[I("a",`
 color: var(--n-item-text-color-child-active-horizontal);
 `),v("extra",`
 color: var(--n-item-text-color-child-active-horizontal);
 `)]),v("icon",`
 color: var(--n-item-icon-color-child-active-horizontal);
 `)]),ee("disabled",[ee("selected, child-active",[I("&:focus-within",Oe)]),w("selected",[X(null,[v("icon","color: var(--n-item-icon-color-active-hover-horizontal);"),m("menu-item-content-header",`
 color: var(--n-item-text-color-active-hover-horizontal);
 `,[I("a","color: var(--n-item-text-color-active-hover-horizontal);"),v("extra","color: var(--n-item-text-color-active-hover-horizontal);")])])]),w("child-active",[X(null,[v("icon","color: var(--n-item-icon-color-child-active-hover-horizontal);"),m("menu-item-content-header",`
 color: var(--n-item-text-color-child-active-hover-horizontal);
 `,[I("a","color: var(--n-item-text-color-child-active-hover-horizontal);"),v("extra","color: var(--n-item-text-color-child-active-hover-horizontal);")])])]),X("border-bottom: 2px solid var(--n-border-color-horizontal);",Oe)]),m("menu-item-content-header",[I("a","color: var(--n-item-text-color-horizontal);")])])]),ee("responsive",[m("menu-item-content-header",`
 overflow: hidden;
 text-overflow: ellipsis;
 `)]),w("collapsed",[m("menu-item-content",[w("selected",[I("&::before",`
 background-color: var(--n-item-color-active-collapsed) !important;
 `)]),m("menu-item-content-header","opacity: 0;"),v("arrow","opacity: 0;"),v("icon","color: var(--n-item-icon-color-collapsed);")])]),m("menu-item",`
 height: var(--n-item-height);
 margin-top: 6px;
 position: relative;
 `),m("menu-item-content",`
 box-sizing: border-box;
 line-height: 1.75;
 height: 100%;
 display: grid;
 grid-template-areas: "icon content arrow";
 grid-template-columns: auto 1fr auto;
 align-items: center;
 cursor: pointer;
 position: relative;
 padding-right: 18px;
 transition:
 background-color .3s var(--n-bezier),
 padding-left .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[I("> *","z-index: 1;"),I("&::before",`
 z-index: auto;
 content: "";
 background-color: #0000;
 position: absolute;
 left: 8px;
 right: 8px;
 top: 0;
 bottom: 0;
 pointer-events: none;
 border-radius: var(--n-border-radius);
 transition: background-color .3s var(--n-bezier);
 `),w("disabled",`
 opacity: .45;
 cursor: not-allowed;
 `),w("collapsed",[v("arrow","transform: rotate(0);")]),w("selected",[I("&::before","background-color: var(--n-item-color-active);"),v("arrow","color: var(--n-arrow-color-active);"),v("icon","color: var(--n-item-icon-color-active);"),m("menu-item-content-header",`
 color: var(--n-item-text-color-active);
 `,[I("a","color: var(--n-item-text-color-active);"),v("extra","color: var(--n-item-text-color-active);")])]),w("child-active",[m("menu-item-content-header",`
 color: var(--n-item-text-color-child-active);
 `,[I("a",`
 color: var(--n-item-text-color-child-active);
 `),v("extra",`
 color: var(--n-item-text-color-child-active);
 `)]),v("arrow",`
 color: var(--n-arrow-color-child-active);
 `),v("icon",`
 color: var(--n-item-icon-color-child-active);
 `)]),ee("disabled",[ee("selected, child-active",[I("&:focus-within",Be)]),w("selected",[X(null,[v("arrow","color: var(--n-arrow-color-active-hover);"),v("icon","color: var(--n-item-icon-color-active-hover);"),m("menu-item-content-header",`
 color: var(--n-item-text-color-active-hover);
 `,[I("a","color: var(--n-item-text-color-active-hover);"),v("extra","color: var(--n-item-text-color-active-hover);")])])]),w("child-active",[X(null,[v("arrow","color: var(--n-arrow-color-child-active-hover);"),v("icon","color: var(--n-item-icon-color-child-active-hover);"),m("menu-item-content-header",`
 color: var(--n-item-text-color-child-active-hover);
 `,[I("a","color: var(--n-item-text-color-child-active-hover);"),v("extra","color: var(--n-item-text-color-child-active-hover);")])])]),w("selected",[X(null,[I("&::before","background-color: var(--n-item-color-active-hover);")])]),X(null,Be)]),v("icon",`
 grid-area: icon;
 color: var(--n-item-icon-color);
 transition:
 color .3s var(--n-bezier),
 font-size .3s var(--n-bezier),
 margin-right .3s var(--n-bezier);
 box-sizing: content-box;
 display: inline-flex;
 align-items: center;
 justify-content: center;
 `),v("arrow",`
 grid-area: arrow;
 font-size: 16px;
 color: var(--n-arrow-color);
 transform: rotate(180deg);
 opacity: 1;
 transition:
 color .3s var(--n-bezier),
 transform 0.2s var(--n-bezier),
 opacity 0.2s var(--n-bezier);
 `),m("menu-item-content-header",`
 grid-area: content;
 transition:
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier);
 opacity: 1;
 white-space: nowrap;
 color: var(--n-item-text-color);
 `,[I("a",`
 outline: none;
 text-decoration: none;
 transition: color .3s var(--n-bezier);
 color: var(--n-item-text-color);
 `,[I("&::before",`
 content: "";
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 `)]),v("extra",`
 font-size: .93em;
 color: var(--n-group-text-color);
 transition: color .3s var(--n-bezier);
 `)])]),m("submenu",`
 cursor: pointer;
 position: relative;
 margin-top: 6px;
 `,[m("menu-item-content",`
 height: var(--n-item-height);
 `),m("submenu-children",`
 overflow: hidden;
 padding: 0;
 `,[no({duration:".2s"})])]),m("menu-item-group",[m("menu-item-group-title",`
 margin-top: 6px;
 color: var(--n-group-text-color);
 cursor: default;
 font-size: .93em;
 height: 36px;
 display: flex;
 align-items: center;
 transition:
 padding-left .3s var(--n-bezier),
 color .3s var(--n-bezier);
 `)])]),m("menu-tooltip",[I("a",`
 color: inherit;
 text-decoration: none;
 `)]),m("menu-divider",`
 transition: background-color .3s var(--n-bezier);
 background-color: var(--n-divider-color);
 height: 1px;
 margin: 6px 18px;
 `)]);function X(e,t){return[w("hover",e,t),I("&:hover",e,t)]}const De=L({name:"MenuOptionContent",props:{collapsed:Boolean,disabled:Boolean,title:[String,Function],icon:Function,extra:[String,Function],showArrow:Boolean,childActive:Boolean,hover:Boolean,paddingLeft:Number,selected:Boolean,maxIconSize:{type:Number,required:!0},activeIconSize:{type:Number,required:!0},iconMarginRight:{type:Number,required:!0},clsPrefix:{type:String,required:!0},onClick:Function,tmNode:{type:Object,required:!0},isEllipsisPlaceholder:Boolean},setup(e){const{props:t}=q(re);return{menuProps:t,style:h(()=>{const{paddingLeft:o}=e;return{paddingLeft:o&&`${o}px`}}),iconStyle:h(()=>{const{maxIconSize:o,activeIconSize:r,iconMarginRight:c}=e;return{width:`${o}px`,height:`${o}px`,fontSize:`${r}px`,marginRight:`${c}px`}})}},render(){const{clsPrefix:e,tmNode:t,menuProps:{renderIcon:o,renderLabel:r,renderExtra:c,expandIcon:i}}=this,a=o?o(t.rawNode):Q(this.icon);return u("div",{onClick:d=>{var s;(s=this.onClick)===null||s===void 0||s.call(this,d)},role:"none",class:[`${e}-menu-item-content`,{[`${e}-menu-item-content--selected`]:this.selected,[`${e}-menu-item-content--collapsed`]:this.collapsed,[`${e}-menu-item-content--child-active`]:this.childActive,[`${e}-menu-item-content--disabled`]:this.disabled,[`${e}-menu-item-content--hover`]:this.hover}],style:this.style},a&&u("div",{class:`${e}-menu-item-content__icon`,style:this.iconStyle,role:"none"},[a]),u("div",{class:`${e}-menu-item-content-header`,role:"none"},this.isEllipsisPlaceholder?this.title:r?r(t.rawNode):Q(this.title),this.extra||c?u("span",{class:`${e}-menu-item-content-header__extra`}," ",c?c(t.rawNode):Q(this.extra)):null),this.showArrow?u(Le,{ariaHidden:!0,class:`${e}-menu-item-content__arrow`,clsPrefix:e},{default:()=>i?i(t.rawNode):u(Eo,null)}):null)}}),le=8;function Ae(e){const t=q(re),{props:o,mergedCollapsedRef:r}=t,c=q(we,null),i=q(Se,null),a=h(()=>o.mode==="horizontal"),d=h(()=>a.value?o.dropdownPlacement:"tmNodes"in e?"right-start":"right"),s=h(()=>{var f;return Math.max((f=o.collapsedIconSize)!==null&&f!==void 0?f:o.iconSize,o.iconSize)}),b=h(()=>{var f;return!a.value&&e.root&&r.value&&(f=o.collapsedIconSize)!==null&&f!==void 0?f:o.iconSize}),k=h(()=>{if(a.value)return;const{collapsedWidth:f,indent:N,rootIndent:R}=o,{root:x,isGroup:S}=e,A=R===void 0?N:R;return x?r.value?f/2-s.value/2:A:i&&typeof i.paddingLeftRef.value=="number"?N/2+i.paddingLeftRef.value:c&&typeof c.paddingLeftRef.value=="number"?(S?N/2:N)+c.paddingLeftRef.value:0}),H=h(()=>{const{collapsedWidth:f,indent:N,rootIndent:R}=o,{value:x}=s,{root:S}=e;return a.value||!S||!r.value?le:(R===void 0?N:R)+x+le-(f+x)/2});return{dropdownPlacement:d,activeIconSize:b,maxIconSize:s,paddingLeft:k,iconMarginRight:H,NMenu:t,NSubmenu:c}}const He={internalKey:{type:[String,Number],required:!0},root:Boolean,isGroup:Boolean,level:{type:Number,required:!0},title:[String,Function],extra:[String,Function]},Qo=L({name:"MenuDivider",setup(){const e=q(re),{mergedClsPrefixRef:t,isHorizontalRef:o}=e;return()=>o.value?null:u("div",{class:`${t.value}-menu-divider`})}}),Ue=Object.assign(Object.assign({},He),{tmNode:{type:Object,required:!0},disabled:Boolean,icon:Function,onClick:Function}),et=ye(Ue),ot=L({name:"MenuOption",props:Ue,setup(e){const t=Ae(e),{NSubmenu:o,NMenu:r}=t,{props:c,mergedClsPrefixRef:i,mergedCollapsedRef:a}=r,d=o?o.mergedDisabledRef:{value:!1},s=h(()=>d.value||e.disabled);function b(H){const{onClick:f}=e;f&&f(H)}function k(H){s.value||(r.doSelect(e.internalKey,e.tmNode.rawNode),b(H))}return{mergedClsPrefix:i,dropdownPlacement:t.dropdownPlacement,paddingLeft:t.paddingLeft,iconMarginRight:t.iconMarginRight,maxIconSize:t.maxIconSize,activeIconSize:t.activeIconSize,mergedTheme:r.mergedThemeRef,menuProps:c,dropdownEnabled:ge(()=>e.root&&a.value&&c.mode!=="horizontal"&&!s.value),selected:ge(()=>r.mergedValueRef.value===e.internalKey),mergedDisabled:s,handleClick:k}},render(){const{mergedClsPrefix:e,mergedTheme:t,tmNode:o,menuProps:{renderLabel:r,nodeProps:c}}=this,i=c?.(o.rawNode);return u("div",Object.assign({},i,{role:"menuitem",class:[`${e}-menu-item`,i?.class]}),u(Ro,{theme:t.peers.Tooltip,themeOverrides:t.peerOverrides.Tooltip,trigger:"hover",placement:this.dropdownPlacement,disabled:!this.dropdownEnabled||this.title===void 0,internalExtraClass:["menu-tooltip"]},{default:()=>r?r(o.rawNode):Q(this.title),trigger:()=>u(De,{tmNode:o,clsPrefix:e,paddingLeft:this.paddingLeft,iconMarginRight:this.iconMarginRight,maxIconSize:this.maxIconSize,activeIconSize:this.activeIconSize,selected:this.selected,title:this.title,extra:this.extra,disabled:this.mergedDisabled,icon:this.icon,onClick:this.handleClick})}))}}),Ge=Object.assign(Object.assign({},He),{tmNode:{type:Object,required:!0},tmNodes:{type:Array,required:!0}}),tt=ye(Ge),rt=L({name:"MenuOptionGroup",props:Ge,setup(e){J(we,null);const t=Ae(e);J(Se,{paddingLeftRef:t.paddingLeft});const{mergedClsPrefixRef:o,props:r}=q(re);return function(){const{value:c}=o,i=t.paddingLeft.value,{nodeProps:a}=r,d=a?.(e.tmNode.rawNode);return u("div",{class:`${c}-menu-item-group`,role:"group"},u("div",Object.assign({},d,{class:[`${c}-menu-item-group-title`,d?.class],style:[d?.style||"",i!==void 0?`padding-left: ${i}px;`:""]}),Q(e.title),e.extra?u(lo,null," ",Q(e.extra)):null),u("div",null,e.tmNodes.map(s=>Re(s,r))))}}});function Ce(e){return e.type==="divider"||e.type==="render"}function nt(e){return e.type==="divider"}function Re(e,t){const{rawNode:o}=e,{show:r}=o;if(r===!1)return null;if(Ce(o))return nt(o)?u(Qo,Object.assign({key:e.key},o.props)):null;const{labelField:c}=t,{key:i,level:a,isGroup:d}=e,s=Object.assign(Object.assign({},o),{title:o.title||o[c],extra:o.titleExtra||o.extra,key:i,internalKey:i,level:a,root:a===0,isGroup:d});return e.children?e.isGroup?u(rt,pe(s,tt,{tmNode:e,tmNodes:e.children,key:i})):u(xe,pe(s,lt,{key:i,rawNodes:o[t.childrenField],tmNodes:e.children,tmNode:e})):u(ot,pe(s,et,{key:i,tmNode:e}))}const We=Object.assign(Object.assign({},He),{rawNodes:{type:Array,default:()=>[]},tmNodes:{type:Array,default:()=>[]},tmNode:{type:Object,required:!0},disabled:Boolean,icon:Function,onClick:Function,domId:String,virtualChildActive:{type:Boolean,default:void 0},isEllipsisPlaceholder:Boolean}),lt=ye(We),xe=L({name:"Submenu",props:We,setup(e){const t=Ae(e),{NMenu:o,NSubmenu:r}=t,{props:c,mergedCollapsedRef:i,mergedThemeRef:a}=o,d=h(()=>{const{disabled:f}=e;return r?.mergedDisabledRef.value||c.disabled?!0:f}),s=M(!1);J(we,{paddingLeftRef:t.paddingLeft,mergedDisabledRef:d}),J(Se,null);function b(){const{onClick:f}=e;f&&f()}function k(){d.value||(i.value||o.toggleExpand(e.internalKey),b())}function H(f){s.value=f}return{menuProps:c,mergedTheme:a,doSelect:o.doSelect,inverted:o.invertedRef,isHorizontal:o.isHorizontalRef,mergedClsPrefix:o.mergedClsPrefixRef,maxIconSize:t.maxIconSize,activeIconSize:t.activeIconSize,iconMarginRight:t.iconMarginRight,dropdownPlacement:t.dropdownPlacement,dropdownShow:s,paddingLeft:t.paddingLeft,mergedDisabled:d,mergedValue:o.mergedValueRef,childActive:ge(()=>{var f;return(f=e.virtualChildActive)!==null&&f!==void 0?f:o.activePathRef.value.includes(e.internalKey)}),collapsed:h(()=>c.mode==="horizontal"?!1:i.value?!0:!o.mergedExpandedKeysRef.value.includes(e.internalKey)),dropdownEnabled:h(()=>!d.value&&(c.mode==="horizontal"||i.value)),handlePopoverShowChange:H,handleClick:k}},render(){var e;const{mergedClsPrefix:t,menuProps:{renderIcon:o,renderLabel:r}}=this,c=()=>{const{isHorizontal:a,paddingLeft:d,collapsed:s,mergedDisabled:b,maxIconSize:k,activeIconSize:H,title:f,childActive:N,icon:R,handleClick:x,menuProps:{nodeProps:S},dropdownShow:A,iconMarginRight:j,tmNode:V,mergedClsPrefix:$,isEllipsisPlaceholder:T,extra:y}=this,z=S?.(V.rawNode);return u("div",Object.assign({},z,{class:[`${$}-menu-item`,z?.class],role:"menuitem"}),u(De,{tmNode:V,paddingLeft:d,collapsed:s,disabled:b,iconMarginRight:j,maxIconSize:k,activeIconSize:H,title:f,extra:y,showArrow:!a,childActive:N,clsPrefix:$,icon:R,hover:A,onClick:x,isEllipsisPlaceholder:T}))},i=()=>u(io,null,{default:()=>{const{tmNodes:a,collapsed:d}=this;return d?null:u("div",{class:`${t}-submenu-children`,role:"menu"},a.map(s=>Re(s,this.menuProps)))}});return this.root?u(Po,Object.assign({size:"large",trigger:"hover"},(e=this.menuProps)===null||e===void 0?void 0:e.dropdownProps,{themeOverrides:this.mergedTheme.peerOverrides.Dropdown,theme:this.mergedTheme.peers.Dropdown,builtinThemeOverrides:{fontSizeLarge:"14px",optionIconSizeLarge:"18px"},value:this.mergedValue,disabled:!this.dropdownEnabled,placement:this.dropdownPlacement,keyField:this.menuProps.keyField,labelField:this.menuProps.labelField,childrenField:this.menuProps.childrenField,onUpdateShow:this.handlePopoverShowChange,options:this.rawNodes,onSelect:this.doSelect,inverted:this.inverted,renderIcon:o,renderLabel:r}),{default:()=>u("div",{class:`${t}-submenu`,role:"menu","aria-expanded":!this.collapsed,id:this.domId},c(),this.isHorizontal?null:i())}):u("div",{class:`${t}-submenu`,role:"menu","aria-expanded":!this.collapsed,id:this.domId},c(),i())}}),it=Object.assign(Object.assign({},Y.props),{options:{type:Array,default:()=>[]},collapsed:{type:Boolean,default:void 0},collapsedWidth:{type:Number,default:48},iconSize:{type:Number,default:20},collapsedIconSize:{type:Number,default:24},rootIndent:Number,indent:{type:Number,default:32},labelField:{type:String,default:"label"},keyField:{type:String,default:"key"},childrenField:{type:String,default:"children"},disabledField:{type:String,default:"disabled"},defaultExpandAll:Boolean,defaultExpandedKeys:Array,expandedKeys:Array,value:[String,Number],defaultValue:{type:[String,Number],default:null},mode:{type:String,default:"vertical"},watchProps:{type:Array,default:void 0},disabled:Boolean,show:{type:Boolean,default:!0},inverted:Boolean,"onUpdate:expandedKeys":[Function,Array],onUpdateExpandedKeys:[Function,Array],onUpdateValue:[Function,Array],"onUpdate:value":[Function,Array],expandIcon:Function,renderIcon:Function,renderLabel:Function,renderExtra:Function,dropdownProps:Object,accordion:Boolean,nodeProps:Function,dropdownPlacement:{type:String,default:"bottom"},responsive:Boolean,items:Array,onOpenNamesChange:[Function,Array],onSelect:[Function,Array],onExpandedNamesChange:[Function,Array],expandedNames:Array,defaultExpandedNames:Array}),at=L({name:"Menu",inheritAttrs:!1,props:it,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:o}=ce(e),r=Y("Menu","-menu",Jo,Fo,e,t),c=q(je,null),i=h(()=>{var p;const{collapsed:C}=e;if(C!==void 0)return C;if(c){const{collapseModeRef:n,collapsedRef:g}=c;if(n.value==="width")return(p=g.value)!==null&&p!==void 0?p:!1}return!1}),a=h(()=>{const{keyField:p,childrenField:C,disabledField:n}=e;return fe(e.items||e.options,{getIgnored(g){return Ce(g)},getChildren(g){return g[C]},getDisabled(g){return g[n]},getKey(g){var P;return(P=g[p])!==null&&P!==void 0?P:g.name}})}),d=h(()=>new Set(a.value.treeNodes.map(p=>p.key))),{watchProps:s}=e,b=M(null);s?.includes("defaultValue")?Ne(()=>{b.value=e.defaultValue}):b.value=e.defaultValue;const k=ae(e,"value"),H=be(k,b),f=M([]),N=()=>{f.value=e.defaultExpandAll?a.value.getNonLeafKeys():e.defaultExpandedNames||e.defaultExpandedKeys||a.value.getPath(H.value,{includeSelf:!1}).keyPath};s?.includes("defaultExpandedKeys")?Ne(N):N();const R=To(e,["expandedNames","expandedKeys"]),x=be(R,f),S=h(()=>a.value.treeNodes),A=h(()=>a.value.getPath(H.value).keyPath);J(re,{props:e,mergedCollapsedRef:i,mergedThemeRef:r,mergedValueRef:H,mergedExpandedKeysRef:x,activePathRef:A,mergedClsPrefixRef:t,isHorizontalRef:h(()=>e.mode==="horizontal"),invertedRef:ae(e,"inverted"),doSelect:j,toggleExpand:$});function j(p,C){const{"onUpdate:value":n,onUpdateValue:g,onSelect:P}=e;g&&K(g,p,C),n&&K(n,p,C),P&&K(P,p,C),b.value=p}function V(p){const{"onUpdate:expandedKeys":C,onUpdateExpandedKeys:n,onExpandedNamesChange:g,onOpenNamesChange:P}=e;C&&K(C,p),n&&K(n,p),g&&K(g,p),P&&K(P,p),f.value=p}function $(p){const C=Array.from(x.value),n=C.findIndex(g=>g===p);if(~n)C.splice(n,1);else{if(e.accordion&&d.value.has(p)){const g=C.findIndex(P=>d.value.has(P));g>-1&&C.splice(g,1)}C.push(p)}V(C)}const T=p=>{const C=a.value.getPath(p??H.value,{includeSelf:!1}).keyPath;if(!C.length)return;const n=Array.from(x.value),g=new Set([...n,...C]);e.accordion&&d.value.forEach(P=>{g.has(P)&&!C.includes(P)&&g.delete(P)}),V(Array.from(g))},y=h(()=>{const{inverted:p}=e,{common:{cubicBezierEaseInOut:C},self:n}=r.value,{borderRadius:g,borderColorHorizontal:P,fontSize:oo,itemHeight:to,dividerColor:ro}=n,l={"--n-divider-color":ro,"--n-bezier":C,"--n-font-size":oo,"--n-border-color-horizontal":P,"--n-border-radius":g,"--n-item-height":to};return p?(l["--n-group-text-color"]=n.groupTextColorInverted,l["--n-color"]=n.colorInverted,l["--n-item-text-color"]=n.itemTextColorInverted,l["--n-item-text-color-hover"]=n.itemTextColorHoverInverted,l["--n-item-text-color-active"]=n.itemTextColorActiveInverted,l["--n-item-text-color-child-active"]=n.itemTextColorChildActiveInverted,l["--n-item-text-color-child-active-hover"]=n.itemTextColorChildActiveInverted,l["--n-item-text-color-active-hover"]=n.itemTextColorActiveHoverInverted,l["--n-item-icon-color"]=n.itemIconColorInverted,l["--n-item-icon-color-hover"]=n.itemIconColorHoverInverted,l["--n-item-icon-color-active"]=n.itemIconColorActiveInverted,l["--n-item-icon-color-active-hover"]=n.itemIconColorActiveHoverInverted,l["--n-item-icon-color-child-active"]=n.itemIconColorChildActiveInverted,l["--n-item-icon-color-child-active-hover"]=n.itemIconColorChildActiveHoverInverted,l["--n-item-icon-color-collapsed"]=n.itemIconColorCollapsedInverted,l["--n-item-text-color-horizontal"]=n.itemTextColorHorizontalInverted,l["--n-item-text-color-hover-horizontal"]=n.itemTextColorHoverHorizontalInverted,l["--n-item-text-color-active-horizontal"]=n.itemTextColorActiveHorizontalInverted,l["--n-item-text-color-child-active-horizontal"]=n.itemTextColorChildActiveHorizontalInverted,l["--n-item-text-color-child-active-hover-horizontal"]=n.itemTextColorChildActiveHoverHorizontalInverted,l["--n-item-text-color-active-hover-horizontal"]=n.itemTextColorActiveHoverHorizontalInverted,l["--n-item-icon-color-horizontal"]=n.itemIconColorHorizontalInverted,l["--n-item-icon-color-hover-horizontal"]=n.itemIconColorHoverHorizontalInverted,l["--n-item-icon-color-active-horizontal"]=n.itemIconColorActiveHorizontalInverted,l["--n-item-icon-color-active-hover-horizontal"]=n.itemIconColorActiveHoverHorizontalInverted,l["--n-item-icon-color-child-active-horizontal"]=n.itemIconColorChildActiveHorizontalInverted,l["--n-item-icon-color-child-active-hover-horizontal"]=n.itemIconColorChildActiveHoverHorizontalInverted,l["--n-arrow-color"]=n.arrowColorInverted,l["--n-arrow-color-hover"]=n.arrowColorHoverInverted,l["--n-arrow-color-active"]=n.arrowColorActiveInverted,l["--n-arrow-color-active-hover"]=n.arrowColorActiveHoverInverted,l["--n-arrow-color-child-active"]=n.arrowColorChildActiveInverted,l["--n-arrow-color-child-active-hover"]=n.arrowColorChildActiveHoverInverted,l["--n-item-color-hover"]=n.itemColorHoverInverted,l["--n-item-color-active"]=n.itemColorActiveInverted,l["--n-item-color-active-hover"]=n.itemColorActiveHoverInverted,l["--n-item-color-active-collapsed"]=n.itemColorActiveCollapsedInverted):(l["--n-group-text-color"]=n.groupTextColor,l["--n-color"]=n.color,l["--n-item-text-color"]=n.itemTextColor,l["--n-item-text-color-hover"]=n.itemTextColorHover,l["--n-item-text-color-active"]=n.itemTextColorActive,l["--n-item-text-color-child-active"]=n.itemTextColorChildActive,l["--n-item-text-color-child-active-hover"]=n.itemTextColorChildActiveHover,l["--n-item-text-color-active-hover"]=n.itemTextColorActiveHover,l["--n-item-icon-color"]=n.itemIconColor,l["--n-item-icon-color-hover"]=n.itemIconColorHover,l["--n-item-icon-color-active"]=n.itemIconColorActive,l["--n-item-icon-color-active-hover"]=n.itemIconColorActiveHover,l["--n-item-icon-color-child-active"]=n.itemIconColorChildActive,l["--n-item-icon-color-child-active-hover"]=n.itemIconColorChildActiveHover,l["--n-item-icon-color-collapsed"]=n.itemIconColorCollapsed,l["--n-item-text-color-horizontal"]=n.itemTextColorHorizontal,l["--n-item-text-color-hover-horizontal"]=n.itemTextColorHoverHorizontal,l["--n-item-text-color-active-horizontal"]=n.itemTextColorActiveHorizontal,l["--n-item-text-color-child-active-horizontal"]=n.itemTextColorChildActiveHorizontal,l["--n-item-text-color-child-active-hover-horizontal"]=n.itemTextColorChildActiveHoverHorizontal,l["--n-item-text-color-active-hover-horizontal"]=n.itemTextColorActiveHoverHorizontal,l["--n-item-icon-color-horizontal"]=n.itemIconColorHorizontal,l["--n-item-icon-color-hover-horizontal"]=n.itemIconColorHoverHorizontal,l["--n-item-icon-color-active-horizontal"]=n.itemIconColorActiveHorizontal,l["--n-item-icon-color-active-hover-horizontal"]=n.itemIconColorActiveHoverHorizontal,l["--n-item-icon-color-child-active-horizontal"]=n.itemIconColorChildActiveHorizontal,l["--n-item-icon-color-child-active-hover-horizontal"]=n.itemIconColorChildActiveHoverHorizontal,l["--n-arrow-color"]=n.arrowColor,l["--n-arrow-color-hover"]=n.arrowColorHover,l["--n-arrow-color-active"]=n.arrowColorActive,l["--n-arrow-color-active-hover"]=n.arrowColorActiveHover,l["--n-arrow-color-child-active"]=n.arrowColorChildActive,l["--n-arrow-color-child-active-hover"]=n.arrowColorChildActiveHover,l["--n-item-color-hover"]=n.itemColorHover,l["--n-item-color-active"]=n.itemColorActive,l["--n-item-color-active-hover"]=n.itemColorActiveHover,l["--n-item-color-active-collapsed"]=n.itemColorActiveCollapsed),l}),z=o?se("menu",h(()=>e.inverted?"a":"b"),y,e):void 0,G=ao(),D=M(null),de=M(null);let F=!0;const Pe=()=>{var p;F?F=!1:(p=D.value)===null||p===void 0||p.sync({showAllItemsBeforeCalculate:!0})};function qe(){return document.getElementById(G)}const ne=M(-1);function Ye(p){ne.value=e.options.length-p}function Xe(p){p||(ne.value=-1)}const Ze=h(()=>{const p=ne.value;return{children:p===-1?[]:e.options.slice(p)}}),Je=h(()=>{const{childrenField:p,disabledField:C,keyField:n}=e;return fe([Ze.value],{getIgnored(g){return Ce(g)},getChildren(g){return g[p]},getDisabled(g){return g[C]},getKey(g){var P;return(P=g[n])!==null&&P!==void 0?P:g.name}})}),Qe=h(()=>fe([{}]).treeNodes[0]);function eo(){var p;if(ne.value===-1)return u(xe,{root:!0,level:0,key:"__ellpisisGroupPlaceholder__",internalKey:"__ellpisisGroupPlaceholder__",title:"···",tmNode:Qe.value,domId:G,isEllipsisPlaceholder:!0});const C=Je.value.treeNodes[0],n=A.value,g=!!(!((p=C.children)===null||p===void 0)&&p.some(P=>n.includes(P.key)));return u(xe,{level:0,root:!0,key:"__ellpisisGroup__",internalKey:"__ellpisisGroup__",title:"···",virtualChildActive:g,tmNode:C,domId:G,rawNodes:C.rawNode.children||[],tmNodes:C.children||[],isEllipsisPlaceholder:!0})}return{mergedClsPrefix:t,controlledExpandedKeys:R,uncontrolledExpanededKeys:f,mergedExpandedKeys:x,uncontrolledValue:b,mergedValue:H,activePath:A,tmNodes:S,mergedTheme:r,mergedCollapsed:i,cssVars:o?void 0:y,themeClass:z?.themeClass,overflowRef:D,counterRef:de,updateCounter:()=>{},onResize:Pe,onUpdateOverflow:Xe,onUpdateCount:Ye,renderCounter:eo,getCounter:qe,onRender:z?.onRender,showOption:T,deriveResponsiveState:Pe}},render(){const{mergedClsPrefix:e,mode:t,themeClass:o,onRender:r}=this;r?.();const c=()=>this.tmNodes.map(s=>Re(s,this.$props)),a=t==="horizontal"&&this.responsive,d=()=>u("div",co(this.$attrs,{role:t==="horizontal"?"menubar":"menu",class:[`${e}-menu`,o,`${e}-menu--${t}`,a&&`${e}-menu--responsive`,this.mergedCollapsed&&`${e}-menu--collapsed`],style:this.cssVars}),a?u(No,{ref:"overflowRef",onUpdateOverflow:this.onUpdateOverflow,getCounter:this.getCounter,onUpdateCount:this.onUpdateCount,updateCounter:this.updateCounter,style:{width:"100%",display:"flex",overflow:"hidden"}},{default:c,counter:this.renderCounter}):c());return a?u(Io,{onResize:this.onResize},{default:d}):d()}}),ct={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},st=L({name:"LogOutOutline",render:function(t,o){return Z(),ie("svg",ct,o[0]||(o[0]=[E("path",{d:"M304 336v40a40 40 0 0 1-40 40H104a40 40 0 0 1-40-40V136a40 40 0 0 1 40-40h152c22.09 0 48 17.91 48 40v40",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),E("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M368 336l80-80l-80-80"},null,-1),E("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M176 256h256"},null,-1)]))}}),O={xs:480,sm:768,md:1024,lg:1200,xl:1600},B=M(typeof window<"u"?window.innerWidth:1024);function dt(){const e=()=>{B.value=window.innerWidth},t=h(()=>B.value<=O.sm),o=h(()=>B.value>O.sm&&B.value<=O.md),r=h(()=>B.value>O.md),c=h(()=>B.value>=O.lg),i=h(()=>B.value>=O.xl),a=h(()=>B.value<=O.xs),d=h(()=>B.value>O.xs&&B.value<=O.sm),s=h(()=>B.value>O.sm&&B.value<=O.md),b=h(()=>B.value>O.md&&B.value<=O.lg),k=h(()=>B.value>O.lg),H=h(()=>a.value?"xs":d.value?"sm":s.value?"md":b.value?"lg":"xl");return so(()=>{window.addEventListener("resize",e),e()}),uo(()=>{window.removeEventListener("resize",e)}),{windowWidth:h(()=>B.value),isMobile:t,isTablet:o,isDesktop:r,isLargeScreen:c,isExtraLargeScreen:i,isXs:a,isSm:d,isMd:s,isLg:b,isXl:k,screenType:H}}const ut={class:"sider-header"},vt={class:"title-text"},mt={class:"header-content"},ht={class:"header-left"},pt={class:"header-right"},ft={class:"user-info"},gt={class:"content-wrapper"},bt=L({__name:"AppLayout",setup(e){const t=mo(),o=vo(),{isMobile:r}=dt(),c=h(()=>o.currentUser),i=M(!1),a=M(!0),d=h(()=>t.currentRoute.value.name),s=[{label:"👥 用户管理",key:"users",icon:()=>u(oe,{component:_o})},{label:"📊 用量统计",key:"usage",icon:()=>u(oe,{component:Bo})},{label:"💳 卡密管理",key:"cards",icon:()=>u(oe,{component:Oo})}],b=A=>{t.push({name:A})},k=()=>{r.value||(a.value=!1)},H=()=>{r.value||(a.value=!0)},f=()=>{r.value&&a.value&&(a.value=!1)},N=()=>{r.value&&(a.value=!0)},R=()=>{r.value&&(a.value=!0)},x=()=>{r.value&&(a.value=!a.value)},S=async()=>{i.value=!0;try{o.logout(),t.push("/login")}catch(A){console.error("退出登录失败:",A)}finally{i.value=!1}};return(A,j)=>{const V=bo("router-view");return Z(),ve(_(_e),{class:"app-layout","has-sider":""},{default:W(()=>[_(r)&&!a.value?(Z(),ie("div",{key:0,class:"mobile-overlay",onClick:N,onTouchstart:N},null,32)):me("",!0),U(_(Zo),{bordered:"","collapse-mode":"width","collapsed-width":_(r)?0:64,width:240,collapsed:a.value,"show-trigger":!1,class:ke(["app-sider",{"mobile-sider":_(r)}]),onMouseenter:k,onMouseleave:H,onTouchstart:f},{default:W(()=>[E("div",ut,[E("div",{class:ke(["sider-title",{collapsed:a.value}])},[j[0]||(j[0]=E("span",{class:"title-icon"},"⚙️",-1)),ho(E("span",vt,"管理系统",512),[[po,!a.value]])],2),_(r)&&!a.value?(Z(),ie("button",{key:0,class:"mobile-close-btn",onClick:R,onTouchstart:R}," ✕ ",32)):me("",!0)]),U(_(at),{"collapsed-width":_(r)?0:64,"collapsed-icon-size":22,options:s,value:d.value,collapsed:a.value,"onUpdate:value":b,class:"sider-menu"},null,8,["collapsed-width","value","collapsed"])]),_:1},8,["collapsed-width","collapsed","class"]),U(_(_e),{class:"right-layout"},{default:W(()=>[U(_(Go),{class:"app-header",bordered:""},{default:W(()=>[E("div",mt,[E("div",ht,[_(r)?(Z(),ie("button",{key:0,class:"mobile-menu-btn",onClick:x,onTouchstart:x},j[1]||(j[1]=[E("span",{class:"menu-icon"},"☰",-1)]),32)):me("",!0)]),E("div",pt,[E("div",ft,[U(_(oe),{component:_(ko),size:"18"},null,8,["component"]),E("span",null,fo(c.value?.username||"admin"),1)]),U(_(wo),{type:"default",size:"small",onClick:S,loading:i.value},{icon:W(()=>[U(_(oe),{component:_(st)},null,8,["component"])]),default:W(()=>[j[2]||(j[2]=go(" 退出登录 "))]),_:1,__:[2]},8,["loading"])])])]),_:1}),U(_(Vo),{class:"app-content"},{default:W(()=>[E("div",gt,[U(V,null,{default:W(({Component:$})=>[(Z(),ve(Co,null,[(Z(),ve(xo($)))],1024))]),_:1})])]),_:1})]),_:1})]),_:1})}}}),At=So(bt,[["__scopeId","data-v-158bfd31"]]);export{At as default};
