import{ah as m,ai as A,aj as D,r as u,j as P,d as T,z as R,A as M,B as F}from"./index-bBUuTVMS.js";class _{static async getUsers(t){try{const e=new URLSearchParams;t?.page&&e.append("page",t.page.toString()),t?.limit&&e.append("limit",t.limit.toString()),t?.search&&e.append("search",t.search);const a=`${m.ADMIN_USERS}${e.toString()?"?"+e.toString():""}`;console.log("请求用户列表API:",a);const n=await A.get(a);if(console.log("用户列表API响应:",n),n.users||n.success){const o=n.users||n.data?.users||[];return console.log("原始用户数据:",o),o.map(r=>{const U=d=>d?typeof d=="string"?new Date(d).getTime():d:0,g={username:r.username,email:r.email,createdAt:r.createdAt,updatedAt:r.updatedAt,vip:r.vip?{type:r.vip.type,expireAt:r.vip.expireAt||0,quotaChars:r.vip.quotaChars,usedChars:r.vip.usedChars,isExpired:r.vip.isExpired}:void 0,usage:r.usage?{totalChars:r.usage.totalChars||0,monthlyChars:r.usage.monthlyChars||0}:void 0,createAt:U(r.createdAt||r.created_at),quota:{daily:r.vip?.quotaChars||0,used:r.vip?.usedChars||r.usage?.totalChars||0,resetAt:0}};return console.log("标准化用户数据:",g),g})}throw new Error(n.message||n.error||"获取用户列表失败")}catch(e){console.error("新API失败:",e);try{console.log("尝试使用旧API:",m.USERS);const a=await A.get(m.USERS);if(console.log("旧API响应:",a),a.success)return(a.users||a.data||[]).map(o=>({...o,email:o.email||"",createdAt:o.createAt,usage:{totalChars:o.quota?.used||0,monthlyChars:0}}))}catch(a){console.error("备用API也失败:",a)}throw new Error(e instanceof Error?e.message:"获取用户列表失败")}}static async getUsageData(t={}){try{const e=new URLSearchParams,a=t.page||1,n=Math.min(t.limit||50,100);e.append("page",a.toString()),e.append("limit",n.toString()),t.search&&e.append("search",t.search);const o=`${m.ADMIN_USERS}${e.toString()?"?"+e.toString():""}`;console.log("请求用户列表API (含用量数据):",o),console.log("请求参数:",t);const c=await A.get(o);if(console.log("用户列表API响应:",c),c.users||c.success&&c.data?.users){const r=c.users||c.data?.users||[],U=c.pagination||c.data?.pagination||{},g=this.standardizeUsageData(r),d=this.calculateUsageStats(g);return{users:g,hasMore:U.hasNext||!1,nextCursor:U.page?(U.page+1).toString():void 0,stats:d}}throw new Error(c.message||c.error||"获取用量数据失败")}catch(e){throw new Error(e instanceof Error?e.message:"获取用量数据失败")}}static async getSystemStats(){try{console.log("请求系统统计API:",m.ADMIN_STATS);const t=await A.get(m.ADMIN_STATS);if(console.log("系统统计API响应:",t),t.users&&t.tasks&&t.cards)return{users:t.users,tasks:t.tasks,cards:t.cards,taskTrend:t.taskTrend||[],timestamp:t.timestamp||new Date().toISOString()};throw new Error(t.message||t.error||"获取系统统计失败")}catch(t){throw console.error("系统统计API失败:",t),new Error(t instanceof Error?t.message:"获取系统统计失败")}}static async getGlobalStats(){try{const t=await this.getSystemStats();return{totalUsers:parseInt(t.users.total_users)||0,totalCharsUsed:0,monthlyCharsUsed:0,vipUsersCount:parseInt(t.users.active_vip_users)||0,newUsers7d:parseInt(t.users.new_users_7d)||0,newUsers30d:parseInt(t.users.new_users_30d)||0,totalTasks:parseInt(t.tasks.total_tasks)||0,completedTasks:parseInt(t.tasks.completed_tasks)||0,failedTasks:parseInt(t.tasks.failed_tasks)||0}}catch(t){console.warn("系统统计API失败，尝试使用用量数据计算统计:",t);try{return(await this.getUsageData({limit:1e3})).stats}catch(e){return console.error("备用统计计算也失败:",e),{totalUsers:0,totalCharsUsed:0,monthlyCharsUsed:0,vipUsersCount:0}}}}static standardizeUsageData(t){return t.map(e=>{const a=r=>r?typeof r=="string"?new Date(r).getTime():r:0,n=e.usage_stats||e.usage||{},o=e.vip_info||e.vip||{};return{username:e.username,email:e.email,created_at:e.created_at,createdAt:e.createdAt||a(e.created_at),updatedAt:e.updatedAt||a(e.updated_at),vip_info:{type:o.type,expireAt:o.expireAt||0,quotaChars:o.quotaChars||0,usedChars:o.usedChars||n.totalChars||0,remainingChars:o.remainingChars,usagePercentage:o.usagePercentage,isExpired:o.isExpired},usage_stats:{totalChars:n.totalChars||0,monthlyChars:n.monthlyChars||0,monthlyResetAt:n.monthlyResetAt||0},usage:n,vip:o}})}static calculateUsageStats(t){let e=0,a=0,n=0;return t.forEach(o=>{const c=o.usage_stats||o.usage||{},r=o.vip_info||o.vip||{};e+=c.totalChars||0,a+=c.monthlyChars||0,r&&r.expireAt>Date.now()&&n++}),{totalUsers:t.length,totalCharsUsed:e,monthlyCharsUsed:a,vipUsersCount:n}}static async updateUserVip(t,e){try{const a=await A.put(`${m.ADMIN_USER_VIP}/${t}/vip`,e);if(a.success)return a;throw new Error(a.message||a.error||"VIP状态更新失败")}catch(a){console.warn("新VIP API失败，尝试使用旧API:",a);try{const n=await A.put(`${m.USER_VIP}/${t}/vip`,e);if(n.success)return n}catch(n){console.error("备用VIP API也失败:",n)}throw new Error(a instanceof Error?a.message:"VIP状态更新失败")}}static async getUserDetail(t){try{const e=await A.get(`${m.ADMIN_USER_DETAIL}/${t}`);if(e.success||e.user)return e.user||e.data?.user||e;throw new Error(e.message||e.error||"获取用户详情失败")}catch(e){throw new Error(e instanceof Error?e.message:"获取用户详情失败")}}}const H=D("users",()=>{const C=u([]),t=u(!1),e=u(null),a=u([]),n=u(!1),o=u(null),c=u({totalUsers:0,totalCharsUsed:0,monthlyCharsUsed:0,vipUsersCount:0}),r=u({hasMore:!1,nextCursor:void 0}),U=u({totalUsers:0,totalCharsUsed:0,monthlyCharsUsed:0,vipUsersCount:0}),g=u(!1),d=u(null),E=u({searchTerm:"",activeFilter:"all"}),p=u({searchTerm:"",activeFilter:"active"}),v=u({field:"createAt",direction:"desc"}),h=u({field:"usage.totalChars",direction:"desc"}),x=P(()=>{let s=[...C.value];if(E.value.searchTerm){const i=E.value.searchTerm.toLowerCase();s=s.filter(l=>l.username.toLowerCase().includes(i)||(l.vip?"有VIP":"无VIP").includes(i))}return v.value.field&&s.sort((i,l)=>{const f=v.value.field;let w=I(i,f),S=I(l,f);if(f==="vip.expireAt"&&(w=i.vip?i.vip.expireAt:-1,S=l.vip?l.vip.expireAt:-1),w===S)return 0;const y=v.value.direction==="asc"?1:-1;return w>S?y:-y}),s}),k=P(()=>{let s=[...a.value];if(p.value.searchTerm){const i=p.value.searchTerm.toLowerCase();s=s.filter(l=>l.username.toLowerCase().includes(i))}return p.value.activeFilter==="active"?s=s.filter(i=>i.usage.totalChars>0):p.value.activeFilter==="inactive"?s=s.filter(i=>i.usage.totalChars===0):p.value.activeFilter==="vip"?s=s.filter(i=>i.vip&&i.vip.expireAt>Date.now()):p.value.activeFilter==="non-vip"&&(s=s.filter(i=>!i.vip||i.vip.expireAt<=Date.now())),h.value.field&&s.sort((i,l)=>{const f=h.value.field;let w=I(i,f),S=I(l,f);if(f==="vip.expireAt"&&(w=i.vip?i.vip.expireAt:-1,S=l.vip?l.vip.expireAt:-1),w===S)return 0;const y=h.value.direction==="asc"?1:-1;return w>S?y:-y}),s}),I=(s,i)=>i.split(".").reduce((l,f)=>l?l[f]:void 0,s);return{users:C,isLoadingUsers:t,usersError:e,usageData:a,isLoadingUsage:n,usageError:o,usageStats:c,usagePagination:r,globalStats:U,isLoadingGlobalStats:g,globalStatsError:d,userFilter:E,usageFilter:p,userSort:v,usageSort:h,filteredUsers:x,filteredUsageData:k,loadUsers:async()=>{t.value=!0,e.value=null;try{const s=await _.getUsers();C.value=s}catch(s){e.value=s instanceof Error?s.message:"加载用户失败"}finally{t.value=!1}},loadUsageData:async(s={},i=!1)=>{n.value=!0,o.value=null;try{const l=await _.getUsageData(s);i?a.value=l.users:a.value.push(...l.users),c.value=l.stats,r.value={hasMore:l.hasMore,nextCursor:l.nextCursor}}catch(l){o.value=l instanceof Error?l.message:"加载用量数据失败"}finally{n.value=!1}},loadGlobalStats:async()=>{g.value=!0,d.value=null;try{const s=await _.getGlobalStats();U.value=s}catch(s){d.value=s instanceof Error?s.message:"加载全局统计失败"}finally{g.value=!1}},setUserSort:s=>{v.value.field===s?v.value.direction=v.value.direction==="asc"?"desc":"asc":(v.value.field=s,v.value.direction="asc")},setUsageSort:s=>{h.value.field===s?h.value.direction=h.value.direction==="asc"?"desc":"asc":(h.value.field=s,h.value.direction="asc")},setUserSearch:s=>{E.value.searchTerm=s},setUsageSearch:s=>{p.value.searchTerm=s},setUsageFilter:s=>{p.value.activeFilter=s},clearErrors:()=>{e.value=null,o.value=null,d.value=null}}}),L={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},J=T({name:"StarOutline",render:function(t,e){return F(),R("svg",L,e[0]||(e[0]=[M("path",{d:"M480 208H308L256 48l-52 160H32l140 96l-54 160l138-100l138 100l-54-160z",fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"32"},null,-1)]))}});export{J as S,_ as U,H as u};
