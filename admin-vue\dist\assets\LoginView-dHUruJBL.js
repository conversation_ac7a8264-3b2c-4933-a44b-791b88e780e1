import{d as N,h as g,a as ae,c as b,b as _,e as F,f as m,g as oe,i as le,u as J,r as B,j as E,k as G,l as Z,m as se,n as ne,p as ie,N as de,w as ce,t as Q,v as ue,o as pe,q as me,s as fe,x as ge,y as he,z as A,A as p,B as I,C as ve,D as we,E as i,F as f,G as l,H as j,I as xe,J as Ce,K as be,L as O,M as ye,O as _e,P as Ne}from"./index-bBUuTVMS.js";import{N as ke,a as U,b as V}from"./Card-DjpRSm_f.js";import{u as Se,c as $,a as ze,r as K,b as Ie,h as W,C as Re,N as R,B as q,_ as Ee}from"./_plugin-vue_export-helper-JcRYbv4V.js";import{N as Ae,a as M}from"./FormItem-yHcy6zAK.js";import{P as Pe}from"./PersonOutline-R4Ifj_Oy.js";const De=N({name:"ChevronLeft",render(){return g("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},g("path",{d:"M10.3536 3.14645C10.5488 3.34171 10.5488 3.65829 10.3536 3.85355L6.20711 8L10.3536 12.1464C10.5488 12.3417 10.5488 12.6583 10.3536 12.8536C10.1583 13.0488 9.84171 13.0488 9.64645 12.8536L5.14645 8.35355C4.95118 8.15829 4.95118 7.84171 5.14645 7.64645L9.64645 3.14645C9.84171 2.95118 10.1583 2.95118 10.3536 3.14645Z",fill:"currentColor"}))}});function Te(e){const{fontWeight:d,textColor1:t,textColor2:n,textColorDisabled:r,dividerColor:s,fontSize:v}=e;return{titleFontSize:v,titleFontWeight:d,dividerColor:s,titleTextColor:t,titleTextColorDisabled:r,fontSize:v,textColor:n,arrowColor:n,arrowColorDisabled:r,itemMargin:"16px 0 0 0",titlePadding:"16px 0 0 0"}}const $e={common:ae,self:Te},Me=b("collapse","width: 100%;",[b("collapse-item",`
 font-size: var(--n-font-size);
 color: var(--n-text-color);
 transition:
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 margin: var(--n-item-margin);
 `,[_("disabled",[m("header","cursor: not-allowed;",[m("header-main",`
 color: var(--n-title-text-color-disabled);
 `),b("collapse-item-arrow",`
 color: var(--n-arrow-color-disabled);
 `)])]),b("collapse-item","margin-left: 32px;"),F("&:first-child","margin-top: 0;"),F("&:first-child >",[m("header","padding-top: 0;")]),_("left-arrow-placement",[m("header",[b("collapse-item-arrow","margin-right: 4px;")])]),_("right-arrow-placement",[m("header",[b("collapse-item-arrow","margin-left: 4px;")])]),m("content-wrapper",[m("content-inner","padding-top: 16px;"),le({duration:"0.15s"})]),_("active",[m("header",[_("active",[b("collapse-item-arrow","transform: rotate(90deg);")])])]),F("&:not(:first-child)","border-top: 1px solid var(--n-divider-color);"),oe("disabled",[_("trigger-area-main",[m("header",[m("header-main","cursor: pointer;"),b("collapse-item-arrow","cursor: default;")])]),_("trigger-area-arrow",[m("header",[b("collapse-item-arrow","cursor: pointer;")])]),_("trigger-area-extra",[m("header",[m("header-extra","cursor: pointer;")])])]),m("header",`
 font-size: var(--n-title-font-size);
 display: flex;
 flex-wrap: nowrap;
 align-items: center;
 transition: color .3s var(--n-bezier);
 position: relative;
 padding: var(--n-title-padding);
 color: var(--n-title-text-color);
 `,[m("header-main",`
 display: flex;
 flex-wrap: nowrap;
 align-items: center;
 font-weight: var(--n-title-font-weight);
 transition: color .3s var(--n-bezier);
 flex: 1;
 color: var(--n-title-text-color);
 `),m("header-extra",`
 display: flex;
 align-items: center;
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 `),b("collapse-item-arrow",`
 display: flex;
 transition:
 transform .15s var(--n-bezier),
 color .3s var(--n-bezier);
 font-size: 18px;
 color: var(--n-arrow-color);
 `)])])]),Be=Object.assign(Object.assign({},G.props),{defaultExpandedNames:{type:[Array,String],default:null},expandedNames:[Array,String],arrowPlacement:{type:String,default:"left"},accordion:{type:Boolean,default:!1},displayDirective:{type:String,default:"if"},triggerAreas:{type:Array,default:()=>["main","extra","arrow"]},onItemHeaderClick:[Function,Array],"onUpdate:expandedNames":[Function,Array],onUpdateExpandedNames:[Function,Array],onExpandedNamesChange:{type:[Function,Array],validator:()=>!0,default:void 0}}),X=ne("n-collapse"),Le=N({name:"Collapse",props:Be,slots:Object,setup(e,{slots:d}){const{mergedClsPrefixRef:t,inlineThemeDisabled:n,mergedRtlRef:r}=J(e),s=B(e.defaultExpandedNames),v=E(()=>e.expandedNames),h=Se(v,s),w=G("Collapse","-collapse",Me,$e,e,t);function c(x){const{"onUpdate:expandedNames":u,onUpdateExpandedNames:C,onExpandedNamesChange:z}=e;C&&$(C,x),u&&$(u,x),z&&$(z,x),s.value=x}function a(x){const{onItemHeaderClick:u}=e;u&&$(u,x)}function o(x,u,C){const{accordion:z}=e,{value:D}=h;if(z)x?(c([u]),a({name:u,expanded:!0,event:C})):(c([]),a({name:u,expanded:!1,event:C}));else if(!Array.isArray(D))c([u]),a({name:u,expanded:!0,event:C});else{const k=D.slice(),T=k.findIndex(L=>u===L);~T?(k.splice(T,1),c(k),a({name:u,expanded:!1,event:C})):(k.push(u),c(k),a({name:u,expanded:!0,event:C}))}}ie(X,{props:e,mergedClsPrefixRef:t,expandedNamesRef:h,slots:d,toggleItem:o});const y=Z("Collapse",r,t),P=E(()=>{const{common:{cubicBezierEaseInOut:x},self:{titleFontWeight:u,dividerColor:C,titlePadding:z,titleTextColor:D,titleTextColorDisabled:k,textColor:T,arrowColor:L,fontSize:Y,titleFontSize:ee,arrowColorDisabled:te,itemMargin:re}}=w.value;return{"--n-font-size":Y,"--n-bezier":x,"--n-text-color":T,"--n-divider-color":C,"--n-title-padding":z,"--n-title-font-size":ee,"--n-title-text-color":D,"--n-title-text-color-disabled":k,"--n-title-font-weight":u,"--n-arrow-color":L,"--n-arrow-color-disabled":te,"--n-item-margin":re}}),S=n?se("collapse",void 0,P,e):void 0;return{rtlEnabled:y,mergedTheme:w,mergedClsPrefix:t,cssVars:n?void 0:P,themeClass:S?.themeClass,onRender:S?.onRender}},render(){var e;return(e=this.onRender)===null||e===void 0||e.call(this),g("div",{class:[`${this.mergedClsPrefix}-collapse`,this.rtlEnabled&&`${this.mergedClsPrefix}-collapse--rtl`,this.themeClass],style:this.cssVars},this.$slots)}}),Fe=N({name:"CollapseItemContent",props:{displayDirective:{type:String,required:!0},show:Boolean,clsPrefix:{type:String,required:!0}},setup(e){return{onceTrue:ze(Q(e,"show"))}},render(){return g(de,null,{default:()=>{const{show:e,displayDirective:d,onceTrue:t,clsPrefix:n}=this,r=d==="show"&&t,s=g("div",{class:`${n}-collapse-item__content-wrapper`},g("div",{class:`${n}-collapse-item__content-inner`},this.$slots));return r?ce(s,[[ue,e]]):e?s:null}})}}),Oe={title:String,name:[String,Number],disabled:Boolean,displayDirective:String},Ue=N({name:"CollapseItem",props:Oe,setup(e){const{mergedRtlRef:d}=J(e),t=pe(),n=me(()=>{var o;return(o=e.name)!==null&&o!==void 0?o:t}),r=ge(X);r||fe("collapse-item","`n-collapse-item` must be placed inside `n-collapse`.");const{expandedNamesRef:s,props:v,mergedClsPrefixRef:h,slots:w}=r,c=E(()=>{const{value:o}=s;if(Array.isArray(o)){const{value:y}=n;return!~o.findIndex(P=>P===y)}else if(o){const{value:y}=n;return y!==o}return!0});return{rtlEnabled:Z("Collapse",d,h),collapseSlots:w,randomName:t,mergedClsPrefix:h,collapsed:c,triggerAreas:Q(v,"triggerAreas"),mergedDisplayDirective:E(()=>{const{displayDirective:o}=e;return o||v.displayDirective}),arrowPlacement:E(()=>v.arrowPlacement),handleClick(o){let y="main";W(o,"arrow")&&(y="arrow"),W(o,"extra")&&(y="extra"),v.triggerAreas.includes(y)&&r&&!e.disabled&&r.toggleItem(c.value,n.value,o)}}},render(){const{collapseSlots:e,$slots:d,arrowPlacement:t,collapsed:n,mergedDisplayDirective:r,mergedClsPrefix:s,disabled:v,triggerAreas:h}=this,w=K(d.header,{collapsed:n},()=>[this.title]),c=d["header-extra"]||e["header-extra"],a=d.arrow||e.arrow;return g("div",{class:[`${s}-collapse-item`,`${s}-collapse-item--${t}-arrow-placement`,v&&`${s}-collapse-item--disabled`,!n&&`${s}-collapse-item--active`,h.map(o=>`${s}-collapse-item--trigger-area-${o}`)]},g("div",{class:[`${s}-collapse-item__header`,!n&&`${s}-collapse-item__header--active`]},g("div",{class:`${s}-collapse-item__header-main`,onClick:this.handleClick},t==="right"&&w,g("div",{class:`${s}-collapse-item-arrow`,key:this.rtlEnabled?0:1,"data-arrow":!0},K(a,{collapsed:n},()=>[g(he,{clsPrefix:s},{default:()=>this.rtlEnabled?g(De,null):g(Re,null)})])),t==="left"&&w),Ie(c,{collapsed:n},o=>g("div",{class:`${s}-collapse-item__header-extra`,onClick:this.handleClick,"data-extra":!0},o))),g(Fe,{clsPrefix:s,displayDirective:r,show:!n},d))}}),je={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ve=N({name:"InformationCircleOutline",render:function(d,t){return I(),A("svg",je,t[0]||(t[0]=[p("path",{d:"M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184s184-82.39 184-184S349.61 64 248 64z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),p("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M220 220h32v116"},null,-1),p("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32",d:"M208 340h88"},null,-1),p("path",{d:"M248 130a26 26 0 1 0 26 26a26 26 0 0 0-26-26z",fill:"currentColor"},null,-1)]))}}),Ke={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},H=N({name:"KeyOutline",render:function(d,t){return I(),A("svg",Ke,t[0]||(t[0]=[p("path",{d:"M218.1 167.17c0 13 0 25.6 4.1 37.4c-43.1 50.6-156.9 184.3-167.5 194.5a20.17 20.17 0 0 0-6.7 15c0 8.5 5.2 16.7 9.6 21.3c6.6 6.9 34.8 33 40 28c15.4-15 18.5-19 24.8-25.2c9.5-9.3-1-28.3 2.3-36s6.8-9.2 12.5-10.4s15.8 2.9 23.7 3c8.3.1 12.8-3.4 19-9.2c5-4.6 8.6-8.9 8.7-15.6c.2-9-12.8-20.9-3.1-30.4s23.7 6.2 34 5s22.8-15.5 24.1-21.6s-11.7-21.8-9.7-30.7c.7-3 6.8-10 11.4-11s25 6.9 29.6 5.9c5.6-1.2 12.1-7.1 17.4-10.4c15.5 6.7 29.6 9.4 47.7 9.4c68.5 0 124-53.4 124-119.2S408.5 48 340 48s-121.9 53.37-121.9 119.17zM400 144a32 32 0 1 1-32-32a32 32 0 0 1 32 32z",fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"32"},null,-1)]))}}),We={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},qe=N({name:"SettingsOutline",render:function(d,t){return I(),A("svg",We,t[0]||(t[0]=[p("path",{d:"M262.29 192.31a64 64 0 1 0 57.4 57.4a64.13 64.13 0 0 0-57.4-57.4zM416.39 256a154.34 154.34 0 0 1-1.53 20.79l45.21 35.46a10.81 10.81 0 0 1 2.45 13.75l-42.77 74a10.81 10.81 0 0 1-13.14 4.59l-44.9-18.08a16.11 16.11 0 0 0-15.17 1.75A164.48 164.48 0 0 1 325 400.8a15.94 15.94 0 0 0-8.82 12.14l-6.73 47.89a11.08 11.08 0 0 1-10.68 9.17h-85.54a11.11 11.11 0 0 1-10.69-8.87l-6.72-47.82a16.07 16.07 0 0 0-9-12.22a155.3 155.3 0 0 1-21.46-12.57a16 16 0 0 0-15.11-1.71l-44.89 18.07a10.81 10.81 0 0 1-13.14-4.58l-42.77-74a10.8 10.8 0 0 1 2.45-13.75l38.21-30a16.05 16.05 0 0 0 6-14.08c-.36-4.17-.58-8.33-.58-12.5s.21-8.27.58-12.35a16 16 0 0 0-6.07-13.94l-38.19-30A10.81 10.81 0 0 1 49.48 186l42.77-74a10.81 10.81 0 0 1 13.14-4.59l44.9 18.08a16.11 16.11 0 0 0 15.17-1.75A164.48 164.48 0 0 1 187 111.2a15.94 15.94 0 0 0 8.82-12.14l6.73-47.89A11.08 11.08 0 0 1 213.23 42h85.54a11.11 11.11 0 0 1 10.69 8.87l6.72 47.82a16.07 16.07 0 0 0 9 12.22a155.3 155.3 0 0 1 21.46 12.57a16 16 0 0 0 15.11 1.71l44.89-18.07a10.81 10.81 0 0 1 13.14 4.58l42.77 74a10.8 10.8 0 0 1-2.45 13.75l-38.21 30a16.05 16.05 0 0 0-6.05 14.08c.33 4.14.55 8.3.55 12.47z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1)]))}}),He={class:"login-page"},Je={class:"login-container"},Ge={class:"login-header"},Ze={class:"login-logo"},Qe={class:"login-options"},Xe={key:0,class:"error-message"},Ye={class:"login-footer"},et=N({__name:"LoginView",setup(e){const d=Ne(),t=ve(),n=B(null),r=we({username:"",password:"",auth_code:""}),s=B(!1),v={username:[{required:!0,message:"请输入管理员用户名",trigger:["input","blur"]}],password:[{required:!0,message:"请输入密码",trigger:["input","blur"]}],auth_code:[{required:!1,message:"请输入授权码",trigger:["input","blur"]}]},h=B(!1),w=async()=>{if(n.value)try{await n.value.validate(),h.value=!0;let c;if(r.username&&r.password)c={username:r.username,password:r.password};else if(r.auth_code)c={username:r.username||"admin",password:"",auth_code:r.auth_code};else throw new Error("请输入用户名和密码，或使用授权码登录");await t.login(c)?(console.log("登录成功"),await _e(),await d.replace("/users"),console.log("跳转完成")):console.error("登录失败:",t.error||"登录失败")}catch(c){console.error("表单验证失败:",c)}finally{h.value=!1}};return(c,a)=>(I(),A("div",He,[p("div",Je,[i(l(ke),{class:"login-card",size:"large"},{default:f(()=>[p("div",Ge,[p("div",Ze,[i(l(R),{component:l(qe),size:"48"},null,8,["component"])]),a[4]||(a[4]=p("h1",{class:"login-title"},"TTS Cards 管理系统",-1)),a[5]||(a[5]=p("p",{class:"login-subtitle"},"管理员登录 - 请输入您的管理员凭据",-1))]),i(l(Ae),{ref_key:"formRef",ref:n,model:r,rules:v,size:"large",onSubmit:xe(w,["prevent"])},{default:f(()=>[i(l(M),{path:"username",label:"管理员用户名"},{default:f(()=>[i(l(U),{value:r.username,"onUpdate:value":a[0]||(a[0]=o=>r.username=o),placeholder:"请输入管理员用户名",disabled:h.value},{prefix:f(()=>[i(l(R),{component:l(Pe)},null,8,["component"])]),_:1},8,["value","disabled"])]),_:1}),i(l(M),{path:"password",label:"密码"},{default:f(()=>[i(l(U),{value:r.password,"onUpdate:value":a[1]||(a[1]=o=>r.password=o),type:"password",placeholder:"请输入密码",disabled:h.value,"show-password-on":"mousedown",onKeyup:be(w,["enter"])},{prefix:f(()=>[i(l(R),{component:l(H)},null,8,["component"])]),_:1},8,["value","disabled"])]),_:1}),s.value?(I(),Ce(l(Le),{key:0},{default:f(()=>[i(l(Ue),{title:"使用授权码登录（兼容模式）",name:"legacy"},{default:f(()=>[i(l(M),{path:"auth_code",label:"授权码"},{default:f(()=>[i(l(U),{value:r.auth_code,"onUpdate:value":a[2]||(a[2]=o=>r.auth_code=o),type:"password",placeholder:"请输入授权码",disabled:h.value,"show-password-on":"mousedown"},{prefix:f(()=>[i(l(R),{component:l(H)},null,8,["component"])]),_:1},8,["value","disabled"])]),_:1})]),_:1})]),_:1})):j("",!0),p("div",Qe,[i(l(q),{text:"",size:"small",onClick:a[3]||(a[3]=o=>s.value=!s.value)},{default:f(()=>[O(ye(s.value?"隐藏":"显示")+"兼容模式 ",1)]),_:1})]),i(l(M),null,{default:f(()=>[i(l(q),{type:"primary",size:"large",block:"",loading:h.value,onClick:w},{default:f(()=>a[6]||(a[6]=[O(" 登录 ")])),_:1,__:[6]},8,["loading"])]),_:1})]),_:1},8,["model"]),l(t).error?(I(),A("div",Xe,[i(l(V),{type:"error",title:l(t).error,closable:"",onClose:l(t).clearError},null,8,["title","onClose"])])):j("",!0),p("div",Ye,[i(l(V),{type:"info","show-icon":!1},{header:f(()=>[i(l(R),{component:l(Ve)},null,8,["component"]),a[7]||(a[7]=O(" 认证说明 "))]),default:f(()=>[a[8]||(a[8]=p("div",null,[p("p",null,"• 管理员用户名必须在 ADMIN_USERS 环境变量中配置"),p("p",null,"• 使用用户名+密码登录获取JWT Token"),p("p",null,"• Token有效期：2小时")],-1))]),_:1,__:[8]})])]),_:1})])]))}}),st=Ee(et,[["__scopeId","data-v-3ae236eb"]]);export{st as default};
