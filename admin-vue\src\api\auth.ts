import { httpClient } from './http'
import { API_ENDPOINTS } from './config'
import type { AuthRequest, AuthResponse } from '@/types'

// 认证API服务
export class AuthService {
  // 登录 (支持新旧API格式)
  static async login(authData: AuthRequest): Promise<AuthResponse> {
    try {
      // 优先尝试新API格式
      if (authData.username && authData.password) {
        try {
          console.log('尝试使用新登录API:', API_ENDPOINTS.AUTH_LOGIN)
          const newApiResponse = await httpClient.post<any>(API_ENDPOINTS.AUTH_LOGIN, {
            username: authData.username,
            password: authData.password
          }) as AuthResponse

          console.log('新API响应数据:', newApiResponse)

          // 新API成功，处理响应 (适配实际后端格式)
          if (newApiResponse.access_token || newApiResponse.token) {
            console.log('新API登录成功')

            // 标准化响应格式
            const standardResponse: AuthResponse = {
              success: true,
              token: newApiResponse.access_token || newApiResponse.token,
              refreshToken: newApiResponse.refresh_token || newApiResponse.refreshToken,
              expiresAt: newApiResponse.expires_in ? Date.now() + (newApiResponse.expires_in * 1000) : newApiResponse.expiresAt,
              user: newApiResponse.username ? { username: newApiResponse.username } : newApiResponse.user
            }

            this.saveAuthData(standardResponse, authData.username)
            return standardResponse
          } else {
            console.warn('新API响应中没有找到token字段:', newApiResponse)
          }
        } catch (newApiError) {
          console.warn('新API登录失败，尝试旧API:', newApiError)
        }
      }

      // 如果新API失败或者是旧格式数据，尝试旧API
      if (authData.auth_code) {
        console.log('尝试使用旧登录API:', API_ENDPOINTS.AUTH)
        const oldApiResponse = await httpClient.post<any>(API_ENDPOINTS.AUTH, {
          auth_code: authData.auth_code,
          username: authData.username
        }) as AuthResponse

        console.log('旧API响应数据:', oldApiResponse)

        if (oldApiResponse.success && oldApiResponse.token) {
          console.log('旧API登录成功')
          this.saveAuthData(oldApiResponse, authData.username)
          return oldApiResponse
        }
      }

      throw new Error('登录失败：无效的凭据')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '登录失败')
    }
  }

  // 保存认证数据
  private static saveAuthData(response: AuthResponse, username?: string): void {
    // 保存access token (支持多种字段名)
    const token = response.token || response.access_token
    if (token) {
      localStorage.setItem('auth_token', token)
      console.log('Token已保存到localStorage')
    }

    // 保存refresh token (支持多种字段名)
    const refreshToken = response.refreshToken || response.refresh_token
    if (refreshToken) {
      localStorage.setItem('refresh_token', refreshToken)
      console.log('RefreshToken已保存到localStorage')
    }

    // 保存用户名 (支持多种格式)
    const finalUsername = username || response.user?.username || response.username
    if (finalUsername) {
      localStorage.setItem('username', finalUsername)
      console.log('用户名已保存:', finalUsername)
    }

    // 保存过期时间
    if (response.expiresAt) {
      localStorage.setItem('token_expires_at', response.expiresAt.toString())
      console.log('Token过期时间已保存:', new Date(response.expiresAt))
    } else {
      // 如果没有过期时间，设置默认2小时过期
      const defaultExpiry = Date.now() + (2 * 60 * 60 * 1000) // 2小时
      localStorage.setItem('token_expires_at', defaultExpiry.toString())
      console.log('使用默认Token过期时间:', new Date(defaultExpiry))
    }
  }

  // 登出
  static logout(): void {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('username')
    localStorage.removeItem('token_expires_at')
  }

  // 检查认证状态
  static isAuthenticated(): boolean {
    const token = localStorage.getItem('auth_token')
    const expiresAt = localStorage.getItem('token_expires_at')
    
    if (!token || !expiresAt) {
      return false
    }
    
    // 检查token是否过期
    const now = Date.now()
    const expiry = parseInt(expiresAt)
    
    if (now >= expiry) {
      this.logout()
      return false
    }
    
    return true
  }

  // 获取当前用户信息
  static getCurrentUser(): { username: string; token: string } | null {
    const token = localStorage.getItem('auth_token')
    const username = localStorage.getItem('username')
    
    if (!token || !this.isAuthenticated()) {
      return null
    }
    
    return {
      username: username || 'admin',
      token
    }
  }

  // 获取token
  static getToken(): string | null {
    if (!this.isAuthenticated()) {
      return null
    }
    return localStorage.getItem('auth_token')
  }
}
