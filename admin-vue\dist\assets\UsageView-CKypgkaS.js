import{S as te,f as ne,D as oe,R as ae,N as F}from"./PageLoading.vue_vue_type_style_index_0_scoped_ea6456ad_lang-C8XH62qh.js";import{c as y,g as A,b as S,e as w,f as U,d as C,h as v,u as W,ae as se,l as re,p as le,a as ie,af as ue,a7 as de,k as q,j as h,aa as ce,m as pe,r as H,V as me,z,A as l,B as k,J as I,F as x,Z as G,G as s,E as c,H as K,M as _,U as ge,ag as he,L as T,X as fe,ac as ve,ad as be}from"./index-bBUuTVMS.js";import{p as ye,q as we,t as ke,N as D,_ as M,B as j}from"./_plugin-vue_export-helper-JcRYbv4V.js";import{u as Se}from"./Dropdown-CPvaWprP.js";import{N as $e,b as Ce}from"./Card-DjpRSm_f.js";import{u as xe,S as _e}from"./StarOutline-B0PkZbhz.js";import{B as ze}from"./BarChartOutline-mQUuYBKn.js";import{P as Ve}from"./PeopleOutline-SQXC-ssz.js";const u="0!important",X="-1px!important";function V(e){return S(`${e}-type`,[w("& +",[y("button",{},[S(`${e}-type`,[U("border",{borderLeftWidth:u}),U("state-border",{left:X})])])])])}function N(e){return S(`${e}-type`,[w("& +",[y("button",[S(`${e}-type`,[U("border",{borderTopWidth:u}),U("state-border",{top:X})])])])])}const Ne=y("button-group",`
 flex-wrap: nowrap;
 display: inline-flex;
 position: relative;
`,[A("vertical",{flexDirection:"row"},[A("rtl",[y("button",[w("&:first-child:not(:last-child)",`
 margin-right: ${u};
 border-top-right-radius: ${u};
 border-bottom-right-radius: ${u};
 `),w("&:last-child:not(:first-child)",`
 margin-left: ${u};
 border-top-left-radius: ${u};
 border-bottom-left-radius: ${u};
 `),w("&:not(:first-child):not(:last-child)",`
 margin-left: ${u};
 margin-right: ${u};
 border-radius: ${u};
 `),V("default"),S("ghost",[V("primary"),V("info"),V("success"),V("warning"),V("error")])])])]),S("vertical",{flexDirection:"column"},[y("button",[w("&:first-child:not(:last-child)",`
 margin-bottom: ${u};
 margin-left: ${u};
 margin-right: ${u};
 border-bottom-left-radius: ${u};
 border-bottom-right-radius: ${u};
 `),w("&:last-child:not(:first-child)",`
 margin-top: ${u};
 margin-left: ${u};
 margin-right: ${u};
 border-top-left-radius: ${u};
 border-top-right-radius: ${u};
 `),w("&:not(:first-child):not(:last-child)",`
 margin: ${u};
 border-radius: ${u};
 `),N("default"),S("ghost",[N("primary"),N("info"),N("success"),N("warning"),N("error")])])])]),Be={size:{type:String,default:void 0},vertical:Boolean},Te=C({name:"ButtonGroup",props:Be,setup(e){const{mergedClsPrefixRef:t,mergedRtlRef:n}=W(e);return se("-button-group",Ne,t),le(ye,e),{rtlEnabled:re("ButtonGroup",n,t),mergedClsPrefix:t}},render(){const{mergedClsPrefix:e}=this;return v("div",{class:[`${e}-button-group`,this.rtlEnabled&&`${e}-button-group--rtl`,this.vertical&&`${e}-button-group--vertical`],role:"group"},this.$slots)}});function Ue(e){const{opacityDisabled:t,heightTiny:n,heightSmall:o,heightMedium:d,heightLarge:p,heightHuge:f,primaryColor:m,fontSize:r}=e;return{fontSize:r,textColor:m,sizeTiny:n,sizeSmall:o,sizeMedium:d,sizeLarge:p,sizeHuge:f,color:m,opacitySpinning:t}}const De={common:ie,self:Ue},Pe=w([w("@keyframes spin-rotate",`
 from {
 transform: rotate(0);
 }
 to {
 transform: rotate(360deg);
 }
 `),y("spin-container",`
 position: relative;
 `,[y("spin-body",`
 position: absolute;
 top: 50%;
 left: 50%;
 transform: translateX(-50%) translateY(-50%);
 `,[we()])]),y("spin-body",`
 display: inline-flex;
 align-items: center;
 justify-content: center;
 flex-direction: column;
 `),y("spin",`
 display: inline-flex;
 height: var(--n-size);
 width: var(--n-size);
 font-size: var(--n-size);
 color: var(--n-color);
 `,[S("rotate",`
 animation: spin-rotate 2s linear infinite;
 `)]),y("spin-description",`
 display: inline-block;
 font-size: var(--n-font-size);
 color: var(--n-text-color);
 transition: color .3s var(--n-bezier);
 margin-top: 8px;
 `),y("spin-content",`
 opacity: 1;
 transition: opacity .3s var(--n-bezier);
 pointer-events: all;
 `,[S("spinning",`
 user-select: none;
 -webkit-user-select: none;
 pointer-events: none;
 opacity: var(--n-opacity-spinning);
 `)])]),Le={small:20,medium:18,large:16},Oe=Object.assign(Object.assign({},q.props),{contentClass:String,contentStyle:[Object,String],description:String,stroke:String,size:{type:[String,Number],default:"medium"},show:{type:Boolean,default:!0},strokeWidth:Number,rotate:{type:Boolean,default:!0},spinning:{type:Boolean,validator:()=>!0,default:void 0},delay:Number}),Re=C({name:"Spin",props:Oe,slots:Object,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:n}=W(e),o=q("Spin","-spin",Pe,De,e,t),d=h(()=>{const{size:r}=e,{common:{cubicBezierEaseInOut:b},self:$}=o.value,{opacitySpinning:P,color:L,textColor:O}=$,R=typeof r=="number"?ke(r):$[ce("size",r)];return{"--n-bezier":b,"--n-opacity-spinning":P,"--n-size":R,"--n-color":L,"--n-text-color":O}}),p=n?pe("spin",h(()=>{const{size:r}=e;return typeof r=="number"?String(r):r[0]}),d,e):void 0,f=Se(e,["spinning","show"]),m=H(!1);return me(r=>{let b;if(f.value){const{delay:$}=e;if($){b=window.setTimeout(()=>{m.value=!0},$),r(()=>{clearTimeout(b)});return}}m.value=f.value}),{mergedClsPrefix:t,active:m,mergedStrokeWidth:h(()=>{const{strokeWidth:r}=e;if(r!==void 0)return r;const{size:b}=e;return Le[typeof b=="number"?"medium":b]}),cssVars:n?void 0:d,themeClass:p?.themeClass,onRender:p?.onRender}},render(){var e,t;const{$slots:n,mergedClsPrefix:o,description:d}=this,p=n.icon&&this.rotate,f=(d||n.description)&&v("div",{class:`${o}-spin-description`},d||((e=n.description)===null||e===void 0?void 0:e.call(n))),m=n.icon?v("div",{class:[`${o}-spin-body`,this.themeClass]},v("div",{class:[`${o}-spin`,p&&`${o}-spin--rotate`],style:n.default?"":this.cssVars},n.icon()),f):v("div",{class:[`${o}-spin-body`,this.themeClass]},v(ue,{clsPrefix:o,style:n.default?"":this.cssVars,stroke:this.stroke,"stroke-width":this.mergedStrokeWidth,class:`${o}-spin`}),f);return(t=this.onRender)===null||t===void 0||t.call(this),n.default?v("div",{class:[`${o}-spin-container`,this.themeClass],style:this.cssVars},v("div",{class:[`${o}-spin-content`,this.active&&`${o}-spin-content--spinning`,this.contentClass],style:this.contentStyle},n),v(de,{name:"fade-in-transition"},{default:()=>this.active?m:null})):m}}),je={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ie=C({name:"DocumentTextOutline",render:function(t,n){return k(),z("svg",je,n[0]||(n[0]=[l("path",{d:"M416 221.25V416a48 48 0 0 1-48 48H144a48 48 0 0 1-48-48V96a48 48 0 0 1 48-48h98.75a32 32 0 0 1 22.62 9.37l141.26 141.26a32 32 0 0 1 9.37 22.62z",fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"32"},null,-1),l("path",{d:"M256 56v120a32 32 0 0 0 32 32h120",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),l("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M176 288h160"},null,-1),l("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M176 368h160"},null,-1)]))}}),Me={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ee=C({name:"TrendingDownOutline",render:function(t,n){return k(),z("svg",Me,n[0]||(n[0]=[l("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M352 368h112V256"},null,-1),l("path",{d:"M48 144l121.37 121.37a32 32 0 0 0 45.26 0l50.74-50.74a32 32 0 0 1 45.26 0L448 352",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1)]))}}),Fe={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Y=C({name:"TrendingUpOutline",render:function(t,n){return k(),z("svg",Fe,n[0]||(n[0]=[l("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M352 144h112v112"},null,-1),l("path",{d:"M48 368l121.37-121.37a32 32 0 0 1 45.26 0l50.74 50.74a32 32 0 0 0 45.26 0L448 160",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1)]))}}),Ae={class:"stats-content"},Ge={class:"stats-icon"},We={class:"stats-info"},qe={class:"stats-value"},He={class:"stats-label"},Ke=C({__name:"StatsCard",props:{value:{type:[Number,String],required:!0},label:{type:String,required:!0},icon:{type:Object,required:!0},color:{type:String,default:"primary"},format:{type:String,default:"number"},trend:{type:Object,default:void 0},loading:{type:Boolean,default:!1}},setup(e){const t=e,n=h(()=>{const o=typeof t.value=="string"?parseFloat(t.value):t.value;if(isNaN(o))return t.value;switch(t.format){case"currency":return new Intl.NumberFormat("zh-CN",{style:"currency",currency:"CNY"}).format(o);case"percentage":return`${o}%`;case"number":default:return new Intl.NumberFormat("zh-CN").format(o)}});return(o,d)=>(k(),I(s($e),{class:G(["stats-card",[`stats-card--${e.color}`,{"stats-card--loading":e.loading}]]),hoverable:""},{default:x(()=>[c(s(Re),{show:e.loading,size:"small"},{default:x(()=>[l("div",Ae,[l("div",Ge,[c(s(D),{component:e.icon,size:"24"},null,8,["component"])]),l("div",We,[l("div",qe,_(n.value),1),l("div",He,_(e.label),1)])]),e.trend?(k(),z("div",{key:0,class:G(["stats-trend",[`trend--${e.trend.type}`]])},[c(s(D),{component:e.trend.type==="up"?s(Y):s(Ee),size:"16"},null,8,["component"]),l("span",null,_(e.trend.value),1)],2)):K("",!0)]),_:1},8,["show"])]),_:1},8,["class"]))}}),B=M(Ke,[["__scopeId","data-v-9d3dc5a5"]]),Xe={class:"filter-buttons"},Ye={class:"filter-label"},Je=C({__name:"FilterButtons",props:{modelValue:{type:String,required:!0},options:{type:Array,required:!0},label:{type:String,default:"筛选条件："}},emits:["update:modelValue","change"],setup(e,{emit:t}){const n=e,o=t,d=h(()=>n.modelValue),p=f=>{o("update:modelValue",f),o("change",f)};return(f,m)=>(k(),z("div",Xe,[l("div",Ye,_(e.label),1),c(s(Te),null,{default:x(()=>[(k(!0),z(ge,null,he(e.options,r=>(k(),I(s(j),{key:r.value,type:d.value===r.value?"primary":"default",secondary:d.value!==r.value,size:"small",onClick:b=>p(r.value)},{default:x(()=>[T(_(r.label),1)]),_:2},1032,["type","secondary","onClick"]))),128))]),_:1})]))}}),Ze=M(Je,[["__scopeId","data-v-de3ddb02"]]),Qe={class:"usage-view"},et={class:"page-header"},tt={class:"page-title"},nt={class:"stats-grid"},ot={class:"controls-section"},at={class:"controls-left"},st={class:"controls-right"},rt={class:"table-section"},lt={class:"pagination-section"},it={class:"pagination-info"},ut=C({__name:"UsageView",setup(e){const t=xe(),n=H(200),o=h(()=>t.filteredUsageData),d=h(()=>t.isLoadingUsage),p=h(()=>t.usageError);h(()=>t.usageStats);const f=h(()=>t.usagePagination.hasMore),m=h(()=>t.globalStats),r=h(()=>t.isLoadingGlobalStats);h(()=>t.globalStatsError);const b=h({get:()=>t.usageFilter.searchTerm,set:a=>t.setUsageSearch(a)}),$=h({get:()=>t.usageFilter.activeFilter,set:a=>t.setUsageFilter(a)}),P=[{value:"all",label:"全部用户"},{value:"active",label:"有使用记录"},{value:"inactive",label:"无使用记录"},{value:"vip",label:"VIP用户"},{value:"non-vip",label:"非VIP用户"}],L=[{label:"50 条/页",value:50},{label:"100 条/页",value:100},{label:"200 条/页",value:200},{label:"500 条/页",value:500},{label:"1000 条/页",value:1e3}],O=[{key:"username",title:"用户名",sortable:!0,width:150},{key:"usage.totalChars",title:"历史总字符",sortable:!0,width:150,render:a=>{const i=a.usage.totalChars;let g="#10b981";return i>1e6?g="#ef4444":i>5e5&&(g="#f59e0b"),v("span",{style:`font-weight: 600; color: ${g}; font-family: monospace;`},i.toLocaleString())}},{key:"usage.monthlyChars",title:"本月字符",sortable:!0,width:150,render:a=>{const i=a.usage.monthlyChars;let g="#10b981";return i>1e5?g="#ef4444":i>5e4&&(g="#f59e0b"),v("span",{style:`font-weight: 600; color: ${g}; font-family: monospace;`},i.toLocaleString())}},{key:"usage.monthlyResetAt",title:"月度重置时间",sortable:!0,width:180,render:a=>new Date(a.usage.monthlyResetAt).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},{key:"createdAt",title:"注册时间",sortable:!0,width:180,render:a=>new Date(a.createdAt).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},{key:"vip",title:"VIP状态",sortable:!0,width:160,render:a=>{if(a.vip){const i=new Date(a.vip.expireAt).toLocaleDateString(),g=a.vip.expireAt<Date.now();return v(F,{type:g?"error":"success",size:"small"},{default:()=>`${i} (${a.vip.type})`})}else return v(F,{type:"default",size:"small"},{default:()=>"非VIP"})}}],R=a=>{t.setUsageSearch(a)},J=a=>{t.setUsageFilter(a)},Z=a=>{n.value=a,E()},E=async()=>{try{await Promise.all([t.loadGlobalStats(),t.loadUsageData({limit:n.value},!0)])}catch(a){console.error("数据刷新失败:",a)}},Q=async()=>{try{await t.loadUsageData({limit:n.value,cursor:t.usagePagination.nextCursor},!1)}catch(a){console.error("加载更多失败:",a)}},ee=()=>{t.clearErrors()};return fe(async()=>{await Promise.all([t.loadGlobalStats(),t.loadUsageData({limit:n.value},!0)])}),ve(()=>{}),be(()=>{t.clearErrors()}),(a,i)=>(k(),z("div",Qe,[l("div",et,[l("h2",tt,[c(s(D),{component:s(ze)},null,8,["component"]),i[3]||(i[3]=T(" 用量统计 "))]),i[4]||(i[4]=l("p",{class:"page-description"},"查看用户使用情况和统计数据",-1))]),l("div",nt,[c(s(B),{value:m.value.totalUsers,label:"总用户数",icon:s(Ve),color:"primary",loading:r.value},null,8,["value","icon","loading"]),c(s(B),{value:m.value.totalCharsUsed,label:"历史总字符数",icon:s(Ie),color:"success",format:"number",loading:r.value},null,8,["value","icon","loading"]),c(s(B),{value:m.value.monthlyCharsUsed,label:"本月字符数",icon:s(Y),color:"warning",format:"number",loading:r.value},null,8,["value","icon","loading"]),c(s(B),{value:m.value.vipUsersCount,label:"VIP用户数",icon:s(_e),color:"error",loading:r.value},null,8,["value","icon","loading"])]),l("div",ot,[l("div",at,[c(s(te),{modelValue:b.value,"onUpdate:modelValue":i[0]||(i[0]=g=>b.value=g),placeholder:"搜索用户名...",onSearch:R},null,8,["modelValue"]),c(s(Ze),{modelValue:$.value,"onUpdate:modelValue":i[1]||(i[1]=g=>$.value=g),options:P,label:"筛选条件：",onChange:J},null,8,["modelValue"])]),l("div",st,[c(s(ne),{value:n.value,"onUpdate:value":[i[2]||(i[2]=g=>n.value=g),Z],options:L,style:{width:"120px"}},null,8,["value"]),c(s(j),{type:"primary",onClick:E,loading:d.value},{icon:x(()=>[c(s(D),{component:s(ae)},null,8,["component"])]),default:x(()=>[i[5]||(i[5]=T(" 刷新数据 "))]),_:1,__:[5]},8,["loading"])])]),l("div",rt,[c(s(oe),{data:o.value,columns:O,loading:d.value,"row-key":"username",pagination:!1},null,8,["data","loading"]),l("div",lt,[c(s(j),{disabled:!f.value||d.value,onClick:Q,loading:d.value},{default:x(()=>i[6]||(i[6]=[T(" 加载更多 ")])),_:1,__:[6]},8,["disabled","loading"]),l("span",it," 已显示 "+_(o.value.length)+" 条数据 "+_(f.value?"，点击加载更多":"，已全部加载"),1)])]),p.value?(k(),I(s(Ce),{key:0,type:"error",title:p.value,closable:"",onClose:ee,style:{"margin-top":"16px"}},null,8,["title"])):K("",!0)]))}}),bt=M(ut,[["__scopeId","data-v-e68eb319"]]);export{bt as default};
