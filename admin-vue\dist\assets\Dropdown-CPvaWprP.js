import{ao as Vt,X as $e,ad as de,j as B,D as Ln,ay as Kn,a6 as ne,az as Dn,n as Q,r as z,q as re,x as V,L as Xt,U as Ce,ab as Zt,d as U,p as J,w as Ne,aR as Wn,h as w,aS as Hn,t as j,ak as qt,a8 as Yt,O as Jt,aT as jn,o as Gn,a9 as Ie,aU as Be,an as ze,aV as Rn,aW as Un,aX as Vn,aY as st,aZ as ve,a_ as Qt,a$ as be,b0 as Ye,b1 as Xn,b2 as yt,b3 as Zn,b4 as wt,b5 as xt,b6 as Oe,b7 as qn,b8 as St,am as Yn,b9 as Jn,ba as Qn,bb as er,bc as tr,bd as nr,be as rr,e as W,aE as or,a as lt,c as D,g as xe,f as Y,b as Z,bf as ir,a7 as en,u as dt,k as ue,V as tn,v as ar,m as nn,W as ut,ap as sr,bg as lr,Q as rn,S as dr,R as ur,T as Me,aa as se}from"./index-bBUuTVMS.js";import{w as X,x as R,a as cr,i as Je,G as fr,H as ct,I as on,y as hr,J as pr,f as De,K as $t,g as We,L as an,u as sn,c as le,N as vr,C as gr,h as Ct}from"./_plugin-vue_export-helper-JcRYbv4V.js";let Ee=[];const ln=new WeakMap;function br(){Ee.forEach(e=>e(...ln.get(e))),Ee=[]}function mr(e,...t){ln.set(e,t),!Ee.includes(e)&&Ee.push(e)===1&&requestAnimationFrame(br)}function yr(){return Vt()!==null}const wr=typeof window<"u";let pe,Se;const xr=()=>{var e,t;pe=wr?(t=(e=document)===null||e===void 0?void 0:e.fonts)===null||t===void 0?void 0:t.ready:void 0,Se=!1,pe!==void 0?pe.then(()=>{Se=!0}):Se=!0};xr();function Sr(e){if(Se)return;let t=!1;$e(()=>{Se||pe?.then(()=>{t||e()})}),de(()=>{t=!0})}function $r(e,t){return B(()=>{for(const n of t)if(e[n]!==void 0)return e[n];return e[t[t.length-1]]})}function Cr(e={},t){const n=Ln({ctrl:!1,command:!1,win:!1,shift:!1,tab:!1}),{keydown:r,keyup:o}=e,i=s=>{switch(s.key){case"Control":n.ctrl=!0;break;case"Meta":n.command=!0,n.win=!0;break;case"Shift":n.shift=!0;break;case"Tab":n.tab=!0;break}r!==void 0&&Object.keys(r).forEach(d=>{if(d!==s.key)return;const c=r[d];if(typeof c=="function")c(s);else{const{stop:h=!1,prevent:f=!1}=c;h&&s.stopPropagation(),f&&s.preventDefault(),c.handler(s)}})},a=s=>{switch(s.key){case"Control":n.ctrl=!1;break;case"Meta":n.command=!1,n.win=!1;break;case"Shift":n.shift=!1;break;case"Tab":n.tab=!1;break}o!==void 0&&Object.keys(o).forEach(d=>{if(d!==s.key)return;const c=o[d];if(typeof c=="function")c(s);else{const{stop:h=!1,prevent:f=!1}=c;h&&s.stopPropagation(),f&&s.preventDefault(),c.handler(s)}})},l=()=>{(t===void 0||t.value)&&(X("keydown",document,i),X("keyup",document,a)),t!==void 0&&ne(t,s=>{s?(X("keydown",document,i),X("keyup",document,a)):(R("keydown",document,i),R("keyup",document,a))})};return yr()?(Dn(l),de(()=>{(t===void 0||t.value)&&(R("keydown",document,i),R("keyup",document,a))})):l(),Kn(n)}const ra=Q("n-internal-select-menu"),Ar=Q("n-internal-select-menu-body"),ft=Q("n-drawer-body"),ht=Q("n-modal-body"),oa=Q("n-modal-provider"),ia=Q("n-modal"),Fe=Q("n-popover-body"),dn="__disabled__";function ge(e){const t=V(ht,null),n=V(ft,null),r=V(Fe,null),o=V(Ar,null),i=z();if(typeof document<"u"){i.value=document.fullscreenElement;const a=()=>{i.value=document.fullscreenElement};$e(()=>{X("fullscreenchange",document,a)}),de(()=>{R("fullscreenchange",document,a)})}return re(()=>{var a;const{to:l}=e;return l!==void 0?l===!1?dn:l===!0?i.value||"body":l:t?.value?(a=t.value.$el)!==null&&a!==void 0?a:t.value:n?.value?n.value:r?.value?r.value:o?.value?o.value:l??(i.value||"body")})}ge.tdkey=dn;ge.propTo={type:[String,Object,Boolean],default:void 0};function Pr(e,t,n){const r=z(e.value);let o=null;return ne(e,i=>{o!==null&&window.clearTimeout(o),i===!0?n&&!n.value?r.value=!0:o=window.setTimeout(()=>{r.value=!0},t):r.value=!1}),r}function Qe(e,t,n="default"){const r=t[n];if(r===void 0)throw new Error(`[vueuc/${e}]: slot[${n}] is empty.`);return r()}function et(e,t=!0,n=[]){return e.forEach(r=>{if(r!==null){if(typeof r!="object"){(typeof r=="string"||typeof r=="number")&&n.push(Xt(String(r)));return}if(Array.isArray(r)){et(r,t,n);return}if(r.type===Ce){if(r.children===null)return;Array.isArray(r.children)&&et(r.children,t,n)}else r.type!==Zt&&n.push(r)}}),n}function At(e,t,n="default"){const r=t[n];if(r===void 0)throw new Error(`[vueuc/${e}]: slot[${n}] is empty.`);const o=et(r());if(o.length===1)return o[0];throw new Error(`[vueuc/${e}]: slot[${n}] should have exactly one child.`)}let oe=null;function un(){if(oe===null&&(oe=document.getElementById("v-binder-view-measurer"),oe===null)){oe=document.createElement("div"),oe.id="v-binder-view-measurer";const{style:e}=oe;e.position="fixed",e.left="0",e.right="0",e.top="0",e.bottom="0",e.pointerEvents="none",e.visibility="hidden",document.body.appendChild(oe)}return oe.getBoundingClientRect()}function Or(e,t){const n=un();return{top:t,left:e,height:0,width:0,right:n.width-e,bottom:n.height-t}}function He(e){const t=e.getBoundingClientRect(),n=un();return{left:t.left-n.left,top:t.top-n.top,bottom:n.height+n.top-t.bottom,right:n.width+n.left-t.right,width:t.width,height:t.height}}function Ir(e){return e.nodeType===9?null:e.parentNode}function cn(e){if(e===null)return null;const t=Ir(e);if(t===null)return null;if(t.nodeType===9)return document;if(t.nodeType===1){const{overflow:n,overflowX:r,overflowY:o}=getComputedStyle(t);if(/(auto|scroll|overlay)/.test(n+o+r))return t}return cn(t)}const fn=U({name:"Binder",props:{syncTargetWithParent:Boolean,syncTarget:{type:Boolean,default:!0}},setup(e){var t;J("VBinder",(t=Vt())===null||t===void 0?void 0:t.proxy);const n=V("VBinder",null),r=z(null),o=u=>{r.value=u,n&&e.syncTargetWithParent&&n.setTargetRef(u)};let i=[];const a=()=>{let u=r.value;for(;u=cn(u),u!==null;)i.push(u);for(const A of i)X("scroll",A,h,!0)},l=()=>{for(const u of i)R("scroll",u,h,!0);i=[]},s=new Set,d=u=>{s.size===0&&a(),s.has(u)||s.add(u)},c=u=>{s.has(u)&&s.delete(u),s.size===0&&l()},h=()=>{mr(f)},f=()=>{s.forEach(u=>u())},b=new Set,m=u=>{b.size===0&&X("resize",window,p),b.has(u)||b.add(u)},g=u=>{b.has(u)&&b.delete(u),b.size===0&&R("resize",window,p)},p=()=>{b.forEach(u=>u())};return de(()=>{R("resize",window,p),l()}),{targetRef:r,setTargetRef:o,addScrollListener:d,removeScrollListener:c,addResizeListener:m,removeResizeListener:g}},render(){return Qe("binder",this.$slots)}}),hn=U({name:"Target",setup(){const{setTargetRef:e,syncTarget:t}=V("VBinder");return{syncTarget:t,setTargetDirective:{mounted:e,updated:e}}},render(){const{syncTarget:e,setTargetDirective:t}=this;return e?Ne(At("follower",this.$slots),[[t]]):At("follower",this.$slots)}}),ce="@@mmoContext",Mr={mounted(e,{value:t}){e[ce]={handler:void 0},typeof t=="function"&&(e[ce].handler=t,X("mousemoveoutside",e,t))},updated(e,{value:t}){const n=e[ce];typeof t=="function"?n.handler?n.handler!==t&&(R("mousemoveoutside",e,n.handler),n.handler=t,X("mousemoveoutside",e,t)):(e[ce].handler=t,X("mousemoveoutside",e,t)):n.handler&&(R("mousemoveoutside",e,n.handler),n.handler=void 0)},unmounted(e){const{handler:t}=e[ce];t&&R("mousemoveoutside",e,t),e[ce].handler=void 0}},fe="@@coContext",Pt={mounted(e,{value:t,modifiers:n}){e[fe]={handler:void 0},typeof t=="function"&&(e[fe].handler=t,X("clickoutside",e,t,{capture:n.capture}))},updated(e,{value:t,modifiers:n}){const r=e[fe];typeof t=="function"?r.handler?r.handler!==t&&(R("clickoutside",e,r.handler,{capture:n.capture}),r.handler=t,X("clickoutside",e,t,{capture:n.capture})):(e[fe].handler=t,X("clickoutside",e,t,{capture:n.capture})):r.handler&&(R("clickoutside",e,r.handler,{capture:n.capture}),r.handler=void 0)},unmounted(e,{modifiers:t}){const{handler:n}=e[fe];n&&R("clickoutside",e,n,{capture:t.capture}),e[fe].handler=void 0}};function Er(e,t){console.error(`[vdirs/${e}]: ${t}`)}class Tr{constructor(){this.elementZIndex=new Map,this.nextZIndex=2e3}get elementCount(){return this.elementZIndex.size}ensureZIndex(t,n){const{elementZIndex:r}=this;if(n!==void 0){t.style.zIndex=`${n}`,r.delete(t);return}const{nextZIndex:o}=this;r.has(t)&&r.get(t)+1===this.nextZIndex||(t.style.zIndex=`${o}`,r.set(t,o),this.nextZIndex=o+1,this.squashState())}unregister(t,n){const{elementZIndex:r}=this;r.has(t)?r.delete(t):n===void 0&&Er("z-index-manager/unregister-element","Element not found when unregistering."),this.squashState()}squashState(){const{elementCount:t}=this;t||(this.nextZIndex=2e3),this.nextZIndex-t>2500&&this.rearrange()}rearrange(){const t=Array.from(this.elementZIndex.entries());t.sort((n,r)=>n[1]-r[1]),this.nextZIndex=2e3,t.forEach(n=>{const r=n[0],o=this.nextZIndex++;`${o}`!==r.style.zIndex&&(r.style.zIndex=`${o}`)})}}const je=new Tr,he="@@ziContext",pn={mounted(e,t){const{value:n={}}=t,{zIndex:r,enabled:o}=n;e[he]={enabled:!!o,initialized:!1},o&&(je.ensureZIndex(e,r),e[he].initialized=!0)},updated(e,t){const{value:n={}}=t,{zIndex:r,enabled:o}=n,i=e[he].enabled;o&&!i&&(je.ensureZIndex(e,r),e[he].initialized=!0),e[he].enabled=!!o},unmounted(e,t){if(!e[he].initialized)return;const{value:n={}}=t,{zIndex:r}=n;je.unregister(e,r)}},{c:we}=Wn(),vn="vueuc-style";function Ot(e){return typeof e=="string"?document.querySelector(e):e()}const kr=U({name:"LazyTeleport",props:{to:{type:[String,Object],default:void 0},disabled:Boolean,show:{type:Boolean,required:!0}},setup(e){return{showTeleport:cr(j(e,"show")),mergedTo:B(()=>{const{to:t}=e;return t??"body"})}},render(){return this.showTeleport?this.disabled?Qe("lazy-teleport",this.$slots):w(Hn,{disabled:this.disabled,to:this.mergedTo},Qe("lazy-teleport",this.$slots)):null}}),Ae={top:"bottom",bottom:"top",left:"right",right:"left"},It={start:"end",center:"center",end:"start"},Ge={top:"height",bottom:"height",left:"width",right:"width"},_r={"bottom-start":"top left",bottom:"top center","bottom-end":"top right","top-start":"bottom left",top:"bottom center","top-end":"bottom right","right-start":"top left",right:"center left","right-end":"bottom left","left-start":"top right",left:"center right","left-end":"bottom right"},Nr={"bottom-start":"bottom left",bottom:"bottom center","bottom-end":"bottom right","top-start":"top left",top:"top center","top-end":"top right","right-start":"top right",right:"center right","right-end":"bottom right","left-start":"top left",left:"center left","left-end":"bottom left"},Br={"bottom-start":"right","bottom-end":"left","top-start":"right","top-end":"left","right-start":"bottom","right-end":"top","left-start":"bottom","left-end":"top"},Mt={top:!0,bottom:!1,left:!0,right:!1},Et={top:"end",bottom:"start",left:"end",right:"start"};function zr(e,t,n,r,o,i){if(!o||i)return{placement:e,top:0,left:0};const[a,l]=e.split("-");let s=l??"center",d={top:0,left:0};const c=(b,m,g)=>{let p=0,u=0;const A=n[b]-t[m]-t[b];return A>0&&r&&(g?u=Mt[m]?A:-A:p=Mt[m]?A:-A),{left:p,top:u}},h=a==="left"||a==="right";if(s!=="center"){const b=Br[e],m=Ae[b],g=Ge[b];if(n[g]>t[g]){if(t[b]+t[g]<n[g]){const p=(n[g]-t[g])/2;t[b]<p||t[m]<p?t[b]<t[m]?(s=It[l],d=c(g,m,h)):d=c(g,b,h):s="center"}}else n[g]<t[g]&&t[m]<0&&t[b]>t[m]&&(s=It[l])}else{const b=a==="bottom"||a==="top"?"left":"top",m=Ae[b],g=Ge[b],p=(n[g]-t[g])/2;(t[b]<p||t[m]<p)&&(t[b]>t[m]?(s=Et[b],d=c(g,b,h)):(s=Et[m],d=c(g,m,h)))}let f=a;return t[a]<n[Ge[a]]&&t[a]<t[Ae[a]]&&(f=Ae[a]),{placement:s!=="center"?`${f}-${s}`:f,left:d.left,top:d.top}}function Fr(e,t){return t?Nr[e]:_r[e]}function Lr(e,t,n,r,o,i){if(i)switch(e){case"bottom-start":return{top:`${Math.round(n.top-t.top+n.height)}px`,left:`${Math.round(n.left-t.left)}px`,transform:"translateY(-100%)"};case"bottom-end":return{top:`${Math.round(n.top-t.top+n.height)}px`,left:`${Math.round(n.left-t.left+n.width)}px`,transform:"translateX(-100%) translateY(-100%)"};case"top-start":return{top:`${Math.round(n.top-t.top)}px`,left:`${Math.round(n.left-t.left)}px`,transform:""};case"top-end":return{top:`${Math.round(n.top-t.top)}px`,left:`${Math.round(n.left-t.left+n.width)}px`,transform:"translateX(-100%)"};case"right-start":return{top:`${Math.round(n.top-t.top)}px`,left:`${Math.round(n.left-t.left+n.width)}px`,transform:"translateX(-100%)"};case"right-end":return{top:`${Math.round(n.top-t.top+n.height)}px`,left:`${Math.round(n.left-t.left+n.width)}px`,transform:"translateX(-100%) translateY(-100%)"};case"left-start":return{top:`${Math.round(n.top-t.top)}px`,left:`${Math.round(n.left-t.left)}px`,transform:""};case"left-end":return{top:`${Math.round(n.top-t.top+n.height)}px`,left:`${Math.round(n.left-t.left)}px`,transform:"translateY(-100%)"};case"top":return{top:`${Math.round(n.top-t.top)}px`,left:`${Math.round(n.left-t.left+n.width/2)}px`,transform:"translateX(-50%)"};case"right":return{top:`${Math.round(n.top-t.top+n.height/2)}px`,left:`${Math.round(n.left-t.left+n.width)}px`,transform:"translateX(-100%) translateY(-50%)"};case"left":return{top:`${Math.round(n.top-t.top+n.height/2)}px`,left:`${Math.round(n.left-t.left)}px`,transform:"translateY(-50%)"};case"bottom":default:return{top:`${Math.round(n.top-t.top+n.height)}px`,left:`${Math.round(n.left-t.left+n.width/2)}px`,transform:"translateX(-50%) translateY(-100%)"}}switch(e){case"bottom-start":return{top:`${Math.round(n.top-t.top+n.height+r)}px`,left:`${Math.round(n.left-t.left+o)}px`,transform:""};case"bottom-end":return{top:`${Math.round(n.top-t.top+n.height+r)}px`,left:`${Math.round(n.left-t.left+n.width+o)}px`,transform:"translateX(-100%)"};case"top-start":return{top:`${Math.round(n.top-t.top+r)}px`,left:`${Math.round(n.left-t.left+o)}px`,transform:"translateY(-100%)"};case"top-end":return{top:`${Math.round(n.top-t.top+r)}px`,left:`${Math.round(n.left-t.left+n.width+o)}px`,transform:"translateX(-100%) translateY(-100%)"};case"right-start":return{top:`${Math.round(n.top-t.top+r)}px`,left:`${Math.round(n.left-t.left+n.width+o)}px`,transform:""};case"right-end":return{top:`${Math.round(n.top-t.top+n.height+r)}px`,left:`${Math.round(n.left-t.left+n.width+o)}px`,transform:"translateY(-100%)"};case"left-start":return{top:`${Math.round(n.top-t.top+r)}px`,left:`${Math.round(n.left-t.left+o)}px`,transform:"translateX(-100%)"};case"left-end":return{top:`${Math.round(n.top-t.top+n.height+r)}px`,left:`${Math.round(n.left-t.left+o)}px`,transform:"translateX(-100%) translateY(-100%)"};case"top":return{top:`${Math.round(n.top-t.top+r)}px`,left:`${Math.round(n.left-t.left+n.width/2+o)}px`,transform:"translateY(-100%) translateX(-50%)"};case"right":return{top:`${Math.round(n.top-t.top+n.height/2+r)}px`,left:`${Math.round(n.left-t.left+n.width+o)}px`,transform:"translateY(-50%)"};case"left":return{top:`${Math.round(n.top-t.top+n.height/2+r)}px`,left:`${Math.round(n.left-t.left+o)}px`,transform:"translateY(-50%) translateX(-100%)"};case"bottom":default:return{top:`${Math.round(n.top-t.top+n.height+r)}px`,left:`${Math.round(n.left-t.left+n.width/2+o)}px`,transform:"translateX(-50%)"}}}const Kr=we([we(".v-binder-follower-container",{position:"absolute",left:"0",right:"0",top:"0",height:"0",pointerEvents:"none",zIndex:"auto"}),we(".v-binder-follower-content",{position:"absolute",zIndex:"auto"},[we("> *",{pointerEvents:"all"})])]),gn=U({name:"Follower",inheritAttrs:!1,props:{show:Boolean,enabled:{type:Boolean,default:void 0},placement:{type:String,default:"bottom"},syncTrigger:{type:Array,default:["resize","scroll"]},to:[String,Object],flip:{type:Boolean,default:!0},internalShift:Boolean,x:Number,y:Number,width:String,minWidth:String,containerClass:String,teleportDisabled:Boolean,zindexable:{type:Boolean,default:!0},zIndex:Number,overlap:Boolean},setup(e){const t=V("VBinder"),n=re(()=>e.enabled!==void 0?e.enabled:e.show),r=z(null),o=z(null),i=()=>{const{syncTrigger:f}=e;f.includes("scroll")&&t.addScrollListener(s),f.includes("resize")&&t.addResizeListener(s)},a=()=>{t.removeScrollListener(s),t.removeResizeListener(s)};$e(()=>{n.value&&(s(),i())});const l=qt();Kr.mount({id:"vueuc/binder",head:!0,anchorMetaName:vn,ssr:l}),de(()=>{a()}),Sr(()=>{n.value&&s()});const s=()=>{if(!n.value)return;const f=r.value;if(f===null)return;const b=t.targetRef,{x:m,y:g,overlap:p}=e,u=m!==void 0&&g!==void 0?Or(m,g):He(b);f.style.setProperty("--v-target-width",`${Math.round(u.width)}px`),f.style.setProperty("--v-target-height",`${Math.round(u.height)}px`);const{width:A,minWidth:O,placement:y,internalShift:x,flip:_}=e;f.setAttribute("v-placement",y),p?f.setAttribute("v-overlap",""):f.removeAttribute("v-overlap");const{style:$}=f;A==="target"?$.width=`${u.width}px`:A!==void 0?$.width=A:$.width="",O==="target"?$.minWidth=`${u.width}px`:O!==void 0?$.minWidth=O:$.minWidth="";const I=He(f),M=He(o.value),{left:S,top:L,placement:P}=zr(y,u,I,x,_,p),k=Fr(P,p),{left:E,top:v,transform:T}=Lr(P,M,u,L,S,p);f.setAttribute("v-placement",P),f.style.setProperty("--v-offset-left",`${Math.round(S)}px`),f.style.setProperty("--v-offset-top",`${Math.round(L)}px`),f.style.transform=`translateX(${E}) translateY(${v}) ${T}`,f.style.setProperty("--v-transform-origin",k),f.style.transformOrigin=k};ne(n,f=>{f?(i(),d()):a()});const d=()=>{Jt().then(s).catch(f=>console.error(f))};["placement","x","y","internalShift","flip","width","overlap","minWidth"].forEach(f=>{ne(j(e,f),s)}),["teleportDisabled"].forEach(f=>{ne(j(e,f),d)}),ne(j(e,"syncTrigger"),f=>{f.includes("resize")?t.addResizeListener(s):t.removeResizeListener(s),f.includes("scroll")?t.addScrollListener(s):t.removeScrollListener(s)});const c=Yt(),h=re(()=>{const{to:f}=e;if(f!==void 0)return f;c.value});return{VBinder:t,mergedEnabled:n,offsetContainerRef:o,followerRef:r,mergedTo:h,syncPosition:s}},render(){return w(kr,{show:this.show,to:this.mergedTo,disabled:this.teleportDisabled},{default:()=>{var e,t;const n=w("div",{class:["v-binder-follower-container",this.containerClass],ref:"offsetContainerRef"},[w("div",{class:"v-binder-follower-content",ref:"followerRef"},(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e))]);return this.zindexable?Ne(n,[[pn,{enabled:this.mergedEnabled,zIndex:this.zIndex}]]):n}})}}),ee="v-hidden",Dr=we("[v-hidden]",{display:"none!important"}),aa=U({name:"Overflow",props:{getCounter:Function,getTail:Function,updateCounter:Function,onUpdateCount:Function,onUpdateOverflow:Function},setup(e,{slots:t}){const n=z(null),r=z(null);function o(a){const{value:l}=n,{getCounter:s,getTail:d}=e;let c;if(s!==void 0?c=s():c=r.value,!l||!c)return;c.hasAttribute(ee)&&c.removeAttribute(ee);const{children:h}=l;if(a.showAllItemsBeforeCalculate)for(const O of h)O.hasAttribute(ee)&&O.removeAttribute(ee);const f=l.offsetWidth,b=[],m=t.tail?d?.():null;let g=m?m.offsetWidth:0,p=!1;const u=l.children.length-(t.tail?1:0);for(let O=0;O<u-1;++O){if(O<0)continue;const y=h[O];if(p){y.hasAttribute(ee)||y.setAttribute(ee,"");continue}else y.hasAttribute(ee)&&y.removeAttribute(ee);const x=y.offsetWidth;if(g+=x,b[O]=x,g>f){const{updateCounter:_}=e;for(let $=O;$>=0;--$){const I=u-1-$;_!==void 0?_(I):c.textContent=`${I}`;const M=c.offsetWidth;if(g-=b[$],g+M<=f||$===0){p=!0,O=$-1,m&&(O===-1?(m.style.maxWidth=`${f-M}px`,m.style.boxSizing="border-box"):m.style.maxWidth="");const{onUpdateCount:S}=e;S&&S(I);break}}}}const{onUpdateOverflow:A}=e;p?A!==void 0&&A(!0):(A!==void 0&&A(!1),c.setAttribute(ee,""))}const i=qt();return Dr.mount({id:"vueuc/overflow",head:!0,anchorMetaName:vn,ssr:i}),$e(()=>o({showAllItemsBeforeCalculate:!1})),{selfRef:n,counterRef:r,sync:o}},render(){const{$slots:e}=this;return Jt(()=>this.sync({showAllItemsBeforeCalculate:!1})),w("div",{class:"v-overflow",ref:"selfRef"},[jn(e,"default"),e.counter?e.counter():w("span",{style:{display:"inline-block"},ref:"counterRef"}),e.tail?e.tail():null])}});function bn(e){return e instanceof HTMLElement}function mn(e){for(let t=0;t<e.childNodes.length;t++){const n=e.childNodes[t];if(bn(n)&&(wn(n)||mn(n)))return!0}return!1}function yn(e){for(let t=e.childNodes.length-1;t>=0;t--){const n=e.childNodes[t];if(bn(n)&&(wn(n)||yn(n)))return!0}return!1}function wn(e){if(!Wr(e))return!1;try{e.focus({preventScroll:!0})}catch{}return document.activeElement===e}function Wr(e){if(e.tabIndex>0||e.tabIndex===0&&e.getAttribute("tabIndex")!==null)return!0;if(e.getAttribute("disabled"))return!1;switch(e.nodeName){case"A":return!!e.href&&e.rel!=="ignore";case"INPUT":return e.type!=="hidden"&&e.type!=="file";case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}}let ye=[];const Hr=U({name:"FocusTrap",props:{disabled:Boolean,active:Boolean,autoFocus:{type:Boolean,default:!0},onEsc:Function,initialFocusTo:String,finalFocusTo:String,returnFocusOnDeactivated:{type:Boolean,default:!0}},setup(e){const t=Gn(),n=z(null),r=z(null);let o=!1,i=!1;const a=typeof document>"u"?null:document.activeElement;function l(){return ye[ye.length-1]===t}function s(p){var u;p.code==="Escape"&&l()&&((u=e.onEsc)===null||u===void 0||u.call(e,p))}$e(()=>{ne(()=>e.active,p=>{p?(h(),X("keydown",document,s)):(R("keydown",document,s),o&&f())},{immediate:!0})}),de(()=>{R("keydown",document,s),o&&f()});function d(p){if(!i&&l()){const u=c();if(u===null||u.contains(Je(p)))return;b("first")}}function c(){const p=n.value;if(p===null)return null;let u=p;for(;u=u.nextSibling,!(u===null||u instanceof Element&&u.tagName==="DIV"););return u}function h(){var p;if(!e.disabled){if(ye.push(t),e.autoFocus){const{initialFocusTo:u}=e;u===void 0?b("first"):(p=Ot(u))===null||p===void 0||p.focus({preventScroll:!0})}o=!0,document.addEventListener("focus",d,!0)}}function f(){var p;if(e.disabled||(document.removeEventListener("focus",d,!0),ye=ye.filter(A=>A!==t),l()))return;const{finalFocusTo:u}=e;u!==void 0?(p=Ot(u))===null||p===void 0||p.focus({preventScroll:!0}):e.returnFocusOnDeactivated&&a instanceof HTMLElement&&(i=!0,a.focus({preventScroll:!0}),i=!1)}function b(p){if(l()&&e.active){const u=n.value,A=r.value;if(u!==null&&A!==null){const O=c();if(O==null||O===A){i=!0,u.focus({preventScroll:!0}),i=!1;return}i=!0;const y=p==="first"?mn(O):yn(O);i=!1,y||(i=!0,u.focus({preventScroll:!0}),i=!1)}}}function m(p){if(i)return;const u=c();u!==null&&(p.relatedTarget!==null&&u.contains(p.relatedTarget)?b("last"):b("first"))}function g(p){i||(p.relatedTarget!==null&&p.relatedTarget===n.value?b("last"):b("first"))}return{focusableStartRef:n,focusableEndRef:r,focusableStyle:"position: absolute; height: 0; width: 0;",handleStartFocus:m,handleEndFocus:g}},render(){const{default:e}=this.$slots;if(e===void 0)return null;if(this.disabled)return e();const{active:t,focusableStyle:n}=this;return w(Ce,null,[w("div",{"aria-hidden":"true",tabindex:t?"0":"-1",ref:"focusableStartRef",style:n,onFocus:this.handleStartFocus}),e(),w("div",{"aria-hidden":"true",style:n,ref:"focusableEndRef",tabindex:t?"0":"-1",onFocus:this.handleEndFocus})])}});let Re;function jr(){return Re===void 0&&(Re=navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom")),Re}function Gr(e){return t=>{t?e.value=t.$el:e.value=null}}function Te(e,t=!0,n=[]){return e.forEach(r=>{if(r!==null){if(typeof r!="object"){(typeof r=="string"||typeof r=="number")&&n.push(Xt(String(r)));return}if(Array.isArray(r)){Te(r,t,n);return}if(r.type===Ce){if(r.children===null)return;Array.isArray(r.children)&&Te(r.children,t,n)}else{if(r.type===Zt&&t)return;n.push(r)}}}),n}function Rr(e,t="default",n=void 0){const r=e[t];if(!r)return Ie("getFirstSlotVNode",`slot[${t}] is empty`),null;const o=Te(r(n));return o.length===1?o[0]:(Ie("getFirstSlotVNode",`slot[${t}] should have exactly one child`),null)}function sa(e,t,n){if(!t)return null;const r=Te(t(n));return r.length===1?r[0]:(Ie("getFirstSlotVNode",`slot[${e}] should have exactly one child`),null)}function xn(e,t=[],n){const r={};return t.forEach(o=>{r[o]=e[o]}),Object.assign(r,n)}var tt=Be(ze,"WeakMap"),Ur=Rn(Object.keys,Object),Vr=Object.prototype,Xr=Vr.hasOwnProperty;function Zr(e){if(!Un(e))return Ur(e);var t=[];for(var n in Object(e))Xr.call(e,n)&&n!="constructor"&&t.push(n);return t}function pt(e){return st(e)?Vn(e):Zr(e)}function qr(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}function Yr(e,t){for(var n=-1,r=e==null?0:e.length,o=0,i=[];++n<r;){var a=e[n];t(a,n,e)&&(i[o++]=a)}return i}function Jr(){return[]}var Qr=Object.prototype,eo=Qr.propertyIsEnumerable,Tt=Object.getOwnPropertySymbols,to=Tt?function(e){return e==null?[]:(e=Object(e),Yr(Tt(e),function(t){return eo.call(e,t)}))}:Jr;function no(e,t,n){var r=t(e);return ve(e)?r:qr(r,n(e))}function kt(e){return no(e,pt,to)}var nt=Be(ze,"DataView"),rt=Be(ze,"Promise"),ot=Be(ze,"Set"),_t="[object Map]",ro="[object Object]",Nt="[object Promise]",Bt="[object Set]",zt="[object WeakMap]",Ft="[object DataView]",oo=be(nt),io=be(Ye),ao=be(rt),so=be(ot),lo=be(tt),ie=Qt;(nt&&ie(new nt(new ArrayBuffer(1)))!=Ft||Ye&&ie(new Ye)!=_t||rt&&ie(rt.resolve())!=Nt||ot&&ie(new ot)!=Bt||tt&&ie(new tt)!=zt)&&(ie=function(e){var t=Qt(e),n=t==ro?e.constructor:void 0,r=n?be(n):"";if(r)switch(r){case oo:return Ft;case io:return _t;case ao:return Nt;case so:return Bt;case lo:return zt}return t});var uo="__lodash_hash_undefined__";function co(e){return this.__data__.set(e,uo),this}function fo(e){return this.__data__.has(e)}function ke(e){var t=-1,n=e==null?0:e.length;for(this.__data__=new Xn;++t<n;)this.add(e[t])}ke.prototype.add=ke.prototype.push=co;ke.prototype.has=fo;function ho(e,t){for(var n=-1,r=e==null?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}function po(e,t){return e.has(t)}var vo=1,go=2;function Sn(e,t,n,r,o,i){var a=n&vo,l=e.length,s=t.length;if(l!=s&&!(a&&s>l))return!1;var d=i.get(e),c=i.get(t);if(d&&c)return d==t&&c==e;var h=-1,f=!0,b=n&go?new ke:void 0;for(i.set(e,t),i.set(t,e);++h<l;){var m=e[h],g=t[h];if(r)var p=a?r(g,m,h,t,e,i):r(m,g,h,e,t,i);if(p!==void 0){if(p)continue;f=!1;break}if(b){if(!ho(t,function(u,A){if(!po(b,A)&&(m===u||o(m,u,n,r,i)))return b.push(A)})){f=!1;break}}else if(!(m===g||o(m,g,n,r,i))){f=!1;break}}return i.delete(e),i.delete(t),f}function bo(e){var t=-1,n=Array(e.size);return e.forEach(function(r,o){n[++t]=[o,r]}),n}function mo(e){var t=-1,n=Array(e.size);return e.forEach(function(r){n[++t]=r}),n}var yo=1,wo=2,xo="[object Boolean]",So="[object Date]",$o="[object Error]",Co="[object Map]",Ao="[object Number]",Po="[object RegExp]",Oo="[object Set]",Io="[object String]",Mo="[object Symbol]",Eo="[object ArrayBuffer]",To="[object DataView]",Lt=yt?yt.prototype:void 0,Ue=Lt?Lt.valueOf:void 0;function ko(e,t,n,r,o,i,a){switch(n){case To:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Eo:return!(e.byteLength!=t.byteLength||!i(new wt(e),new wt(t)));case xo:case So:case Ao:return Zn(+e,+t);case $o:return e.name==t.name&&e.message==t.message;case Po:case Io:return e==t+"";case Co:var l=bo;case Oo:var s=r&yo;if(l||(l=mo),e.size!=t.size&&!s)return!1;var d=a.get(e);if(d)return d==t;r|=wo,a.set(e,t);var c=Sn(l(e),l(t),r,o,i,a);return a.delete(e),c;case Mo:if(Ue)return Ue.call(e)==Ue.call(t)}return!1}var _o=1,No=Object.prototype,Bo=No.hasOwnProperty;function zo(e,t,n,r,o,i){var a=n&_o,l=kt(e),s=l.length,d=kt(t),c=d.length;if(s!=c&&!a)return!1;for(var h=s;h--;){var f=l[h];if(!(a?f in t:Bo.call(t,f)))return!1}var b=i.get(e),m=i.get(t);if(b&&m)return b==t&&m==e;var g=!0;i.set(e,t),i.set(t,e);for(var p=a;++h<s;){f=l[h];var u=e[f],A=t[f];if(r)var O=a?r(A,u,f,t,e,i):r(u,A,f,e,t,i);if(!(O===void 0?u===A||o(u,A,n,r,i):O)){g=!1;break}p||(p=f=="constructor")}if(g&&!p){var y=e.constructor,x=t.constructor;y!=x&&"constructor"in e&&"constructor"in t&&!(typeof y=="function"&&y instanceof y&&typeof x=="function"&&x instanceof x)&&(g=!1)}return i.delete(e),i.delete(t),g}var Fo=1,Kt="[object Arguments]",Dt="[object Array]",Pe="[object Object]",Lo=Object.prototype,Wt=Lo.hasOwnProperty;function Ko(e,t,n,r,o,i){var a=ve(e),l=ve(t),s=a?Dt:ie(e),d=l?Dt:ie(t);s=s==Kt?Pe:s,d=d==Kt?Pe:d;var c=s==Pe,h=d==Pe,f=s==d;if(f&&xt(e)){if(!xt(t))return!1;a=!0,c=!1}if(f&&!c)return i||(i=new Oe),a||qn(e)?Sn(e,t,n,r,o,i):ko(e,t,s,n,r,o,i);if(!(n&Fo)){var b=c&&Wt.call(e,"__wrapped__"),m=h&&Wt.call(t,"__wrapped__");if(b||m){var g=b?e.value():e,p=m?t.value():t;return i||(i=new Oe),o(g,p,n,r,i)}}return f?(i||(i=new Oe),zo(e,t,n,r,o,i)):!1}function vt(e,t,n,r,o){return e===t?!0:e==null||t==null||!St(e)&&!St(t)?e!==e&&t!==t:Ko(e,t,n,r,vt,o)}var Do=1,Wo=2;function Ho(e,t,n,r){var o=n.length,i=o;if(e==null)return!i;for(e=Object(e);o--;){var a=n[o];if(a[2]?a[1]!==e[a[0]]:!(a[0]in e))return!1}for(;++o<i;){a=n[o];var l=a[0],s=e[l],d=a[1];if(a[2]){if(s===void 0&&!(l in e))return!1}else{var c=new Oe,h;if(!(h===void 0?vt(d,s,Do|Wo,r,c):h))return!1}}return!0}function $n(e){return e===e&&!Yn(e)}function jo(e){for(var t=pt(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,$n(o)]}return t}function Cn(e,t){return function(n){return n==null?!1:n[e]===t&&(t!==void 0||e in Object(n))}}function Go(e){var t=jo(e);return t.length==1&&t[0][2]?Cn(t[0][0],t[0][1]):function(n){return n===e||Ho(n,e,t)}}function Ro(e,t){return e!=null&&t in Object(e)}function Uo(e,t,n){t=fr(t,e);for(var r=-1,o=t.length,i=!1;++r<o;){var a=ct(t[r]);if(!(i=e!=null&&n(e,a)))break;e=e[a]}return i||++r!=o?i:(o=e==null?0:e.length,!!o&&Jn(o)&&Qn(a,o)&&(ve(e)||er(e)))}function Vo(e,t){return e!=null&&Uo(e,t,Ro)}var Xo=1,Zo=2;function qo(e,t){return on(e)&&$n(t)?Cn(ct(e),t):function(n){var r=hr(n,e);return r===void 0&&r===t?Vo(n,e):vt(t,r,Xo|Zo)}}function Yo(e){return function(t){return t?.[e]}}function Jo(e){return function(t){return pr(t,e)}}function Qo(e){return on(e)?Yo(ct(e)):Jo(e)}function ei(e){return typeof e=="function"?e:e==null?tr:typeof e=="object"?ve(e)?qo(e[0],e[1]):Go(e):Qo(e)}function ti(e,t){return e&&nr(e,t,pt)}function ni(e,t){return function(n,r){if(n==null)return n;if(!st(n))return e(n,r);for(var o=n.length,i=-1,a=Object(n);++i<o&&r(a[i],i,a)!==!1;);return n}}var ri=ni(ti);function oi(e,t){var n=-1,r=st(e)?Array(e.length):[];return ri(e,function(o,i,a){r[++n]=t(o,i,a)}),r}function ii(e,t){var n=ve(e)?rr:oi;return n(e,ei(t))}function Ht(e){return Array.isArray(e)?e:[e]}const it={STOP:"STOP"};function An(e,t){const n=t(e);e.children!==void 0&&n!==it.STOP&&e.children.forEach(r=>An(r,t))}function ai(e,t={}){const{preserveGroup:n=!1}=t,r=[],o=n?a=>{a.isLeaf||(r.push(a.key),i(a.children))}:a=>{a.isLeaf||(a.isGroup||r.push(a.key),i(a.children))};function i(a){a.forEach(o)}return i(e),r}function si(e,t){const{isLeaf:n}=e;return n!==void 0?n:!t(e)}function li(e){return e.children}function di(e){return e.key}function ui(){return!1}function ci(e,t){const{isLeaf:n}=e;return!(n===!1&&!Array.isArray(t(e)))}function fi(e){return e.disabled===!0}function hi(e,t){return e.isLeaf===!1&&!Array.isArray(t(e))}function Ve(e){var t;return e==null?[]:Array.isArray(e)?e:(t=e.checkedKeys)!==null&&t!==void 0?t:[]}function Xe(e){var t;return e==null||Array.isArray(e)?[]:(t=e.indeterminateKeys)!==null&&t!==void 0?t:[]}function pi(e,t){const n=new Set(e);return t.forEach(r=>{n.has(r)||n.add(r)}),Array.from(n)}function vi(e,t){const n=new Set(e);return t.forEach(r=>{n.has(r)&&n.delete(r)}),Array.from(n)}function gi(e){return e?.type==="group"}function la(e){const t=new Map;return e.forEach((n,r)=>{t.set(n.key,r)}),n=>{var r;return(r=t.get(n))!==null&&r!==void 0?r:null}}class bi extends Error{constructor(){super(),this.message="SubtreeNotLoadedError: checking a subtree whose required nodes are not fully loaded."}}function mi(e,t,n,r){return _e(t.concat(e),n,r,!1)}function yi(e,t){const n=new Set;return e.forEach(r=>{const o=t.treeNodeMap.get(r);if(o!==void 0){let i=o.parent;for(;i!==null&&!(i.disabled||n.has(i.key));)n.add(i.key),i=i.parent}}),n}function wi(e,t,n,r){const o=_e(t,n,r,!1),i=_e(e,n,r,!0),a=yi(e,n),l=[];return o.forEach(s=>{(i.has(s)||a.has(s))&&l.push(s)}),l.forEach(s=>o.delete(s)),o}function Ze(e,t){const{checkedKeys:n,keysToCheck:r,keysToUncheck:o,indeterminateKeys:i,cascade:a,leafOnly:l,checkStrategy:s,allowNotLoaded:d}=e;if(!a)return r!==void 0?{checkedKeys:pi(n,r),indeterminateKeys:Array.from(i)}:o!==void 0?{checkedKeys:vi(n,o),indeterminateKeys:Array.from(i)}:{checkedKeys:Array.from(n),indeterminateKeys:Array.from(i)};const{levelTreeNodeMap:c}=t;let h;o!==void 0?h=wi(o,n,t,d):r!==void 0?h=mi(r,n,t,d):h=_e(n,t,d,!1);const f=s==="parent",b=s==="child"||l,m=h,g=new Set,p=Math.max.apply(null,Array.from(c.keys()));for(let u=p;u>=0;u-=1){const A=u===0,O=c.get(u);for(const y of O){if(y.isLeaf)continue;const{key:x,shallowLoaded:_}=y;if(b&&_&&y.children.forEach(S=>{!S.disabled&&!S.isLeaf&&S.shallowLoaded&&m.has(S.key)&&m.delete(S.key)}),y.disabled||!_)continue;let $=!0,I=!1,M=!0;for(const S of y.children){const L=S.key;if(!S.disabled){if(M&&(M=!1),m.has(L))I=!0;else if(g.has(L)){I=!0,$=!1;break}else if($=!1,I)break}}$&&!M?(f&&y.children.forEach(S=>{!S.disabled&&m.has(S.key)&&m.delete(S.key)}),m.add(x)):I&&g.add(x),A&&b&&m.has(x)&&m.delete(x)}}return{checkedKeys:Array.from(m),indeterminateKeys:Array.from(g)}}function _e(e,t,n,r){const{treeNodeMap:o,getChildren:i}=t,a=new Set,l=new Set(e);return e.forEach(s=>{const d=o.get(s);d!==void 0&&An(d,c=>{if(c.disabled)return it.STOP;const{key:h}=c;if(!a.has(h)&&(a.add(h),l.add(h),hi(c.rawNode,i))){if(r)return it.STOP;if(!n)throw new bi}})}),l}function xi(e,{includeGroup:t=!1,includeSelf:n=!0},r){var o;const i=r.treeNodeMap;let a=e==null?null:(o=i.get(e))!==null&&o!==void 0?o:null;const l={keyPath:[],treeNodePath:[],treeNode:a};if(a?.ignored)return l.treeNode=null,l;for(;a;)!a.ignored&&(t||!a.isGroup)&&l.treeNodePath.push(a),a=a.parent;return l.treeNodePath.reverse(),n||l.treeNodePath.pop(),l.keyPath=l.treeNodePath.map(s=>s.key),l}function Si(e){if(e.length===0)return null;const t=e[0];return t.isGroup||t.ignored||t.disabled?t.getNext():t}function $i(e,t){const n=e.siblings,r=n.length,{index:o}=e;return t?n[(o+1)%r]:o===n.length-1?null:n[o+1]}function jt(e,t,{loop:n=!1,includeDisabled:r=!1}={}){const o=t==="prev"?Ci:$i,i={reverse:t==="prev"};let a=!1,l=null;function s(d){if(d!==null){if(d===e){if(!a)a=!0;else if(!e.disabled&&!e.isGroup){l=e;return}}else if((!d.disabled||r)&&!d.ignored&&!d.isGroup){l=d;return}if(d.isGroup){const c=gt(d,i);c!==null?l=c:s(o(d,n))}else{const c=o(d,!1);if(c!==null)s(c);else{const h=Ai(d);h?.isGroup?s(o(h,n)):n&&s(o(d,!0))}}}}return s(e),l}function Ci(e,t){const n=e.siblings,r=n.length,{index:o}=e;return t?n[(o-1+r)%r]:o===0?null:n[o-1]}function Ai(e){return e.parent}function gt(e,t={}){const{reverse:n=!1}=t,{children:r}=e;if(r){const{length:o}=r,i=n?o-1:0,a=n?-1:o,l=n?-1:1;for(let s=i;s!==a;s+=l){const d=r[s];if(!d.disabled&&!d.ignored)if(d.isGroup){const c=gt(d,t);if(c!==null)return c}else return d}}return null}const Pi={getChild(){return this.ignored?null:gt(this)},getParent(){const{parent:e}=this;return e?.isGroup?e.getParent():e},getNext(e={}){return jt(this,"next",e)},getPrev(e={}){return jt(this,"prev",e)}};function Oi(e,t){const n=t?new Set(t):void 0,r=[];function o(i){i.forEach(a=>{r.push(a),!(a.isLeaf||!a.children||a.ignored)&&(a.isGroup||n===void 0||n.has(a.key))&&o(a.children)})}return o(e),r}function Ii(e,t){const n=e.key;for(;t;){if(t.key===n)return!0;t=t.parent}return!1}function Pn(e,t,n,r,o,i=null,a=0){const l=[];return e.forEach((s,d)=>{var c;const h=Object.create(r);if(h.rawNode=s,h.siblings=l,h.level=a,h.index=d,h.isFirstChild=d===0,h.isLastChild=d+1===e.length,h.parent=i,!h.ignored){const f=o(s);Array.isArray(f)&&(h.children=Pn(f,t,n,r,o,h,a+1))}l.push(h),t.set(h.key,h),n.has(a)||n.set(a,[]),(c=n.get(a))===null||c===void 0||c.push(h)}),l}function Mi(e,t={}){var n;const r=new Map,o=new Map,{getDisabled:i=fi,getIgnored:a=ui,getIsGroup:l=gi,getKey:s=di}=t,d=(n=t.getChildren)!==null&&n!==void 0?n:li,c=t.ignoreEmptyChildren?y=>{const x=d(y);return Array.isArray(x)?x.length?x:null:x}:d,h=Object.assign({get key(){return s(this.rawNode)},get disabled(){return i(this.rawNode)},get isGroup(){return l(this.rawNode)},get isLeaf(){return si(this.rawNode,c)},get shallowLoaded(){return ci(this.rawNode,c)},get ignored(){return a(this.rawNode)},contains(y){return Ii(this,y)}},Pi),f=Pn(e,r,o,h,c);function b(y){if(y==null)return null;const x=r.get(y);return x&&!x.isGroup&&!x.ignored?x:null}function m(y){if(y==null)return null;const x=r.get(y);return x&&!x.ignored?x:null}function g(y,x){const _=m(y);return _?_.getPrev(x):null}function p(y,x){const _=m(y);return _?_.getNext(x):null}function u(y){const x=m(y);return x?x.getParent():null}function A(y){const x=m(y);return x?x.getChild():null}const O={treeNodes:f,treeNodeMap:r,levelTreeNodeMap:o,maxLevel:Math.max(...o.keys()),getChildren:c,getFlattenedNodes(y){return Oi(f,y)},getNode:b,getPrev:g,getNext:p,getParent:u,getChild:A,getFirstAvailableNode(){return Si(f)},getPath(y,x={}){return xi(y,x,O)},getCheckedKeys(y,x={}){const{cascade:_=!0,leafOnly:$=!1,checkStrategy:I="all",allowNotLoaded:M=!1}=x;return Ze({checkedKeys:Ve(y),indeterminateKeys:Xe(y),cascade:_,leafOnly:$,checkStrategy:I,allowNotLoaded:M},O)},check(y,x,_={}){const{cascade:$=!0,leafOnly:I=!1,checkStrategy:M="all",allowNotLoaded:S=!1}=_;return Ze({checkedKeys:Ve(x),indeterminateKeys:Xe(x),keysToCheck:y==null?[]:Ht(y),cascade:$,leafOnly:I,checkStrategy:M,allowNotLoaded:S},O)},uncheck(y,x,_={}){const{cascade:$=!0,leafOnly:I=!1,checkStrategy:M="all",allowNotLoaded:S=!1}=_;return Ze({checkedKeys:Ve(x),indeterminateKeys:Xe(x),keysToUncheck:y==null?[]:Ht(y),cascade:$,leafOnly:I,checkStrategy:M,allowNotLoaded:S},O)},getNonLeafKeys(y={}){return ai(f,y)}};return O}const{cubicBezierEaseIn:Gt,cubicBezierEaseOut:Rt}=or;function Ei({transformOrigin:e="inherit",duration:t=".2s",enterScale:n=".9",originalTransform:r="",originalTransition:o=""}={}){return[W("&.fade-in-scale-up-transition-leave-active",{transformOrigin:e,transition:`opacity ${t} ${Gt}, transform ${t} ${Gt} ${o&&`,${o}`}`}),W("&.fade-in-scale-up-transition-enter-active",{transformOrigin:e,transition:`opacity ${t} ${Rt}, transform ${t} ${Rt} ${o&&`,${o}`}`}),W("&.fade-in-scale-up-transition-enter-from, &.fade-in-scale-up-transition-leave-to",{opacity:0,transform:`${r} scale(${n})`}),W("&.fade-in-scale-up-transition-leave-from, &.fade-in-scale-up-transition-enter-to",{opacity:1,transform:`${r} scale(1)`})]}const Ti={space:"6px",spaceArrow:"10px",arrowOffset:"10px",arrowOffsetVertical:"10px",arrowHeight:"6px",padding:"8px 14px"};function ki(e){const{boxShadow2:t,popoverColor:n,textColor2:r,borderRadius:o,fontSize:i,dividerColor:a}=e;return Object.assign(Object.assign({},Ti),{fontSize:i,borderRadius:o,color:n,dividerColor:a,textColor:r,boxShadow:t})}const bt={name:"Popover",common:lt,self:ki},qe={top:"bottom",bottom:"top",left:"right",right:"left"},K="var(--n-arrow-height) * 1.414",_i=W([D("popover",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 position: relative;
 font-size: var(--n-font-size);
 color: var(--n-text-color);
 box-shadow: var(--n-box-shadow);
 word-break: break-word;
 `,[W(">",[D("scrollbar",`
 height: inherit;
 max-height: inherit;
 `)]),xe("raw",`
 background-color: var(--n-color);
 border-radius: var(--n-border-radius);
 `,[xe("scrollable",[xe("show-header-or-footer","padding: var(--n-padding);")])]),Y("header",`
 padding: var(--n-padding);
 border-bottom: 1px solid var(--n-divider-color);
 transition: border-color .3s var(--n-bezier);
 `),Y("footer",`
 padding: var(--n-padding);
 border-top: 1px solid var(--n-divider-color);
 transition: border-color .3s var(--n-bezier);
 `),Z("scrollable, show-header-or-footer",[Y("content",`
 padding: var(--n-padding);
 `)])]),D("popover-shared",`
 transform-origin: inherit;
 `,[D("popover-arrow-wrapper",`
 position: absolute;
 overflow: hidden;
 pointer-events: none;
 `,[D("popover-arrow",`
 transition: background-color .3s var(--n-bezier);
 position: absolute;
 display: block;
 width: calc(${K});
 height: calc(${K});
 box-shadow: 0 0 8px 0 rgba(0, 0, 0, .12);
 transform: rotate(45deg);
 background-color: var(--n-color);
 pointer-events: all;
 `)]),W("&.popover-transition-enter-from, &.popover-transition-leave-to",`
 opacity: 0;
 transform: scale(.85);
 `),W("&.popover-transition-enter-to, &.popover-transition-leave-from",`
 transform: scale(1);
 opacity: 1;
 `),W("&.popover-transition-enter-active",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .15s var(--n-bezier-ease-out),
 transform .15s var(--n-bezier-ease-out);
 `),W("&.popover-transition-leave-active",`
 transition:
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 opacity .15s var(--n-bezier-ease-in),
 transform .15s var(--n-bezier-ease-in);
 `)]),q("top-start",`
 top: calc(${K} / -2);
 left: calc(${te("top-start")} - var(--v-offset-left));
 `),q("top",`
 top: calc(${K} / -2);
 transform: translateX(calc(${K} / -2)) rotate(45deg);
 left: 50%;
 `),q("top-end",`
 top: calc(${K} / -2);
 right: calc(${te("top-end")} + var(--v-offset-left));
 `),q("bottom-start",`
 bottom: calc(${K} / -2);
 left: calc(${te("bottom-start")} - var(--v-offset-left));
 `),q("bottom",`
 bottom: calc(${K} / -2);
 transform: translateX(calc(${K} / -2)) rotate(45deg);
 left: 50%;
 `),q("bottom-end",`
 bottom: calc(${K} / -2);
 right: calc(${te("bottom-end")} + var(--v-offset-left));
 `),q("left-start",`
 left: calc(${K} / -2);
 top: calc(${te("left-start")} - var(--v-offset-top));
 `),q("left",`
 left: calc(${K} / -2);
 transform: translateY(calc(${K} / -2)) rotate(45deg);
 top: 50%;
 `),q("left-end",`
 left: calc(${K} / -2);
 bottom: calc(${te("left-end")} + var(--v-offset-top));
 `),q("right-start",`
 right: calc(${K} / -2);
 top: calc(${te("right-start")} - var(--v-offset-top));
 `),q("right",`
 right: calc(${K} / -2);
 transform: translateY(calc(${K} / -2)) rotate(45deg);
 top: 50%;
 `),q("right-end",`
 right: calc(${K} / -2);
 bottom: calc(${te("right-end")} + var(--v-offset-top));
 `),...ii({top:["right-start","left-start"],right:["top-end","bottom-end"],bottom:["right-end","left-end"],left:["top-start","bottom-start"]},(e,t)=>{const n=["right","left"].includes(t),r=n?"width":"height";return e.map(o=>{const i=o.split("-")[1]==="end",l=`calc((${`var(--v-target-${r}, 0px)`} - ${K}) / 2)`,s=te(o);return W(`[v-placement="${o}"] >`,[D("popover-shared",[Z("center-arrow",[D("popover-arrow",`${t}: calc(max(${l}, ${s}) ${i?"+":"-"} var(--v-offset-${n?"left":"top"}));`)])])])})})]);function te(e){return["top","bottom"].includes(e.split("-")[0])?"var(--n-arrow-offset)":"var(--n-arrow-offset-vertical)"}function q(e,t){const n=e.split("-")[0],r=["top","bottom"].includes(n)?"height: var(--n-space-arrow);":"width: var(--n-space-arrow);";return W(`[v-placement="${e}"] >`,[D("popover-shared",`
 margin-${qe[n]}: var(--n-space);
 `,[Z("show-arrow",`
 margin-${qe[n]}: var(--n-space-arrow);
 `),Z("overlap",`
 margin: 0;
 `),ir("popover-arrow-wrapper",`
 right: 0;
 left: 0;
 top: 0;
 bottom: 0;
 ${n}: 100%;
 ${qe[n]}: auto;
 ${r}
 `,[D("popover-arrow",t)])])])}const On=Object.assign(Object.assign({},ue.props),{to:ge.propTo,show:Boolean,trigger:String,showArrow:Boolean,delay:Number,duration:Number,raw:Boolean,arrowPointToCenter:Boolean,arrowClass:String,arrowStyle:[String,Object],arrowWrapperClass:String,arrowWrapperStyle:[String,Object],displayDirective:String,x:Number,y:Number,flip:Boolean,overlap:Boolean,placement:String,width:[Number,String],keepAliveOnHover:Boolean,scrollable:Boolean,contentClass:String,contentStyle:[Object,String],headerClass:String,headerStyle:[Object,String],footerClass:String,footerStyle:[Object,String],internalDeactivateImmediately:Boolean,animated:Boolean,onClickoutside:Function,internalTrapFocus:Boolean,internalOnAfterLeave:Function,minWidth:Number,maxWidth:Number});function In({arrowClass:e,arrowStyle:t,arrowWrapperClass:n,arrowWrapperStyle:r,clsPrefix:o}){return w("div",{key:"__popover-arrow__",style:r,class:[`${o}-popover-arrow-wrapper`,n]},w("div",{class:[`${o}-popover-arrow`,e],style:t}))}const Ni=U({name:"PopoverBody",inheritAttrs:!1,props:On,setup(e,{slots:t,attrs:n}){const{namespaceRef:r,mergedClsPrefixRef:o,inlineThemeDisabled:i}=dt(e),a=ue("Popover","-popover",_i,bt,e,o),l=z(null),s=V("NPopover"),d=z(null),c=z(e.show),h=z(!1);tn(()=>{const{show:$}=e;$&&!jr()&&!e.internalDeactivateImmediately&&(h.value=!0)});const f=B(()=>{const{trigger:$,onClickoutside:I}=e,M=[],{positionManuallyRef:{value:S}}=s;return S||($==="click"&&!I&&M.push([Pt,y,void 0,{capture:!0}]),$==="hover"&&M.push([Mr,O])),I&&M.push([Pt,y,void 0,{capture:!0}]),(e.displayDirective==="show"||e.animated&&h.value)&&M.push([ar,e.show]),M}),b=B(()=>{const{common:{cubicBezierEaseInOut:$,cubicBezierEaseIn:I,cubicBezierEaseOut:M},self:{space:S,spaceArrow:L,padding:P,fontSize:k,textColor:E,dividerColor:v,color:T,boxShadow:C,borderRadius:N,arrowHeight:G,arrowOffset:H,arrowOffsetVertical:me}}=a.value;return{"--n-box-shadow":C,"--n-bezier":$,"--n-bezier-ease-in":I,"--n-bezier-ease-out":M,"--n-font-size":k,"--n-text-color":E,"--n-color":T,"--n-divider-color":v,"--n-border-radius":N,"--n-arrow-height":G,"--n-arrow-offset":H,"--n-arrow-offset-vertical":me,"--n-padding":P,"--n-space":S,"--n-space-arrow":L}}),m=B(()=>{const $=e.width==="trigger"?void 0:De(e.width),I=[];$&&I.push({width:$});const{maxWidth:M,minWidth:S}=e;return M&&I.push({maxWidth:De(M)}),S&&I.push({maxWidth:De(S)}),i||I.push(b.value),I}),g=i?nn("popover",void 0,b,e):void 0;s.setBodyInstance({syncPosition:p}),de(()=>{s.setBodyInstance(null)}),ne(j(e,"show"),$=>{e.animated||($?c.value=!0:c.value=!1)});function p(){var $;($=l.value)===null||$===void 0||$.syncPosition()}function u($){e.trigger==="hover"&&e.keepAliveOnHover&&e.show&&s.handleMouseEnter($)}function A($){e.trigger==="hover"&&e.keepAliveOnHover&&s.handleMouseLeave($)}function O($){e.trigger==="hover"&&!x().contains(Je($))&&s.handleMouseMoveOutside($)}function y($){(e.trigger==="click"&&!x().contains(Je($))||e.onClickoutside)&&s.handleClickOutside($)}function x(){return s.getTriggerElement()}J(Fe,d),J(ft,null),J(ht,null);function _(){if(g?.onRender(),!(e.displayDirective==="show"||e.show||e.animated&&h.value))return null;let I;const M=s.internalRenderBodyRef.value,{value:S}=o;if(M)I=M([`${S}-popover-shared`,g?.themeClass.value,e.overlap&&`${S}-popover-shared--overlap`,e.showArrow&&`${S}-popover-shared--show-arrow`,e.arrowPointToCenter&&`${S}-popover-shared--center-arrow`],d,m.value,u,A);else{const{value:L}=s.extraClassRef,{internalTrapFocus:P}=e,k=!$t(t.header)||!$t(t.footer),E=()=>{var v,T;const C=k?w(Ce,null,We(t.header,H=>H?w("div",{class:[`${S}-popover__header`,e.headerClass],style:e.headerStyle},H):null),We(t.default,H=>H?w("div",{class:[`${S}-popover__content`,e.contentClass],style:e.contentStyle},t):null),We(t.footer,H=>H?w("div",{class:[`${S}-popover__footer`,e.footerClass],style:e.footerStyle},H):null)):e.scrollable?(v=t.default)===null||v===void 0?void 0:v.call(t):w("div",{class:[`${S}-popover__content`,e.contentClass],style:e.contentStyle},t),N=e.scrollable?w(an,{contentClass:k?void 0:`${S}-popover__content ${(T=e.contentClass)!==null&&T!==void 0?T:""}`,contentStyle:k?void 0:e.contentStyle},{default:()=>C}):C,G=e.showArrow?In({arrowClass:e.arrowClass,arrowStyle:e.arrowStyle,arrowWrapperClass:e.arrowWrapperClass,arrowWrapperStyle:e.arrowWrapperStyle,clsPrefix:S}):null;return[N,G]};I=w("div",ut({class:[`${S}-popover`,`${S}-popover-shared`,g?.themeClass.value,L.map(v=>`${S}-${v}`),{[`${S}-popover--scrollable`]:e.scrollable,[`${S}-popover--show-header-or-footer`]:k,[`${S}-popover--raw`]:e.raw,[`${S}-popover-shared--overlap`]:e.overlap,[`${S}-popover-shared--show-arrow`]:e.showArrow,[`${S}-popover-shared--center-arrow`]:e.arrowPointToCenter}],ref:d,style:m.value,onKeydown:s.handleKeydown,onMouseenter:u,onMouseleave:A},n),P?w(Hr,{active:e.show,autoFocus:!0},{default:E}):E())}return Ne(I,f.value)}return{displayed:h,namespace:r,isMounted:s.isMountedRef,zIndex:s.zIndexRef,followerRef:l,adjustedTo:ge(e),followerEnabled:c,renderContentNode:_}},render(){return w(gn,{ref:"followerRef",zIndex:this.zIndex,show:this.show,enabled:this.followerEnabled,to:this.adjustedTo,x:this.x,y:this.y,flip:this.flip,placement:this.placement,containerClass:this.namespace,overlap:this.overlap,width:this.width==="trigger"?"target":void 0,teleportDisabled:this.adjustedTo===ge.tdkey},{default:()=>this.animated?w(en,{name:"popover-transition",appear:this.isMounted,onEnter:()=>{this.followerEnabled=!0},onAfterLeave:()=>{var e;(e=this.internalOnAfterLeave)===null||e===void 0||e.call(this),this.followerEnabled=!1,this.displayed=!1}},{default:this.renderContentNode}):this.renderContentNode()})}}),Bi=Object.keys(On),zi={focus:["onFocus","onBlur"],click:["onClick"],hover:["onMouseenter","onMouseleave"],manual:[],nested:["onFocus","onBlur","onMouseenter","onMouseleave","onClick"]};function Fi(e,t,n){zi[t].forEach(r=>{e.props?e.props=Object.assign({},e.props):e.props={};const o=e.props[r],i=n[r];o?e.props[r]=(...a)=>{o(...a),i(...a)}:e.props[r]=i})}const Le={show:{type:Boolean,default:void 0},defaultShow:Boolean,showArrow:{type:Boolean,default:!0},trigger:{type:String,default:"hover"},delay:{type:Number,default:100},duration:{type:Number,default:100},raw:Boolean,placement:{type:String,default:"top"},x:Number,y:Number,arrowPointToCenter:Boolean,disabled:Boolean,getDisabled:Function,displayDirective:{type:String,default:"if"},arrowClass:String,arrowStyle:[String,Object],arrowWrapperClass:String,arrowWrapperStyle:[String,Object],flip:{type:Boolean,default:!0},animated:{type:Boolean,default:!0},width:{type:[Number,String],default:void 0},overlap:Boolean,keepAliveOnHover:{type:Boolean,default:!0},zIndex:Number,to:ge.propTo,scrollable:Boolean,contentClass:String,contentStyle:[Object,String],headerClass:String,headerStyle:[Object,String],footerClass:String,footerStyle:[Object,String],onClickoutside:Function,"onUpdate:show":[Function,Array],onUpdateShow:[Function,Array],internalDeactivateImmediately:Boolean,internalSyncTargetWithParent:Boolean,internalInheritedEventHandlers:{type:Array,default:()=>[]},internalTrapFocus:Boolean,internalExtraClass:{type:Array,default:()=>[]},onShow:[Function,Array],onHide:[Function,Array],arrow:{type:Boolean,default:void 0},minWidth:Number,maxWidth:Number},Li=Object.assign(Object.assign(Object.assign({},ue.props),Le),{internalOnAfterLeave:Function,internalRenderBody:Function}),Mn=U({name:"Popover",inheritAttrs:!1,props:Li,slots:Object,__popover__:!0,setup(e){const t=Yt(),n=z(null),r=B(()=>e.show),o=z(e.defaultShow),i=sn(r,o),a=re(()=>e.disabled?!1:i.value),l=()=>{if(e.disabled)return!0;const{getDisabled:v}=e;return!!v?.()},s=()=>l()?!1:i.value,d=$r(e,["arrow","showArrow"]),c=B(()=>e.overlap?!1:d.value);let h=null;const f=z(null),b=z(null),m=re(()=>e.x!==void 0&&e.y!==void 0);function g(v){const{"onUpdate:show":T,onUpdateShow:C,onShow:N,onHide:G}=e;o.value=v,T&&le(T,v),C&&le(C,v),v&&N&&le(N,!0),v&&G&&le(G,!1)}function p(){h&&h.syncPosition()}function u(){const{value:v}=f;v&&(window.clearTimeout(v),f.value=null)}function A(){const{value:v}=b;v&&(window.clearTimeout(v),b.value=null)}function O(){const v=l();if(e.trigger==="focus"&&!v){if(s())return;g(!0)}}function y(){const v=l();if(e.trigger==="focus"&&!v){if(!s())return;g(!1)}}function x(){const v=l();if(e.trigger==="hover"&&!v){if(A(),f.value!==null||s())return;const T=()=>{g(!0),f.value=null},{delay:C}=e;C===0?T():f.value=window.setTimeout(T,C)}}function _(){const v=l();if(e.trigger==="hover"&&!v){if(u(),b.value!==null||!s())return;const T=()=>{g(!1),b.value=null},{duration:C}=e;C===0?T():b.value=window.setTimeout(T,C)}}function $(){_()}function I(v){var T;s()&&(e.trigger==="click"&&(u(),A(),g(!1)),(T=e.onClickoutside)===null||T===void 0||T.call(e,v))}function M(){if(e.trigger==="click"&&!l()){u(),A();const v=!s();g(v)}}function S(v){e.internalTrapFocus&&v.key==="Escape"&&(u(),A(),g(!1))}function L(v){o.value=v}function P(){var v;return(v=n.value)===null||v===void 0?void 0:v.targetRef}function k(v){h=v}return J("NPopover",{getTriggerElement:P,handleKeydown:S,handleMouseEnter:x,handleMouseLeave:_,handleClickOutside:I,handleMouseMoveOutside:$,setBodyInstance:k,positionManuallyRef:m,isMountedRef:t,zIndexRef:j(e,"zIndex"),extraClassRef:j(e,"internalExtraClass"),internalRenderBodyRef:j(e,"internalRenderBody")}),tn(()=>{i.value&&l()&&g(!1)}),{binderInstRef:n,positionManually:m,mergedShowConsideringDisabledProp:a,uncontrolledShow:o,mergedShowArrow:c,getMergedShow:s,setShow:L,handleClick:M,handleMouseEnter:x,handleMouseLeave:_,handleFocus:O,handleBlur:y,syncPosition:p}},render(){var e;const{positionManually:t,$slots:n}=this;let r,o=!1;if(!t&&(r=Rr(n,"trigger"),r)){r=sr(r),r=r.type===lr?w("span",[r]):r;const i={onClick:this.handleClick,onMouseenter:this.handleMouseEnter,onMouseleave:this.handleMouseLeave,onFocus:this.handleFocus,onBlur:this.handleBlur};if(!((e=r.type)===null||e===void 0)&&e.__popover__)o=!0,r.props||(r.props={internalSyncTargetWithParent:!0,internalInheritedEventHandlers:[]}),r.props.internalSyncTargetWithParent=!0,r.props.internalInheritedEventHandlers?r.props.internalInheritedEventHandlers=[i,...r.props.internalInheritedEventHandlers]:r.props.internalInheritedEventHandlers=[i];else{const{internalInheritedEventHandlers:a}=this,l=[i,...a],s={onBlur:d=>{l.forEach(c=>{c.onBlur(d)})},onFocus:d=>{l.forEach(c=>{c.onFocus(d)})},onClick:d=>{l.forEach(c=>{c.onClick(d)})},onMouseenter:d=>{l.forEach(c=>{c.onMouseenter(d)})},onMouseleave:d=>{l.forEach(c=>{c.onMouseleave(d)})}};Fi(r,a?"nested":t?"manual":this.trigger,s)}}return w(fn,{ref:"binderInstRef",syncTarget:!o,syncTargetWithParent:this.internalSyncTargetWithParent},{default:()=>{this.mergedShowConsideringDisabledProp;const i=this.getMergedShow();return[this.internalTrapFocus&&i?Ne(w("div",{style:{position:"fixed",top:0,right:0,bottom:0,left:0}}),[[pn,{enabled:i,zIndex:this.zIndex}]]):null,t?null:w(hn,null,{default:()=>r}),w(Ni,xn(this.$props,Bi,Object.assign(Object.assign({},this.$attrs),{showArrow:this.mergedShowArrow,show:i})),{default:()=>{var a,l;return(l=(a=this.$slots).default)===null||l===void 0?void 0:l.call(a)},header:()=>{var a,l;return(l=(a=this.$slots).header)===null||l===void 0?void 0:l.call(a)},footer:()=>{var a,l;return(l=(a=this.$slots).footer)===null||l===void 0?void 0:l.call(a)}})]}})}}),Ki={padding:"4px 0",optionIconSizeSmall:"14px",optionIconSizeMedium:"16px",optionIconSizeLarge:"16px",optionIconSizeHuge:"18px",optionSuffixWidthSmall:"14px",optionSuffixWidthMedium:"14px",optionSuffixWidthLarge:"16px",optionSuffixWidthHuge:"16px",optionIconSuffixWidthSmall:"32px",optionIconSuffixWidthMedium:"32px",optionIconSuffixWidthLarge:"36px",optionIconSuffixWidthHuge:"36px",optionPrefixWidthSmall:"14px",optionPrefixWidthMedium:"14px",optionPrefixWidthLarge:"16px",optionPrefixWidthHuge:"16px",optionIconPrefixWidthSmall:"36px",optionIconPrefixWidthMedium:"36px",optionIconPrefixWidthLarge:"40px",optionIconPrefixWidthHuge:"40px"};function Di(e){const{primaryColor:t,textColor2:n,dividerColor:r,hoverColor:o,popoverColor:i,invertedColor:a,borderRadius:l,fontSizeSmall:s,fontSizeMedium:d,fontSizeLarge:c,fontSizeHuge:h,heightSmall:f,heightMedium:b,heightLarge:m,heightHuge:g,textColor3:p,opacityDisabled:u}=e;return Object.assign(Object.assign({},Ki),{optionHeightSmall:f,optionHeightMedium:b,optionHeightLarge:m,optionHeightHuge:g,borderRadius:l,fontSizeSmall:s,fontSizeMedium:d,fontSizeLarge:c,fontSizeHuge:h,optionTextColor:n,optionTextColorHover:n,optionTextColorActive:t,optionTextColorChildActive:t,color:i,dividerColor:r,suffixColor:n,prefixColor:n,optionColorHover:o,optionColorActive:dr(t,{alpha:.1}),groupHeaderTextColor:p,optionTextColorInverted:"#BBB",optionTextColorHoverInverted:"#FFF",optionTextColorActiveInverted:"#FFF",optionTextColorChildActiveInverted:"#FFF",colorInverted:a,dividerColorInverted:"#BBB",suffixColorInverted:"#BBB",prefixColorInverted:"#BBB",optionColorHoverInverted:t,optionColorActiveInverted:t,groupHeaderTextColorInverted:"#AAA",optionOpacityDisabled:u})}const Wi=rn({name:"Dropdown",common:lt,peers:{Popover:bt},self:Di}),Hi={padding:"8px 14px"};function ji(e){const{borderRadius:t,boxShadow2:n,baseColor:r}=e;return Object.assign(Object.assign({},Hi),{borderRadius:t,boxShadow:n,color:ur(r,"rgba(0, 0, 0, .85)"),textColor:r})}const Gi=rn({name:"Tooltip",common:lt,peers:{Popover:bt},self:ji}),Ri=Object.assign(Object.assign({},Le),ue.props),da=U({name:"Tooltip",props:Ri,slots:Object,__popover__:!0,setup(e){const{mergedClsPrefixRef:t}=dt(e),n=ue("Tooltip","-tooltip",void 0,Gi,e,t),r=z(null);return Object.assign(Object.assign({},{syncPosition(){r.value.syncPosition()},setShow(i){r.value.setShow(i)}}),{popoverRef:r,mergedTheme:n,popoverThemeOverrides:B(()=>n.value.self)})},render(){const{mergedTheme:e,internalExtraClass:t}=this;return w(Mn,Object.assign(Object.assign({},this.$props),{theme:e.peers.Popover,themeOverrides:e.peerOverrides.Popover,builtinThemeOverrides:this.popoverThemeOverrides,internalExtraClass:t.concat("tooltip"),ref:"popoverRef"}),this.$slots)}}),mt=Q("n-dropdown-menu"),Ke=Q("n-dropdown"),Ut=Q("n-dropdown-option"),En=U({name:"DropdownDivider",props:{clsPrefix:{type:String,required:!0}},render(){return w("div",{class:`${this.clsPrefix}-dropdown-divider`})}}),Ui=U({name:"DropdownGroupHeader",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0}},setup(){const{showIconRef:e,hasSubmenuRef:t}=V(mt),{renderLabelRef:n,labelFieldRef:r,nodePropsRef:o,renderOptionRef:i}=V(Ke);return{labelField:r,showIcon:e,hasSubmenu:t,renderLabel:n,nodeProps:o,renderOption:i}},render(){var e;const{clsPrefix:t,hasSubmenu:n,showIcon:r,nodeProps:o,renderLabel:i,renderOption:a}=this,{rawNode:l}=this.tmNode,s=w("div",Object.assign({class:`${t}-dropdown-option`},o?.(l)),w("div",{class:`${t}-dropdown-option-body ${t}-dropdown-option-body--group`},w("div",{"data-dropdown-option":!0,class:[`${t}-dropdown-option-body__prefix`,r&&`${t}-dropdown-option-body__prefix--show-icon`]},Me(l.icon)),w("div",{class:`${t}-dropdown-option-body__label`,"data-dropdown-option":!0},i?i(l):Me((e=l.title)!==null&&e!==void 0?e:l[this.labelField])),w("div",{class:[`${t}-dropdown-option-body__suffix`,n&&`${t}-dropdown-option-body__suffix--has-submenu`],"data-dropdown-option":!0})));return a?a({node:s,option:l}):s}});function at(e,t){return e.type==="submenu"||e.type===void 0&&e[t]!==void 0}function Vi(e){return e.type==="group"}function Tn(e){return e.type==="divider"}function Xi(e){return e.type==="render"}const kn=U({name:"DropdownOption",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0},parentKey:{type:[String,Number],default:null},placement:{type:String,default:"right-start"},props:Object,scrollable:Boolean},setup(e){const t=V(Ke),{hoverKeyRef:n,keyboardKeyRef:r,lastToggledSubmenuKeyRef:o,pendingKeyPathRef:i,activeKeyPathRef:a,animatedRef:l,mergedShowRef:s,renderLabelRef:d,renderIconRef:c,labelFieldRef:h,childrenFieldRef:f,renderOptionRef:b,nodePropsRef:m,menuPropsRef:g}=t,p=V(Ut,null),u=V(mt),A=V(Fe),O=B(()=>e.tmNode.rawNode),y=B(()=>{const{value:C}=f;return at(e.tmNode.rawNode,C)}),x=B(()=>{const{disabled:C}=e.tmNode;return C}),_=B(()=>{if(!y.value)return!1;const{key:C,disabled:N}=e.tmNode;if(N)return!1;const{value:G}=n,{value:H}=r,{value:me}=o,{value:ae}=i;return G!==null?ae.includes(C):H!==null?ae.includes(C)&&ae[ae.length-1]!==C:me!==null?ae.includes(C):!1}),$=B(()=>r.value===null&&!l.value),I=Pr(_,300,$),M=B(()=>!!p?.enteringSubmenuRef.value),S=z(!1);J(Ut,{enteringSubmenuRef:S});function L(){S.value=!0}function P(){S.value=!1}function k(){const{parentKey:C,tmNode:N}=e;N.disabled||s.value&&(o.value=C,r.value=null,n.value=N.key)}function E(){const{tmNode:C}=e;C.disabled||s.value&&n.value!==C.key&&k()}function v(C){if(e.tmNode.disabled||!s.value)return;const{relatedTarget:N}=C;N&&!Ct({target:N},"dropdownOption")&&!Ct({target:N},"scrollbarRail")&&(n.value=null)}function T(){const{value:C}=y,{tmNode:N}=e;s.value&&!C&&!N.disabled&&(t.doSelect(N.key,N.rawNode),t.doUpdateShow(!1))}return{labelField:h,renderLabel:d,renderIcon:c,siblingHasIcon:u.showIconRef,siblingHasSubmenu:u.hasSubmenuRef,menuProps:g,popoverBody:A,animated:l,mergedShowSubmenu:B(()=>I.value&&!M.value),rawNode:O,hasSubmenu:y,pending:re(()=>{const{value:C}=i,{key:N}=e.tmNode;return C.includes(N)}),childActive:re(()=>{const{value:C}=a,{key:N}=e.tmNode,G=C.findIndex(H=>N===H);return G===-1?!1:G<C.length-1}),active:re(()=>{const{value:C}=a,{key:N}=e.tmNode,G=C.findIndex(H=>N===H);return G===-1?!1:G===C.length-1}),mergedDisabled:x,renderOption:b,nodeProps:m,handleClick:T,handleMouseMove:E,handleMouseEnter:k,handleMouseLeave:v,handleSubmenuBeforeEnter:L,handleSubmenuAfterEnter:P}},render(){var e,t;const{animated:n,rawNode:r,mergedShowSubmenu:o,clsPrefix:i,siblingHasIcon:a,siblingHasSubmenu:l,renderLabel:s,renderIcon:d,renderOption:c,nodeProps:h,props:f,scrollable:b}=this;let m=null;if(o){const A=(e=this.menuProps)===null||e===void 0?void 0:e.call(this,r,r.children);m=w(_n,Object.assign({},A,{clsPrefix:i,scrollable:this.scrollable,tmNodes:this.tmNode.children,parentKey:this.tmNode.key}))}const g={class:[`${i}-dropdown-option-body`,this.pending&&`${i}-dropdown-option-body--pending`,this.active&&`${i}-dropdown-option-body--active`,this.childActive&&`${i}-dropdown-option-body--child-active`,this.mergedDisabled&&`${i}-dropdown-option-body--disabled`],onMousemove:this.handleMouseMove,onMouseenter:this.handleMouseEnter,onMouseleave:this.handleMouseLeave,onClick:this.handleClick},p=h?.(r),u=w("div",Object.assign({class:[`${i}-dropdown-option`,p?.class],"data-dropdown-option":!0},p),w("div",ut(g,f),[w("div",{class:[`${i}-dropdown-option-body__prefix`,a&&`${i}-dropdown-option-body__prefix--show-icon`]},[d?d(r):Me(r.icon)]),w("div",{"data-dropdown-option":!0,class:`${i}-dropdown-option-body__label`},s?s(r):Me((t=r[this.labelField])!==null&&t!==void 0?t:r.title)),w("div",{"data-dropdown-option":!0,class:[`${i}-dropdown-option-body__suffix`,l&&`${i}-dropdown-option-body__suffix--has-submenu`]},this.hasSubmenu?w(vr,null,{default:()=>w(gr,null)}):null)]),this.hasSubmenu?w(fn,null,{default:()=>[w(hn,null,{default:()=>w("div",{class:`${i}-dropdown-offset-container`},w(gn,{show:this.mergedShowSubmenu,placement:this.placement,to:b&&this.popoverBody||void 0,teleportDisabled:!b},{default:()=>w("div",{class:`${i}-dropdown-menu-wrapper`},n?w(en,{onBeforeEnter:this.handleSubmenuBeforeEnter,onAfterEnter:this.handleSubmenuAfterEnter,name:"fade-in-scale-up-transition",appear:!0},{default:()=>m}):m)}))})]}):null);return c?c({node:u,option:r}):u}}),Zi=U({name:"NDropdownGroup",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0},parentKey:{type:[String,Number],default:null}},render(){const{tmNode:e,parentKey:t,clsPrefix:n}=this,{children:r}=e;return w(Ce,null,w(Ui,{clsPrefix:n,tmNode:e,key:e.key}),r?.map(o=>{const{rawNode:i}=o;return i.show===!1?null:Tn(i)?w(En,{clsPrefix:n,key:o.key}):o.isGroup?(Ie("dropdown","`group` node is not allowed to be put in `group` node."),null):w(kn,{clsPrefix:n,tmNode:o,parentKey:t,key:o.key})}))}}),qi=U({name:"DropdownRenderOption",props:{tmNode:{type:Object,required:!0}},render(){const{rawNode:{render:e,props:t}}=this.tmNode;return w("div",t,[e?.()])}}),_n=U({name:"DropdownMenu",props:{scrollable:Boolean,showArrow:Boolean,arrowStyle:[String,Object],clsPrefix:{type:String,required:!0},tmNodes:{type:Array,default:()=>[]},parentKey:{type:[String,Number],default:null}},setup(e){const{renderIconRef:t,childrenFieldRef:n}=V(Ke);J(mt,{showIconRef:B(()=>{const o=t.value;return e.tmNodes.some(i=>{var a;if(i.isGroup)return(a=i.children)===null||a===void 0?void 0:a.some(({rawNode:s})=>o?o(s):s.icon);const{rawNode:l}=i;return o?o(l):l.icon})}),hasSubmenuRef:B(()=>{const{value:o}=n;return e.tmNodes.some(i=>{var a;if(i.isGroup)return(a=i.children)===null||a===void 0?void 0:a.some(({rawNode:s})=>at(s,o));const{rawNode:l}=i;return at(l,o)})})});const r=z(null);return J(ht,null),J(ft,null),J(Fe,r),{bodyRef:r}},render(){const{parentKey:e,clsPrefix:t,scrollable:n}=this,r=this.tmNodes.map(o=>{const{rawNode:i}=o;return i.show===!1?null:Xi(i)?w(qi,{tmNode:o,key:o.key}):Tn(i)?w(En,{clsPrefix:t,key:o.key}):Vi(i)?w(Zi,{clsPrefix:t,tmNode:o,parentKey:e,key:o.key}):w(kn,{clsPrefix:t,tmNode:o,parentKey:e,key:o.key,props:i.props,scrollable:n})});return w("div",{class:[`${t}-dropdown-menu`,n&&`${t}-dropdown-menu--scrollable`],ref:"bodyRef"},n?w(an,{contentClass:`${t}-dropdown-menu__content`},{default:()=>r}):r,this.showArrow?In({clsPrefix:t,arrowStyle:this.arrowStyle,arrowClass:void 0,arrowWrapperClass:void 0,arrowWrapperStyle:void 0}):null)}}),Yi=D("dropdown-menu",`
 transform-origin: var(--v-transform-origin);
 background-color: var(--n-color);
 border-radius: var(--n-border-radius);
 box-shadow: var(--n-box-shadow);
 position: relative;
 transition:
 background-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier);
`,[Ei(),D("dropdown-option",`
 position: relative;
 `,[W("a",`
 text-decoration: none;
 color: inherit;
 outline: none;
 `,[W("&::before",`
 content: "";
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 `)]),D("dropdown-option-body",`
 display: flex;
 cursor: pointer;
 position: relative;
 height: var(--n-option-height);
 line-height: var(--n-option-height);
 font-size: var(--n-font-size);
 color: var(--n-option-text-color);
 transition: color .3s var(--n-bezier);
 `,[W("&::before",`
 content: "";
 position: absolute;
 top: 0;
 bottom: 0;
 left: 4px;
 right: 4px;
 transition: background-color .3s var(--n-bezier);
 border-radius: var(--n-border-radius);
 `),xe("disabled",[Z("pending",`
 color: var(--n-option-text-color-hover);
 `,[Y("prefix, suffix",`
 color: var(--n-option-text-color-hover);
 `),W("&::before","background-color: var(--n-option-color-hover);")]),Z("active",`
 color: var(--n-option-text-color-active);
 `,[Y("prefix, suffix",`
 color: var(--n-option-text-color-active);
 `),W("&::before","background-color: var(--n-option-color-active);")]),Z("child-active",`
 color: var(--n-option-text-color-child-active);
 `,[Y("prefix, suffix",`
 color: var(--n-option-text-color-child-active);
 `)])]),Z("disabled",`
 cursor: not-allowed;
 opacity: var(--n-option-opacity-disabled);
 `),Z("group",`
 font-size: calc(var(--n-font-size) - 1px);
 color: var(--n-group-header-text-color);
 `,[Y("prefix",`
 width: calc(var(--n-option-prefix-width) / 2);
 `,[Z("show-icon",`
 width: calc(var(--n-option-icon-prefix-width) / 2);
 `)])]),Y("prefix",`
 width: var(--n-option-prefix-width);
 display: flex;
 justify-content: center;
 align-items: center;
 color: var(--n-prefix-color);
 transition: color .3s var(--n-bezier);
 z-index: 1;
 `,[Z("show-icon",`
 width: var(--n-option-icon-prefix-width);
 `),D("icon",`
 font-size: var(--n-option-icon-size);
 `)]),Y("label",`
 white-space: nowrap;
 flex: 1;
 z-index: 1;
 `),Y("suffix",`
 box-sizing: border-box;
 flex-grow: 0;
 flex-shrink: 0;
 display: flex;
 justify-content: flex-end;
 align-items: center;
 min-width: var(--n-option-suffix-width);
 padding: 0 8px;
 transition: color .3s var(--n-bezier);
 color: var(--n-suffix-color);
 z-index: 1;
 `,[Z("has-submenu",`
 width: var(--n-option-icon-suffix-width);
 `),D("icon",`
 font-size: var(--n-option-icon-size);
 `)]),D("dropdown-menu","pointer-events: all;")]),D("dropdown-offset-container",`
 pointer-events: none;
 position: absolute;
 left: 0;
 right: 0;
 top: -4px;
 bottom: -4px;
 `)]),D("dropdown-divider",`
 transition: background-color .3s var(--n-bezier);
 background-color: var(--n-divider-color);
 height: 1px;
 margin: 4px 0;
 `),D("dropdown-menu-wrapper",`
 transform-origin: var(--v-transform-origin);
 width: fit-content;
 `),W(">",[D("scrollbar",`
 height: inherit;
 max-height: inherit;
 `)]),xe("scrollable",`
 padding: var(--n-padding);
 `),Z("scrollable",[Y("content",`
 padding: var(--n-padding);
 `)])]),Ji={animated:{type:Boolean,default:!0},keyboard:{type:Boolean,default:!0},size:{type:String,default:"medium"},inverted:Boolean,placement:{type:String,default:"bottom"},onSelect:[Function,Array],options:{type:Array,default:()=>[]},menuProps:Function,showArrow:Boolean,renderLabel:Function,renderIcon:Function,renderOption:Function,nodeProps:Function,labelField:{type:String,default:"label"},keyField:{type:String,default:"key"},childrenField:{type:String,default:"children"},value:[String,Number]},Qi=Object.keys(Le),ea=Object.assign(Object.assign(Object.assign({},Le),Ji),ue.props),ua=U({name:"Dropdown",inheritAttrs:!1,props:ea,setup(e){const t=z(!1),n=sn(j(e,"show"),t),r=B(()=>{const{keyField:P,childrenField:k}=e;return Mi(e.options,{getKey(E){return E[P]},getDisabled(E){return E.disabled===!0},getIgnored(E){return E.type==="divider"||E.type==="render"},getChildren(E){return E[k]}})}),o=B(()=>r.value.treeNodes),i=z(null),a=z(null),l=z(null),s=B(()=>{var P,k,E;return(E=(k=(P=i.value)!==null&&P!==void 0?P:a.value)!==null&&k!==void 0?k:l.value)!==null&&E!==void 0?E:null}),d=B(()=>r.value.getPath(s.value).keyPath),c=B(()=>r.value.getPath(e.value).keyPath),h=re(()=>e.keyboard&&n.value);Cr({keydown:{ArrowUp:{prevent:!0,handler:x},ArrowRight:{prevent:!0,handler:y},ArrowDown:{prevent:!0,handler:_},ArrowLeft:{prevent:!0,handler:O},Enter:{prevent:!0,handler:$},Escape:A}},h);const{mergedClsPrefixRef:f,inlineThemeDisabled:b}=dt(e),m=ue("Dropdown","-dropdown",Yi,Wi,e,f);J(Ke,{labelFieldRef:j(e,"labelField"),childrenFieldRef:j(e,"childrenField"),renderLabelRef:j(e,"renderLabel"),renderIconRef:j(e,"renderIcon"),hoverKeyRef:i,keyboardKeyRef:a,lastToggledSubmenuKeyRef:l,pendingKeyPathRef:d,activeKeyPathRef:c,animatedRef:j(e,"animated"),mergedShowRef:n,nodePropsRef:j(e,"nodeProps"),renderOptionRef:j(e,"renderOption"),menuPropsRef:j(e,"menuProps"),doSelect:g,doUpdateShow:p}),ne(n,P=>{!e.animated&&!P&&u()});function g(P,k){const{onSelect:E}=e;E&&le(E,P,k)}function p(P){const{"onUpdate:show":k,onUpdateShow:E}=e;k&&le(k,P),E&&le(E,P),t.value=P}function u(){i.value=null,a.value=null,l.value=null}function A(){p(!1)}function O(){M("left")}function y(){M("right")}function x(){M("up")}function _(){M("down")}function $(){const P=I();P?.isLeaf&&n.value&&(g(P.key,P.rawNode),p(!1))}function I(){var P;const{value:k}=r,{value:E}=s;return!k||E===null?null:(P=k.getNode(E))!==null&&P!==void 0?P:null}function M(P){const{value:k}=s,{value:{getFirstAvailableNode:E}}=r;let v=null;if(k===null){const T=E();T!==null&&(v=T.key)}else{const T=I();if(T){let C;switch(P){case"down":C=T.getNext();break;case"up":C=T.getPrev();break;case"right":C=T.getChild();break;case"left":C=T.getParent();break}C&&(v=C.key)}}v!==null&&(i.value=null,a.value=v)}const S=B(()=>{const{size:P,inverted:k}=e,{common:{cubicBezierEaseInOut:E},self:v}=m.value,{padding:T,dividerColor:C,borderRadius:N,optionOpacityDisabled:G,[se("optionIconSuffixWidth",P)]:H,[se("optionSuffixWidth",P)]:me,[se("optionIconPrefixWidth",P)]:ae,[se("optionPrefixWidth",P)]:Nn,[se("fontSize",P)]:Bn,[se("optionHeight",P)]:zn,[se("optionIconSize",P)]:Fn}=v,F={"--n-bezier":E,"--n-font-size":Bn,"--n-padding":T,"--n-border-radius":N,"--n-option-height":zn,"--n-option-prefix-width":Nn,"--n-option-icon-prefix-width":ae,"--n-option-suffix-width":me,"--n-option-icon-suffix-width":H,"--n-option-icon-size":Fn,"--n-divider-color":C,"--n-option-opacity-disabled":G};return k?(F["--n-color"]=v.colorInverted,F["--n-option-color-hover"]=v.optionColorHoverInverted,F["--n-option-color-active"]=v.optionColorActiveInverted,F["--n-option-text-color"]=v.optionTextColorInverted,F["--n-option-text-color-hover"]=v.optionTextColorHoverInverted,F["--n-option-text-color-active"]=v.optionTextColorActiveInverted,F["--n-option-text-color-child-active"]=v.optionTextColorChildActiveInverted,F["--n-prefix-color"]=v.prefixColorInverted,F["--n-suffix-color"]=v.suffixColorInverted,F["--n-group-header-text-color"]=v.groupHeaderTextColorInverted):(F["--n-color"]=v.color,F["--n-option-color-hover"]=v.optionColorHover,F["--n-option-color-active"]=v.optionColorActive,F["--n-option-text-color"]=v.optionTextColor,F["--n-option-text-color-hover"]=v.optionTextColorHover,F["--n-option-text-color-active"]=v.optionTextColorActive,F["--n-option-text-color-child-active"]=v.optionTextColorChildActive,F["--n-prefix-color"]=v.prefixColor,F["--n-suffix-color"]=v.suffixColor,F["--n-group-header-text-color"]=v.groupHeaderTextColor),F}),L=b?nn("dropdown",B(()=>`${e.size[0]}${e.inverted?"i":""}`),S,e):void 0;return{mergedClsPrefix:f,mergedTheme:m,tmNodes:o,mergedShow:n,handleAfterLeave:()=>{e.animated&&u()},doUpdateShow:p,cssVars:b?void 0:S,themeClass:L?.themeClass,onRender:L?.onRender}},render(){const e=(r,o,i,a,l)=>{var s;const{mergedClsPrefix:d,menuProps:c}=this;(s=this.onRender)===null||s===void 0||s.call(this);const h=c?.(void 0,this.tmNodes.map(b=>b.rawNode))||{},f={ref:Gr(o),class:[r,`${d}-dropdown`,this.themeClass],clsPrefix:d,tmNodes:this.tmNodes,style:[...i,this.cssVars],showArrow:this.showArrow,arrowStyle:this.arrowStyle,scrollable:this.scrollable,onMouseenter:a,onMouseleave:l};return w(_n,ut(this.$attrs,f,h))},{mergedTheme:t}=this,n={show:this.mergedShow,theme:t.peers.Popover,themeOverrides:t.peerOverrides.Popover,internalOnAfterLeave:this.handleAfterLeave,internalRenderBody:e,onUpdateShow:this.doUpdateShow,"onUpdate:show":void 0};return w(Mn,Object.assign({},xn(this.$props,Qi),n),{trigger:()=>{var r,o;return(o=(r=this.$slots).default)===null||o===void 0?void 0:o.call(r)}})}});export{Fe as A,fn as B,pn as C,oa as D,ra as E,Hr as F,la as G,Ar as H,Gr as I,kr as L,da as N,aa as V,ua as a,Cr as b,Mi as c,Wi as d,hn as e,gn as f,Pt as g,Ei as h,ge as i,Te as j,xn as k,wr as l,we as m,vn as n,mr as o,bt as p,Mn as q,Le as r,Sr as s,Gi as t,$r as u,yr as v,sa as w,ia as x,ht as y,ft as z};
