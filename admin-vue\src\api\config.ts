// API配置
export const API_CONFIG = {
  // 支持环境变量配置，默认值为新的API地址
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://api.myaitts.com',
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
  RETRY_COUNT: parseInt(import.meta.env.VITE_API_RETRY_COUNT || '3')
}

// API端点
export const API_ENDPOINTS = {
  // 认证接口 (新管理员API)
  AUTH_LOGIN: '/api/auth/login',  // 新的登录接口
  AUTH: '/auth',                  // 保留旧接口作为备用

  // 用户管理接口 (新管理员API)
  ADMIN_USERS: '/api/admin/users',           // 获取用户列表
  ADMIN_USER_DETAIL: '/api/admin/users',     // 获取用户详情 (需要拼接username)
  ADMIN_USER_VIP: '/api/admin/users',        // VIP管理 (需要拼接username/vip)

  // 卡密管理接口 (新管理员API)
  ADMIN_CARDS: '/api/admin/cards',           // 获取卡密列表
  ADMIN_CARDS_GENERATE: '/api/admin/cards/generate',  // 生成卡密
  ADMIN_CARDS_PACKAGES: '/api/admin/cards/packages',  // 获取套餐类型

  // 系统统计接口 (新管理员API)
  ADMIN_STATS: '/api/admin/stats',           // 获取系统统计

  // 用量统计接口 (基于文档标准)
  USER_QUOTA: '/api/user/quota',             // 用户配额信息
  USER_STATS: '/api/user/stats',             // 用户详细统计
  USER_PROFILE: '/api/user/profile',         // 用户详细资料

  // 兼容性接口 (保留旧接口作为备用)
  USERS: '/users',                           // 旧用户接口
  USAGE: '/api/admin/users/usage',           // 旧用量接口 (将废弃)
  USAGE_STATS: '/api/admin/users/stats',     // 旧统计接口
  USER_VIP: '/api/admin/users',              // 旧VIP接口
  CARDS: '/cards',                           // 旧卡密接口
  CARD_GENERATE: '',                         // 旧生成接口
  CARD_EDIT: '/edit-card',                   // 编辑接口
  CARD_DELETE: '/delete-card'                // 删除接口
}

// 请求头配置
export const getAuthHeaders = (token?: string) => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  }
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  
  return headers
}
