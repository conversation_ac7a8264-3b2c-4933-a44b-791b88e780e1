// 用户相关类型 (适配新后端格式)
export interface User {
  username: string
  email?: string                    // 新增邮箱字段
  createdAt?: string | number       // 支持ISO字符串和时间戳
  updatedAt?: string | number       // 新增更新时间

  // 新的VIP结构
  vip?: {
    type: string | null             // 支持null值
    expireAt: number
    quotaChars?: number             // VIP配额字符数
    usedChars?: number              // VIP已用字符数
    isExpired?: boolean             // 是否过期
  }

  // 新的用量结构
  usage?: {
    totalChars: number              // 总字符使用量
    monthlyChars: number            // 月度字符使用量
  }

  // 保持向后兼容的旧格式
  createAt?: number                 // 兼容旧字段名
  quota?: {                         // 兼容旧结构
    daily: number
    used: number
    resetAt: number
  }

  // 兼容旧VIP格式
  vip_info?: any
  usage_stats?: any
  created_at?: string | number
  updated_at?: string | number
}

// 用量统计类型 (基于文档标准)
export interface UsageData {
  username: string
  email?: string
  created_at?: string
  createdAt?: string | number
  updatedAt?: string | number
  vip_info?: {
    type: string | null
    expireAt: number
    quotaChars: number
    usedChars?: number
    remainingChars?: number
    usagePercentage?: number
    isExpired?: boolean
  }
  usage_stats?: {
    totalChars: number
    monthlyChars: number
    monthlyResetAt: number
  }
  // 向后兼容的旧格式
  usage?: {
    totalChars: number
    monthlyChars: number
    monthlyResetAt: number
  }
  vip?: {
    type: string | null
    expireAt: number
  }
}

// 卡密类型 (基于API文档标准)
export interface Card {
  id?: number                    // 卡密ID
  code: string                   // 卡密代码
  package_type?: string          // 新格式：套餐类型
  type?: string                  // 兼容旧格式
  status: 'unused' | 'used'      // 卡密状态
  package_info?: {               // 新格式：套餐信息
    type: string
    duration: number
    quotaChars: number
    price: number
    description: string
  }
  created_at?: string            // 新格式：创建时间 (ISO字符串)
  used_at?: string | null        // 新格式：使用时间
  used_by?: string | null        // 新格式：使用者
  userUsage?: {                  // 新格式：用户使用量信息 (仅在已使用且有使用记录时存在)
    totalChars: number           // 历史总使用量
    monthlyChars: number         // 当月使用量
    monthlyResetAt: number       // 月度重置时间戳
  }
  // 向后兼容的旧格式
  usedBy?: string
  activatedAt?: number
  createdAt?: number
  totalChars?: number            // 字符使用总量 (兼容旧格式)
}

// 卡密类型映射
export interface CardType {
  value: string
  label: string
  description?: string
}

// 认证相关类型
export interface AuthRequest {
  // 新API格式
  username: string
  password: string
  // 保持向后兼容
  auth_code?: string
}

export interface AuthResponse {
  success?: boolean  // 旧API格式
  token?: string
  expiresAt?: number
  message?: string
  // 新API格式 (实际后端返回格式)
  access_token?: string
  refresh_token?: string
  expires_in?: number
  username?: string
  // 兼容格式
  refreshToken?: string
  user?: {
    username: string
  }
}

// 分页相关类型
export interface PaginationParams {
  limit?: number
  cursor?: string
}

export interface PaginationResponse<T> {
  data: T[]
  hasMore: boolean
  nextCursor?: string
  total?: number
}

// 排序相关类型
export interface SortState {
  field: string | null
  direction: 'asc' | 'desc'
}

// 筛选相关类型
export interface FilterState {
  searchTerm: string
  activeFilter: string
}

// 统计数据类型 (兼容旧格式)
export interface StatsData {
  totalUsers: number
  totalCharsUsed: number
  monthlyCharsUsed: number
  vipUsersCount: number
  // 新API扩展字段
  newUsers7d?: number
  newUsers30d?: number
  totalTasks?: number
  completedTasks?: number
  failedTasks?: number
}

// 系统统计类型 (新格式，基于文档)
export interface SystemStats {
  users: {
    total_users: string
    active_vip_users: string
    new_users_7d: string
    new_users_30d: string
  }
  tasks: {
    total_tasks: string
    completed_tasks: string
    failed_tasks: string
    processing_tasks: string
    tasks_24h: string
    tasks_7d: string
  }
  cards: {
    total_cards: string
    unused_cards: string
    used_cards: string
    new_cards_7d: string
  }
  taskTrend: Array<{
    date: string
    tasks: number
    completed: number
    failed: number
  }>
  timestamp: string
  totalCards?: number
  unusedCards?: number
  usedCards?: number
}

// 表格列定义类型
export interface TableColumn {
  key: string
  title: string
  sortable?: boolean
  width?: number
  minWidth?: number
  fixed?: 'left' | 'right'
  render?: (row: any) => any
}

// VIP管理相关类型
export interface UpdateVipRequest {
  action: 'set' | 'extend' | 'remove'
  vipType?: string
  expireAt?: number
  extendDays?: number
}

export interface UpdateVipResponse {
  success: boolean
  message?: string
  user?: User
}

export interface VipType {
  value: string
  label: string
  description?: string
  durationDays?: number
}

// 通用API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页参数
export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  offset?: number
}

// 分页响应
export interface PaginationResponse {
  total: number
  limit: number
  offset: number
  hasMore: boolean
}

// 用户列表API响应
export interface UsersApiResponse {
  users: User[]
  pagination: PaginationResponse
}
