import { httpClient } from './http'
import { API_ENDPOINTS } from './config'
import type { User, UsageData, PaginationParams, StatsData, SystemStats, UpdateVipRequest, UpdateVipResponse, UsersApiResponse, PaginationResponse } from '@/types'

// 用户API服务
export class UserService {
  // 获取用户列表 (使用新管理员API)
  static async getUsers(params?: { page?: number; limit?: number; search?: string }): Promise<User[]> {
    try {
      // 构建查询参数
      const queryParams = new URLSearchParams()
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.search) queryParams.append('search', params.search)

      const url = `${API_ENDPOINTS.ADMIN_USERS}${queryParams.toString() ? '?' + queryParams.toString() : ''}`
      console.log('请求用户列表API:', url)

      const response = await httpClient.get<any>(url)
      console.log('用户列表API响应:', response)

      // 处理新API响应格式
      if (response.users || response.success) {
        const users = response.users || response.data?.users || []
        console.log('原始用户数据:', users)

        // 标准化用户数据格式
        const standardizedUsers = users.map((user: any) => {
          // 处理时间字段 - 支持ISO字符串和时间戳
          const parseTime = (timeValue: string | number | undefined): number => {
            if (!timeValue) return 0
            if (typeof timeValue === 'string') {
              return new Date(timeValue).getTime()
            }
            return timeValue
          }

          const standardUser: User = {
            username: user.username,
            email: user.email,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,

            // 新的VIP结构
            vip: user.vip ? {
              type: user.vip.type,
              expireAt: user.vip.expireAt || 0,
              quotaChars: user.vip.quotaChars,
              usedChars: user.vip.usedChars,
              isExpired: user.vip.isExpired
            } : undefined,

            // 新的用量结构
            usage: user.usage ? {
              totalChars: user.usage.totalChars || 0,
              monthlyChars: user.usage.monthlyChars || 0
            } : undefined,

            // 向后兼容字段
            createAt: parseTime(user.createdAt || user.created_at),
            quota: {
              daily: user.vip?.quotaChars || 0,
              used: user.vip?.usedChars || user.usage?.totalChars || 0,
              resetAt: 0
            }
          }

          console.log('标准化用户数据:', standardUser)
          return standardUser
        })

        return standardizedUsers
      }

      throw new Error(response.message || response.error || '获取用户列表失败')
    } catch (error) {
      console.error('新API失败:', error)

      // 如果新API失败，尝试使用旧API作为备用
      try {
        console.log('尝试使用旧API:', API_ENDPOINTS.USERS)
        const fallbackResponse = await httpClient.get<any>(API_ENDPOINTS.USERS)
        console.log('旧API响应:', fallbackResponse)

        if (fallbackResponse.success) {
          const users = fallbackResponse.users || fallbackResponse.data || []
          return users.map((user: any) => ({
            ...user,
            email: user.email || '',
            createdAt: user.createAt,
            usage: {
              totalChars: user.quota?.used || 0,
              monthlyChars: 0
            }
          }))
        }
      } catch (fallbackError) {
        console.error('备用API也失败:', fallbackError)
      }

      throw new Error(error instanceof Error ? error.message : '获取用户列表失败')
    }
  }

  // 获取用量统计数据 (基于标准用户列表接口)
  static async getUsageData(params: PaginationParams & { search?: string, page?: number } = {}): Promise<{
    users: UsageData[]
    hasMore: boolean
    nextCursor?: string
    stats: StatsData
  }> {
    try {
      const queryParams = new URLSearchParams()

      // 使用标准分页参数
      const page = params.page || 1
      const limit = Math.min(params.limit || 50, 100)

      queryParams.append('page', page.toString())
      queryParams.append('limit', limit.toString())

      if (params.search) {
        queryParams.append('search', params.search)
      }

      // 使用标准用户列表接口而不是专用用量接口
      const url = `${API_ENDPOINTS.ADMIN_USERS}${queryParams.toString() ? '?' + queryParams.toString() : ''}`
      console.log('请求用户列表API (含用量数据):', url)
      console.log('请求参数:', params)

      const response = await httpClient.get<any>(url)
      console.log('用户列表API响应:', response)

      // 适配标准用户列表API响应结构
      // 标准响应：{ users: [...], pagination: { page, limit, total, totalPages, hasNext, hasPrev } }
      if (response.users || (response.success && response.data?.users)) {
        const users = response.users || response.data?.users || []
        const pagination = (response as any).pagination || response.data?.pagination || {}

        // 标准化用户数据格式
        const standardizedUsers = this.standardizeUsageData(users)

        // 计算统计数据
        const stats = this.calculateUsageStats(standardizedUsers)

        return {
          users: standardizedUsers,
          hasMore: pagination.hasNext || false,
          nextCursor: pagination.page ? (pagination.page + 1).toString() : undefined,
          stats: stats
        }
      }

      throw new Error(response.message || response.error || '获取用量数据失败')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '获取用量数据失败')
    }
  }

  // 获取系统统计数据 (使用新管理员API)
  static async getSystemStats(): Promise<SystemStats> {
    try {
      console.log('请求系统统计API:', API_ENDPOINTS.ADMIN_STATS)
      const response = await httpClient.get<any>(API_ENDPOINTS.ADMIN_STATS)
      console.log('系统统计API响应:', response)

      // 直接返回符合文档格式的系统统计
      if ((response as any).users && (response as any).tasks && (response as any).cards) {
        return {
          users: (response as any).users,
          tasks: (response as any).tasks,
          cards: (response as any).cards,
          taskTrend: (response as any).taskTrend || [],
          timestamp: (response as any).timestamp || new Date().toISOString()
        }
      }

      throw new Error(response.message || response.error || '获取系统统计失败')
    } catch (error) {
      console.error('系统统计API失败:', error)
      throw new Error(error instanceof Error ? error.message : '获取系统统计失败')
    }
  }

  // 获取全局统计数据 (兼容旧格式，基于系统统计转换)
  static async getGlobalStats(): Promise<StatsData> {
    try {
      // 优先尝试获取系统统计并转换格式
      const systemStats = await this.getSystemStats()

      return {
        totalUsers: parseInt(systemStats.users.total_users) || 0,
        totalCharsUsed: 0, // 系统统计中没有字符使用量，需要从用户数据计算
        monthlyCharsUsed: 0,
        vipUsersCount: parseInt(systemStats.users.active_vip_users) || 0,
        newUsers7d: parseInt(systemStats.users.new_users_7d) || 0,
        newUsers30d: parseInt(systemStats.users.new_users_30d) || 0,
        totalTasks: parseInt(systemStats.tasks.total_tasks) || 0,
        completedTasks: parseInt(systemStats.tasks.completed_tasks) || 0,
        failedTasks: parseInt(systemStats.tasks.failed_tasks) || 0
      }
    } catch (error) {
      // 如果系统统计API失败，尝试通过用量数据计算统计作为备用
      console.warn('系统统计API失败，尝试使用用量数据计算统计:', error)
      try {
        // 获取用量数据并计算统计
        const usageResult = await this.getUsageData({ limit: 1000 })
        return usageResult.stats
      } catch (fallbackError) {
        console.error('备用统计计算也失败:', fallbackError)
        // 返回默认统计数据
        return {
          totalUsers: 0,
          totalCharsUsed: 0,
          monthlyCharsUsed: 0,
          vipUsersCount: 0
        }
      }
    }
  }

  // 标准化用量数据格式 (适配文档格式)
  private static standardizeUsageData(users: any[]): UsageData[] {
    return users.map((user: any) => {
      const parseTime = (timeValue: string | number | undefined): number => {
        if (!timeValue) return 0
        if (typeof timeValue === 'string') {
          return new Date(timeValue).getTime()
        }
        return timeValue
      }

      // 标准化用量数据
      const usage_stats = user.usage_stats || user.usage || {}
      const vip_info = user.vip_info || user.vip || {}

      const standardUser: UsageData = {
        username: user.username,
        email: user.email,
        created_at: user.created_at,
        createdAt: user.createdAt || parseTime(user.created_at),
        updatedAt: user.updatedAt || parseTime(user.updated_at),
        vip_info: {
          type: vip_info.type,
          expireAt: vip_info.expireAt || 0,
          quotaChars: vip_info.quotaChars || 0,
          usedChars: vip_info.usedChars || usage_stats.totalChars || 0,
          remainingChars: vip_info.remainingChars,
          usagePercentage: vip_info.usagePercentage,
          isExpired: vip_info.isExpired
        },
        usage_stats: {
          totalChars: usage_stats.totalChars || 0,
          monthlyChars: usage_stats.monthlyChars || 0,
          monthlyResetAt: usage_stats.monthlyResetAt || 0
        },
        // 向后兼容
        usage: usage_stats,
        vip: vip_info
      }

      return standardUser
    })
  }

  // 计算用量统计数据（保留用于向后兼容）
  private static calculateUsageStats(users: UsageData[]): StatsData {
    let totalCharsUsed = 0
    let monthlyCharsUsed = 0
    let vipUsersCount = 0

    users.forEach(user => {
      // 优先使用新格式，回退到旧格式
      const usage = user.usage_stats || user.usage || {}
      const vip = user.vip_info || user.vip || {}

      totalCharsUsed += (usage as any).totalChars || 0
      monthlyCharsUsed += (usage as any).monthlyChars || 0

      if (vip && (vip as any).expireAt > Date.now()) {
        vipUsersCount++
      }
    })

    return {
      totalUsers: users.length,
      totalCharsUsed,
      monthlyCharsUsed,
      vipUsersCount
    }
  }

  // 更新用户VIP状态 (使用新管理员API)
  static async updateUserVip(username: string, vipData: UpdateVipRequest): Promise<UpdateVipResponse> {
    try {
      const response = await httpClient.put<UpdateVipResponse>(`${API_ENDPOINTS.ADMIN_USER_VIP}/${username}/vip`, vipData)

      if (response.success) {
        return response
      }

      throw new Error(response.message || response.error || 'VIP状态更新失败')
    } catch (error) {
      // 如果新API失败，尝试使用旧API作为备用
      console.warn('新VIP API失败，尝试使用旧API:', error)
      try {
        const fallbackResponse = await httpClient.put<UpdateVipResponse>(`${API_ENDPOINTS.USER_VIP}/${username}/vip`, vipData)
        if (fallbackResponse.success) {
          return fallbackResponse
        }
      } catch (fallbackError) {
        console.error('备用VIP API也失败:', fallbackError)
      }

      throw new Error(error instanceof Error ? error.message : 'VIP状态更新失败')
    }
  }

  // 获取用户详细信息 (新增方法，使用新管理员API)
  static async getUserDetail(username: string): Promise<any> {
    try {
      const response = await httpClient.get<any>(`${API_ENDPOINTS.ADMIN_USER_DETAIL}/${username}`)

      if (response.success || (response as any).user) {
        return (response as any).user || response.data?.user || response
      }

      throw new Error(response.message || response.error || '获取用户详情失败')
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '获取用户详情失败')
    }
  }
}
