<template>
  <div class="card-generator">
    <!-- 生成表单 -->
    <n-card title="生成卡密" size="large">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="top"
        size="large"
      >
        <n-grid :cols="2" :x-gap="24" responsive="screen">
          <n-grid-item :span="2">
            <n-form-item label="卡密类型" path="type">
              <n-select
                v-model:value="formData.type"
                :options="cardTypeOptions"
                placeholder="请选择卡密类型"
                @update:value="handleTypeChange"
              />
            </n-form-item>
          </n-grid-item>
          
          <n-grid-item :span="2">
            <n-form-item label="生成数量" path="count">
              <n-input-number
                v-model:value="formData.count"
                :min="1"
                :max="100"
                placeholder="请输入生成数量"
                style="width: 100%"
              />
              <template #feedback>
                单次最多可生成 100 个卡密 (新API支持批量生成)
              </template>
            </n-form-item>
          </n-grid-item>

          <!-- 新增：自定义卡密选项 -->
          <n-grid-item :span="2">
            <n-form-item label="自定义卡密 (可选)" path="customCode">
              <n-input
                v-model:value="formData.customCode"
                placeholder="输入32位自定义卡密，留空则自动生成"
                maxlength="32"
                show-count
                clearable
              />
              <template #feedback>
                自定义卡密必须是32位字符，仅在生成单个卡密时有效
              </template>
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <div class="form-actions">
          <n-button
            type="primary"
            size="large"
            @click="handleGenerate"
            :loading="isGenerating"
            :disabled="isGenerating"
          >
            <template #icon>
              <n-icon :component="AddOutline" />
            </template>
            生成卡密
          </n-button>
          
          <n-button
            size="large"
            @click="handlePreview"
            :loading="isPreviewLoading"
          >
            <template #icon>
              <n-icon :component="EyeOutline" />
            </template>
            预览格式
          </n-button>
        </div>
      </n-form>
    </n-card>

    <!-- 预览区域 -->
    <n-card v-if="previewCode" title="预览" size="large" style="margin-top: 24px">
      <div class="preview-content">
        <div class="preview-item">
          <strong>卡密号：</strong>{{ previewCode }}
        </div>
        <div class="preview-item">
          <strong>类型：</strong>{{ getCardTypeName(formData.type) }}
        </div>
      </div>
    </n-card>

    <!-- 生成进度 -->
    <n-card v-if="isGenerating" title="生成进度" size="large" style="margin-top: 24px">
      <div class="progress-content">
        <div class="progress-text">
          正在生成: {{ generateProgress }}/{{ generateTotal }}
        </div>
        <n-progress
          type="line"
          :percentage="progressPercentage"
          :show-indicator="false"
          style="margin-top: 12px"
        />
      </div>
    </n-card>

    <!-- 生成结果 -->
    <n-card v-if="generatedCards.length > 0" title="生成结果" size="large" style="margin-top: 24px">
      <div class="result-content">
        <div class="result-list">
          <div
            v-for="(card, index) in generatedCards"
            :key="index"
            class="result-item"
          >
            <div class="card-info">
              <div class="card-code">{{ card }}</div>
              <div class="card-type">{{ getCardTypeName(formData.type) }}</div>
            </div>
            <n-button
              size="small"
              @click="copyCard(card)"
            >
              <template #icon>
                <n-icon :component="CopyOutline" />
              </template>
              复制
            </n-button>
          </div>
        </div>
        
        <div class="result-actions">
          <n-button
            type="primary"
            @click="copyAllCards"
          >
            <template #icon>
              <n-icon :component="CopyOutline" />
            </template>
            复制全部
          </n-button>
          
          <n-dropdown
            :options="copyFormatOptions"
            @select="handleCopyFormat"
          >
            <n-button>
              复制格式
              <template #icon>
                <n-icon :component="ChevronDownOutline" />
              </template>
            </n-button>
          </n-dropdown>
          
          <n-button
            @click="clearResults"
          >
            <template #icon>
              <n-icon :component="TrashOutline" />
            </template>
            清空结果
          </n-button>
        </div>
      </div>
    </n-card>

    <!-- 错误提示 -->
    <n-alert
      v-if="error"
      type="error"
      :title="error"
      closable
      @close="clearError"
      style="margin-top: 16px"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted } from 'vue'
import {
  NCard,
  NForm,
  NFormItem,
  NGrid,
  NGridItem,
  NSelect,
  NInput,
  NInputNumber,
  NButton,
  NIcon,
  NProgress,
  NAlert,
  NDropdown,
  type FormInst,
  type FormRules
} from 'naive-ui'
import {
  AddOutline,
  EyeOutline,
  CopyOutline,
  ChevronDownOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { useCardsStore } from '@/stores'

// 状态管理
const cardsStore = useCardsStore()

// 表单引用
const formRef = ref<FormInst | null>(null)

// 表单数据
const formData = reactive({
  type: 'M',
  count: 1,
  customCode: '' // 新增自定义卡密字段
})

// 表单验证规则
const formRules: FormRules = {
  type: [
    {
      required: true,
      message: '请选择卡密类型',
      trigger: ['change', 'blur']
    }
  ],
  count: [
    {
      required: true,
      type: 'number',
      min: 1,
      max: 100,
      message: '生成数量必须在1-100之间',
      trigger: ['input', 'blur']
    }
  ],
  customCode: [
    {
      validator: (rule: any, value: string) => {
        if (!value) return true // 可选字段
        if (value.length !== 32) {
          return new Error('自定义卡密必须是32位字符')
        }
        if (!/^[A-Za-z0-9]+$/.test(value)) {
          return new Error('自定义卡密只能包含字母和数字')
        }
        if (formData.count > 1) {
          return new Error('自定义卡密仅在生成单个卡密时有效')
        }
        return true
      },
      trigger: ['input', 'blur']
    }
  ]
}

// 响应式数据
const previewCode = ref('')
const isPreviewLoading = ref(false)

// 计算属性
const cardTypeOptions = computed(() => {
  return cardsStore.cardTypes.map(type => ({
    label: `${type.label} - ${type.description}`,
    value: type.value
  }))
})

const isGenerating = computed(() => cardsStore.isGenerating)
const generateProgress = computed(() => cardsStore.generateProgress)
const generateTotal = computed(() => cardsStore.generateTotal)
const generatedCards = computed(() => cardsStore.generatedCards)
const error = computed(() => cardsStore.error)

const progressPercentage = computed(() => {
  if (generateTotal.value === 0) return 0
  return Math.round((generateProgress.value / generateTotal.value) * 100)
})

// 复制格式选项
const copyFormatOptions = [
  {
    label: '仅卡密号',
    key: 'codes'
  },
  {
    label: '卡密+类型',
    key: 'with-type'
  },
  {
    label: 'CSV格式',
    key: 'csv'
  },
  {
    label: 'JSON格式',
    key: 'json'
  }
]

// 获取卡密类型名称
const getCardTypeName = (type: string): string => {
  const cardType = cardsStore.cardTypes.find(t => t.value === type)
  return cardType ? cardType.label : type
}

// 处理类型变化
const handleTypeChange = () => {
  previewCode.value = ''
}

// 处理预览
const handlePreview = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate(undefined, (rule) => rule?.key === 'type')
    isPreviewLoading.value = true
    
    previewCode.value = await cardsStore.generatePreview(formData.type)
  } catch (error) {
    console.error('预览失败:', error)
  } finally {
    isPreviewLoading.value = false
  }
}

// 处理生成
const handleGenerate = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 支持自定义卡密
    const customCode = formData.customCode.trim() || undefined
    await cardsStore.generateCards(formData.type, formData.count, customCode)

    const message = customCode
      ? `成功生成自定义卡密: ${customCode}`
      : `成功生成 ${formData.count} 个卡密`
    console.log(message)

    // 清空自定义卡密输入
    if (customCode) {
      formData.customCode = ''
    }
  } catch (error) {
    console.error('生成卡密失败:', error)
  }
}

// 复制单个卡密
const copyCard = async (card: string) => {
  try {
    await navigator.clipboard.writeText(card)
    console.log('卡密已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 复制全部卡密
const copyAllCards = async () => {
  try {
    const text = generatedCards.value.join('\n')
    await navigator.clipboard.writeText(text)
    console.log('所有卡密已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 处理复制格式
const handleCopyFormat = async (key: string) => {
  try {
    let text = ''
    const cards = generatedCards.value
    const typeName = getCardTypeName(formData.type)
    
    switch (key) {
      case 'codes':
        text = cards.join('\n')
        break
      case 'with-type':
        text = cards.map(card => `${card} - ${typeName}`).join('\n')
        break
      case 'csv':
        text = '卡密号,类型\n' + cards.map(card => `${card},${typeName}`).join('\n')
        break
      case 'json':
        text = JSON.stringify(
          cards.map(card => ({ code: card, type: formData.type, typeName })),
          null,
          2
        )
        break
    }
    
    await navigator.clipboard.writeText(text)
    console.log('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 清空结果
const clearResults = () => {
  cardsStore.clearGeneratedCards()
  previewCode.value = ''
}

// 清除错误
const clearError = () => {
  cardsStore.clearError()
}

// 组件初始化
onMounted(async () => {
  // 加载套餐类型
  await cardsStore.loadCardTypes()
})
</script>

<style scoped>
.card-generator {
  max-width: 800px;
  margin: 0 auto;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-item {
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.preview-item strong {
  color: #374151;
  margin-right: 8px;
}

.progress-content {
  text-align: center;
}

.progress-text {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.result-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:nth-child(even) {
  background: #f9fafb;
}

.card-info {
  flex: 1;
}

.card-code {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 700;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 4px;
}

.card-type {
  font-size: 12px;
  color: #6b7280;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 响应式 */
@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
  }
  
  .result-actions {
    flex-direction: column;
  }
  
  .result-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
