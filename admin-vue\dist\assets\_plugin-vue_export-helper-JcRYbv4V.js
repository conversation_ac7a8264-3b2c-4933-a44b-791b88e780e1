import{r as j,ay as cr,a6 as Or,j as I,ac as xt,aF as mt,d as pe,aT as yt,ao as wt,X as Ir,ad as Fe,bh as Ct,ab as St,U as Dr,x as Wr,n as Mr,p as zt,aZ as kr,al as Fr,b1 as Lr,bi as Tt,h as $,e as P,aE as _r,a as Je,c as ie,b as Y,f as G,u as er,l as Nr,k as ye,V as Rt,m as rr,a7 as dr,W as Gr,ae as Pt,t as Bt,O as Et,R as Vr,g as ur,aH as Ht,N as $t,aI as Ot,af as It,q as Dt,aa as x,S as $e,a9 as Wt}from"./index-bBUuTVMS.js";function rn(e,r){let{target:t}=e;for(;t;){if(t.dataset&&t.dataset[r]!==void 0)return!0;t=t.parentElement}return!1}function Mt(e){return e.composedPath()[0]||null}function fr(e){return typeof e=="string"?e.endsWith("px")?Number(e.slice(0,e.length-2)):Number(e):e}function tn(e){if(e!=null)return typeof e=="number"?`${e}px`:e.endsWith("px")?e:`${e}px`}function Re(e,r){const t=e.trim().split(/\s+/g),n={top:t[0]};switch(t.length){case 1:n.right=t[0],n.bottom=t[0],n.left=t[0];break;case 2:n.right=t[1],n.left=t[1],n.bottom=t[0];break;case 3:n.right=t[1],n.bottom=t[2],n.left=t[1];break;case 4:n.right=t[1],n.bottom=t[2],n.left=t[3];break;default:throw new Error("[seemly/getMargin]:"+e+" is not a valid value.")}return r===void 0?n:n[r]}function on(e,r){const[t,n]=e.split(" ");return{row:t,col:n||t}}function Me(e){return e.composedPath()[0]}const kt={mousemoveoutside:new WeakMap,clickoutside:new WeakMap};function Ft(e,r,t){if(e==="mousemoveoutside"){const n=o=>{r.contains(Me(o))||t(o)};return{mousemove:n,touchstart:n}}else if(e==="clickoutside"){let n=!1;const o=u=>{n=!r.contains(Me(u))},l=u=>{n&&(r.contains(Me(u))||t(u))};return{mousedown:o,mouseup:l,touchstart:o,touchend:l}}return console.error(`[evtd/create-trap-handler]: name \`${e}\` is invalid. This could be a bug of evtd.`),{}}function Xr(e,r,t){const n=kt[e];let o=n.get(r);o===void 0&&n.set(r,o=new WeakMap);let l=o.get(t);return l===void 0&&o.set(t,l=Ft(e,r,t)),l}function Lt(e,r,t,n){if(e==="mousemoveoutside"||e==="clickoutside"){const o=Xr(e,r,t);return Object.keys(o).forEach(l=>{Pe(l,document,o[l],n)}),!0}return!1}function _t(e,r,t,n){if(e==="mousemoveoutside"||e==="clickoutside"){const o=Xr(e,r,t);return Object.keys(o).forEach(l=>{he(l,document,o[l],n)}),!0}return!1}function Nt(){if(typeof window>"u")return{on:()=>{},off:()=>{}};const e=new WeakMap,r=new WeakMap;function t(){e.set(this,!0)}function n(){e.set(this,!0),r.set(this,!0)}function o(i,s,d){const g=i[s];return i[s]=function(){return d.apply(i,arguments),g.apply(i,arguments)},i}function l(i,s){i[s]=Event.prototype[s]}const u=new WeakMap,H=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function R(){var i;return(i=u.get(this))!==null&&i!==void 0?i:null}function T(i,s){H!==void 0&&Object.defineProperty(i,"currentTarget",{configurable:!0,enumerable:!0,get:s??H.get})}const D={bubble:{},capture:{}},b={};function q(){const i=function(s){const{type:d,eventPhase:g,bubbles:w}=s,C=Me(s);if(g===2)return;const V=g===1?"capture":"bubble";let O=C;const X=[];for(;O===null&&(O=window),X.push(O),O!==window;)O=O.parentNode||null;const M=D.capture[d],S=D.bubble[d];if(o(s,"stopPropagation",t),o(s,"stopImmediatePropagation",n),T(s,R),V==="capture"){if(M===void 0)return;for(let K=X.length-1;K>=0&&!e.has(s);--K){const ee=X[K],Q=M.get(ee);if(Q!==void 0){u.set(s,ee);for(const oe of Q){if(r.has(s))break;oe(s)}}if(K===0&&!w&&S!==void 0){const oe=S.get(ee);if(oe!==void 0)for(const ce of oe){if(r.has(s))break;ce(s)}}}}else if(V==="bubble"){if(S===void 0)return;for(let K=0;K<X.length&&!e.has(s);++K){const ee=X[K],Q=S.get(ee);if(Q!==void 0){u.set(s,ee);for(const oe of Q){if(r.has(s))break;oe(s)}}}}l(s,"stopPropagation"),l(s,"stopImmediatePropagation"),T(s)};return i.displayName="evtdUnifiedHandler",i}function p(){const i=function(s){const{type:d,eventPhase:g}=s;if(g!==2)return;const w=b[d];w!==void 0&&w.forEach(C=>C(s))};return i.displayName="evtdUnifiedWindowEventHandler",i}const E=q(),Z=p();function F(i,s){const d=D[i];return d[s]===void 0&&(d[s]=new Map,window.addEventListener(s,E,i==="capture")),d[s]}function v(i){return b[i]===void 0&&(b[i]=new Set,window.addEventListener(i,Z)),b[i]}function N(i,s){let d=i.get(s);return d===void 0&&i.set(s,d=new Set),d}function W(i,s,d,g){const w=D[s][d];if(w!==void 0){const C=w.get(i);if(C!==void 0&&C.has(g))return!0}return!1}function f(i,s){const d=b[i];return!!(d!==void 0&&d.has(s))}function m(i,s,d,g){let w;if(typeof g=="object"&&g.once===!0?w=M=>{y(i,s,w,g),d(M)}:w=d,Lt(i,s,w,g))return;const V=g===!0||typeof g=="object"&&g.capture===!0?"capture":"bubble",O=F(V,i),X=N(O,s);if(X.has(w)||X.add(w),s===window){const M=v(i);M.has(w)||M.add(w)}}function y(i,s,d,g){if(_t(i,s,d,g))return;const C=g===!0||typeof g=="object"&&g.capture===!0,V=C?"capture":"bubble",O=F(V,i),X=N(O,s);if(s===window&&!W(s,C?"bubble":"capture",i,d)&&f(i,d)){const S=b[i];S.delete(d),S.size===0&&(window.removeEventListener(i,Z),b[i]=void 0)}X.has(d)&&X.delete(d),X.size===0&&O.delete(s),O.size===0&&(window.removeEventListener(i,E,V==="capture"),D[V][i]=void 0)}return{on:m,off:y}}const{on:Pe,off:he}=Nt();function nn(e){const r=j(!!e.value);if(r.value)return cr(r);const t=Or(e,n=>{n&&(r.value=!0,t())});return cr(r)}function an(e,r){return Or(e,t=>{t!==void 0&&(r.value=t)}),I(()=>e.value===void 0?r.value:e.value)}const Gt=(typeof window>"u"?!1:/iPad|iPhone|iPod/.test(navigator.platform)||navigator.platform==="MacIntel"&&navigator.maxTouchPoints>1)&&!window.MSStream;function Vt(){return Gt}const Le=typeof document<"u"&&typeof window<"u";function Xt(e){const r={isDeactivated:!1};let t=!1;return xt(()=>{if(r.isDeactivated=!1,!t){t=!0;return}e()}),mt(()=>{r.isDeactivated=!0,t||(t=!0)}),r}function hr(e,r){console.error(`[vueuc/${e}]: ${r}`)}var ve=[],jt=function(){return ve.some(function(e){return e.activeTargets.length>0})},At=function(){return ve.some(function(e){return e.skippedTargets.length>0})},vr="ResizeObserver loop completed with undelivered notifications.",Yt=function(){var e;typeof ErrorEvent=="function"?e=new ErrorEvent("error",{message:vr}):(e=document.createEvent("Event"),e.initEvent("error",!1,!1),e.message=vr),window.dispatchEvent(e)},Ee;(function(e){e.BORDER_BOX="border-box",e.CONTENT_BOX="content-box",e.DEVICE_PIXEL_CONTENT_BOX="device-pixel-content-box"})(Ee||(Ee={}));var be=function(e){return Object.freeze(e)},Ut=function(){function e(r,t){this.inlineSize=r,this.blockSize=t,be(this)}return e}(),jr=function(){function e(r,t,n,o){return this.x=r,this.y=t,this.width=n,this.height=o,this.top=this.y,this.left=this.x,this.bottom=this.top+this.height,this.right=this.left+this.width,be(this)}return e.prototype.toJSON=function(){var r=this,t=r.x,n=r.y,o=r.top,l=r.right,u=r.bottom,H=r.left,R=r.width,T=r.height;return{x:t,y:n,top:o,right:l,bottom:u,left:H,width:R,height:T}},e.fromRect=function(r){return new e(r.x,r.y,r.width,r.height)},e}(),tr=function(e){return e instanceof SVGElement&&"getBBox"in e},Ar=function(e){if(tr(e)){var r=e.getBBox(),t=r.width,n=r.height;return!t&&!n}var o=e,l=o.offsetWidth,u=o.offsetHeight;return!(l||u||e.getClientRects().length)},br=function(e){var r;if(e instanceof Element)return!0;var t=(r=e?.ownerDocument)===null||r===void 0?void 0:r.defaultView;return!!(t&&e instanceof t.Element)},qt=function(e){switch(e.tagName){case"INPUT":if(e.type!=="image")break;case"VIDEO":case"AUDIO":case"EMBED":case"OBJECT":case"CANVAS":case"IFRAME":case"IMG":return!0}return!1},Be=typeof window<"u"?window:{},Oe=new WeakMap,pr=/auto|scroll/,Kt=/^tb|vertical/,Qt=/msie|trident/i.test(Be.navigator&&Be.navigator.userAgent),ne=function(e){return parseFloat(e||"0")},me=function(e,r,t){return e===void 0&&(e=0),r===void 0&&(r=0),t===void 0&&(t=!1),new Ut((t?r:e)||0,(t?e:r)||0)},gr=be({devicePixelContentBoxSize:me(),borderBoxSize:me(),contentBoxSize:me(),contentRect:new jr(0,0,0,0)}),Yr=function(e,r){if(r===void 0&&(r=!1),Oe.has(e)&&!r)return Oe.get(e);if(Ar(e))return Oe.set(e,gr),gr;var t=getComputedStyle(e),n=tr(e)&&e.ownerSVGElement&&e.getBBox(),o=!Qt&&t.boxSizing==="border-box",l=Kt.test(t.writingMode||""),u=!n&&pr.test(t.overflowY||""),H=!n&&pr.test(t.overflowX||""),R=n?0:ne(t.paddingTop),T=n?0:ne(t.paddingRight),D=n?0:ne(t.paddingBottom),b=n?0:ne(t.paddingLeft),q=n?0:ne(t.borderTopWidth),p=n?0:ne(t.borderRightWidth),E=n?0:ne(t.borderBottomWidth),Z=n?0:ne(t.borderLeftWidth),F=b+T,v=R+D,N=Z+p,W=q+E,f=H?e.offsetHeight-W-e.clientHeight:0,m=u?e.offsetWidth-N-e.clientWidth:0,y=o?F+N:0,i=o?v+W:0,s=n?n.width:ne(t.width)-y-m,d=n?n.height:ne(t.height)-i-f,g=s+F+m+N,w=d+v+f+W,C=be({devicePixelContentBoxSize:me(Math.round(s*devicePixelRatio),Math.round(d*devicePixelRatio),l),borderBoxSize:me(g,w,l),contentBoxSize:me(s,d,l),contentRect:new jr(b,R,s,d)});return Oe.set(e,C),C},Ur=function(e,r,t){var n=Yr(e,t),o=n.borderBoxSize,l=n.contentBoxSize,u=n.devicePixelContentBoxSize;switch(r){case Ee.DEVICE_PIXEL_CONTENT_BOX:return u;case Ee.BORDER_BOX:return o;default:return l}},Zt=function(){function e(r){var t=Yr(r);this.target=r,this.contentRect=t.contentRect,this.borderBoxSize=be([t.borderBoxSize]),this.contentBoxSize=be([t.contentBoxSize]),this.devicePixelContentBoxSize=be([t.devicePixelContentBoxSize])}return e}(),qr=function(e){if(Ar(e))return 1/0;for(var r=0,t=e.parentNode;t;)r+=1,t=t.parentNode;return r},Jt=function(){var e=1/0,r=[];ve.forEach(function(u){if(u.activeTargets.length!==0){var H=[];u.activeTargets.forEach(function(T){var D=new Zt(T.target),b=qr(T.target);H.push(D),T.lastReportedSize=Ur(T.target,T.observedBox),b<e&&(e=b)}),r.push(function(){u.callback.call(u.observer,H,u.observer)}),u.activeTargets.splice(0,u.activeTargets.length)}});for(var t=0,n=r;t<n.length;t++){var o=n[t];o()}return e},xr=function(e){ve.forEach(function(t){t.activeTargets.splice(0,t.activeTargets.length),t.skippedTargets.splice(0,t.skippedTargets.length),t.observationTargets.forEach(function(o){o.isActive()&&(qr(o.target)>e?t.activeTargets.push(o):t.skippedTargets.push(o))})})},eo=function(){var e=0;for(xr(e);jt();)e=Jt(),xr(e);return At()&&Yt(),e>0},Ke,Kr=[],ro=function(){return Kr.splice(0).forEach(function(e){return e()})},to=function(e){if(!Ke){var r=0,t=document.createTextNode(""),n={characterData:!0};new MutationObserver(function(){return ro()}).observe(t,n),Ke=function(){t.textContent="".concat(r?r--:r++)}}Kr.push(e),Ke()},oo=function(e){to(function(){requestAnimationFrame(e)})},ke=0,no=function(){return!!ke},io=250,ao={attributes:!0,characterData:!0,childList:!0,subtree:!0},mr=["resize","load","transitionend","animationend","animationstart","animationiteration","keyup","keydown","mouseup","mousedown","mouseover","mouseout","blur","focus"],yr=function(e){return e===void 0&&(e=0),Date.now()+e},Qe=!1,so=function(){function e(){var r=this;this.stopped=!0,this.listener=function(){return r.schedule()}}return e.prototype.run=function(r){var t=this;if(r===void 0&&(r=io),!Qe){Qe=!0;var n=yr(r);oo(function(){var o=!1;try{o=eo()}finally{if(Qe=!1,r=n-yr(),!no())return;o?t.run(1e3):r>0?t.run(r):t.start()}})}},e.prototype.schedule=function(){this.stop(),this.run()},e.prototype.observe=function(){var r=this,t=function(){return r.observer&&r.observer.observe(document.body,ao)};document.body?t():Be.addEventListener("DOMContentLoaded",t)},e.prototype.start=function(){var r=this;this.stopped&&(this.stopped=!1,this.observer=new MutationObserver(this.listener),this.observe(),mr.forEach(function(t){return Be.addEventListener(t,r.listener,!0)}))},e.prototype.stop=function(){var r=this;this.stopped||(this.observer&&this.observer.disconnect(),mr.forEach(function(t){return Be.removeEventListener(t,r.listener,!0)}),this.stopped=!0)},e}(),Ze=new so,wr=function(e){!ke&&e>0&&Ze.start(),ke+=e,!ke&&Ze.stop()},lo=function(e){return!tr(e)&&!qt(e)&&getComputedStyle(e).display==="inline"},co=function(){function e(r,t){this.target=r,this.observedBox=t||Ee.CONTENT_BOX,this.lastReportedSize={inlineSize:0,blockSize:0}}return e.prototype.isActive=function(){var r=Ur(this.target,this.observedBox,!0);return lo(this.target)&&(this.lastReportedSize=r),this.lastReportedSize.inlineSize!==r.inlineSize||this.lastReportedSize.blockSize!==r.blockSize},e}(),uo=function(){function e(r,t){this.activeTargets=[],this.skippedTargets=[],this.observationTargets=[],this.observer=r,this.callback=t}return e}(),Ie=new WeakMap,Cr=function(e,r){for(var t=0;t<e.length;t+=1)if(e[t].target===r)return t;return-1},De=function(){function e(){}return e.connect=function(r,t){var n=new uo(r,t);Ie.set(r,n)},e.observe=function(r,t,n){var o=Ie.get(r),l=o.observationTargets.length===0;Cr(o.observationTargets,t)<0&&(l&&ve.push(o),o.observationTargets.push(new co(t,n&&n.box)),wr(1),Ze.schedule())},e.unobserve=function(r,t){var n=Ie.get(r),o=Cr(n.observationTargets,t),l=n.observationTargets.length===1;o>=0&&(l&&ve.splice(ve.indexOf(n),1),n.observationTargets.splice(o,1),wr(-1))},e.disconnect=function(r){var t=this,n=Ie.get(r);n.observationTargets.slice().forEach(function(o){return t.unobserve(r,o.target)}),n.activeTargets.splice(0,n.activeTargets.length)},e}(),fo=function(){function e(r){if(arguments.length===0)throw new TypeError("Failed to construct 'ResizeObserver': 1 argument required, but only 0 present.");if(typeof r!="function")throw new TypeError("Failed to construct 'ResizeObserver': The callback provided as parameter 1 is not a function.");De.connect(this,r)}return e.prototype.observe=function(r,t){if(arguments.length===0)throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': 1 argument required, but only 0 present.");if(!br(r))throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': parameter 1 is not of type 'Element");De.observe(this,r,t)},e.prototype.unobserve=function(r){if(arguments.length===0)throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': 1 argument required, but only 0 present.");if(!br(r))throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': parameter 1 is not of type 'Element");De.unobserve(this,r)},e.prototype.disconnect=function(){De.disconnect(this)},e.toString=function(){return"function ResizeObserver () { [polyfill code] }"},e}();class ho{constructor(){this.handleResize=this.handleResize.bind(this),this.observer=new(typeof window<"u"&&window.ResizeObserver||fo)(this.handleResize),this.elHandlersMap=new Map}handleResize(r){for(const t of r){const n=this.elHandlersMap.get(t.target);n!==void 0&&n(t)}}registerHandler(r,t){this.elHandlersMap.set(r,t),this.observer.observe(r)}unregisterHandler(r){this.elHandlersMap.has(r)&&(this.elHandlersMap.delete(r),this.observer.unobserve(r))}}const Sr=new ho,zr=pe({name:"ResizeObserver",props:{onResize:Function},setup(e){let r=!1;const t=wt().proxy;function n(o){const{onResize:l}=e;l!==void 0&&l(o)}Ir(()=>{const o=t.$el;if(o===void 0){hr("resize-observer","$el does not exist.");return}if(o.nextElementSibling!==o.nextSibling&&o.nodeType===3&&o.nodeValue!==""){hr("resize-observer","$el can not be observed (it may be a text node).");return}o.nextElementSibling!==null&&(Sr.registerHandler(o.nextElementSibling,n),r=!0)}),Fe(()=>{r&&Sr.unregisterHandler(t.$el.nextElementSibling)})},render(){return yt(this.$slots,"default")}});function Tr(e){return e.replace(/#|\(|\)|,|\s|\./g,"_")}const vo=/^(\d|\.)+$/,Rr=/(\d|\.)+/;function bo(e,{c:r=1,offset:t=0,attachPx:n=!0}={}){if(typeof e=="number"){const o=(e+t)*r;return o===0?"0":`${o}px`}else if(typeof e=="string")if(vo.test(e)){const o=(Number(e)+t)*r;return n?o===0?"0":`${o}px`:`${o}`}else{const o=Rr.exec(e);return o?e.replace(Rr,String((Number(o[0])+t)*r)):e}return e}function Pr(e){const{left:r,right:t,top:n,bottom:o}=Re(e);return`${n} ${r} ${o} ${t}`}function Qr(e,...r){if(Array.isArray(e))e.forEach(t=>Qr(t,...r));else return e(...r)}function sn(e){return Object.keys(e)}function we(e){return e.some(r=>Ct(r)?!(r.type===St||r.type===Dr&&!we(r.children)):!0)?e:null}function ln(e,r){return e&&we(e())||r()}function cn(e,r,t){return e&&we(e(r))||t(r)}function Br(e,r){const t=e&&we(e());return r(t||null)}function dn(e,r,t){const n=e&&we(e(r));return t(n||null)}function po(e){return!(e&&we(e()))}const Er=pe({render(){var e,r;return(r=(e=this.$slots).default)===null||r===void 0?void 0:r.call(e)}}),Hr=Mr("n-form-item");function go(e,{defaultSize:r="medium",mergedSize:t,mergedDisabled:n}={}){const o=Wr(Hr,null);zt(Hr,null);const l=I(t?()=>t(o):()=>{const{size:R}=e;if(R)return R;if(o){const{mergedSize:T}=o;if(T.value!==void 0)return T.value}return r}),u=I(n?()=>n(o):()=>{const{disabled:R}=e;return R!==void 0?R:o?o.disabled.value:!1}),H=I(()=>{const{status:R}=e;return R||o?.mergedValidationStatus.value});return Fe(()=>{o&&o.restoreValidation()}),{mergedSizeRef:l,mergedDisabledRef:u,mergedStatusRef:H,nTriggerFormBlur(){o&&o.handleContentBlur()},nTriggerFormChange(){o&&o.handleContentChange()},nTriggerFormFocus(){o&&o.handleContentFocus()},nTriggerFormInput(){o&&o.handleContentInput()}}}var xo=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,mo=/^\w*$/;function yo(e,r){if(kr(e))return!1;var t=typeof e;return t=="number"||t=="symbol"||t=="boolean"||e==null||Fr(e)?!0:mo.test(e)||!xo.test(e)||r!=null&&e in Object(r)}var wo="Expected a function";function or(e,r){if(typeof e!="function"||r!=null&&typeof r!="function")throw new TypeError(wo);var t=function(){var n=arguments,o=r?r.apply(this,n):n[0],l=t.cache;if(l.has(o))return l.get(o);var u=e.apply(this,n);return t.cache=l.set(o,u)||l,u};return t.cache=new(or.Cache||Lr),t}or.Cache=Lr;var Co=500;function So(e){var r=or(e,function(n){return t.size===Co&&t.clear(),n}),t=r.cache;return r}var zo=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,To=/\\(\\)?/g,Ro=So(function(e){var r=[];return e.charCodeAt(0)===46&&r.push(""),e.replace(zo,function(t,n,o,l){r.push(o?l.replace(To,"$1"):n||t)}),r});function Po(e,r){return kr(e)?e:yo(e,r)?[e]:Ro(Tt(e))}function Bo(e){if(typeof e=="string"||Fr(e))return e;var r=e+"";return r=="0"&&1/e==-1/0?"-0":r}function Eo(e,r){r=Po(r,e);for(var t=0,n=r.length;e!=null&&t<n;)e=e[Bo(r[t++])];return t&&t==n?e:void 0}function un(e,r,t){var n=e==null?void 0:Eo(e,r);return n===void 0?t:n}const fn=pe({name:"ChevronRight",render(){return $("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},$("path",{d:"M5.64645 3.14645C5.45118 3.34171 5.45118 3.65829 5.64645 3.85355L9.79289 8L5.64645 12.1464C5.45118 12.3417 5.45118 12.6583 5.64645 12.8536C5.84171 13.0488 6.15829 13.0488 6.35355 12.8536L10.8536 8.35355C11.0488 8.15829 11.0488 7.84171 10.8536 7.64645L6.35355 3.14645C6.15829 2.95118 5.84171 2.95118 5.64645 3.14645Z",fill:"currentColor"}))}}),{cubicBezierEaseInOut:$r}=_r;function Ho({name:e="fade-in",enterDuration:r="0.2s",leaveDuration:t="0.2s",enterCubicBezier:n=$r,leaveCubicBezier:o=$r}={}){return[P(`&.${e}-transition-enter-active`,{transition:`all ${r} ${n}!important`}),P(`&.${e}-transition-leave-active`,{transition:`all ${t} ${o}!important`}),P(`&.${e}-transition-enter-from, &.${e}-transition-leave-to`,{opacity:0}),P(`&.${e}-transition-leave-from, &.${e}-transition-enter-to`,{opacity:1})]}const $o={railInsetHorizontalBottom:"auto 2px 4px 2px",railInsetHorizontalTop:"4px 2px auto 2px",railInsetVerticalRight:"2px 4px 2px auto",railInsetVerticalLeft:"2px auto 2px 4px",railColor:"transparent"};function Oo(e){const{scrollbarColor:r,scrollbarColorHover:t,scrollbarHeight:n,scrollbarWidth:o,scrollbarBorderRadius:l}=e;return Object.assign(Object.assign({},$o),{height:n,width:o,borderRadius:l,color:r,colorHover:t})}const Io={name:"Scrollbar",common:Je,self:Oo},Do=ie("scrollbar",`
 overflow: hidden;
 position: relative;
 z-index: auto;
 height: 100%;
 width: 100%;
`,[P(">",[ie("scrollbar-container",`
 width: 100%;
 overflow: scroll;
 height: 100%;
 min-height: inherit;
 max-height: inherit;
 scrollbar-width: none;
 `,[P("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",`
 width: 0;
 height: 0;
 display: none;
 `),P(">",[ie("scrollbar-content",`
 box-sizing: border-box;
 min-width: 100%;
 `)])])]),P(">, +",[ie("scrollbar-rail",`
 position: absolute;
 pointer-events: none;
 user-select: none;
 background: var(--n-scrollbar-rail-color);
 -webkit-user-select: none;
 `,[Y("horizontal",`
 height: var(--n-scrollbar-height);
 `,[P(">",[G("scrollbar",`
 height: var(--n-scrollbar-height);
 border-radius: var(--n-scrollbar-border-radius);
 right: 0;
 `)])]),Y("horizontal--top",`
 top: var(--n-scrollbar-rail-top-horizontal-top); 
 right: var(--n-scrollbar-rail-right-horizontal-top); 
 bottom: var(--n-scrollbar-rail-bottom-horizontal-top); 
 left: var(--n-scrollbar-rail-left-horizontal-top); 
 `),Y("horizontal--bottom",`
 top: var(--n-scrollbar-rail-top-horizontal-bottom); 
 right: var(--n-scrollbar-rail-right-horizontal-bottom); 
 bottom: var(--n-scrollbar-rail-bottom-horizontal-bottom); 
 left: var(--n-scrollbar-rail-left-horizontal-bottom); 
 `),Y("vertical",`
 width: var(--n-scrollbar-width);
 `,[P(">",[G("scrollbar",`
 width: var(--n-scrollbar-width);
 border-radius: var(--n-scrollbar-border-radius);
 bottom: 0;
 `)])]),Y("vertical--left",`
 top: var(--n-scrollbar-rail-top-vertical-left); 
 right: var(--n-scrollbar-rail-right-vertical-left); 
 bottom: var(--n-scrollbar-rail-bottom-vertical-left); 
 left: var(--n-scrollbar-rail-left-vertical-left); 
 `),Y("vertical--right",`
 top: var(--n-scrollbar-rail-top-vertical-right); 
 right: var(--n-scrollbar-rail-right-vertical-right); 
 bottom: var(--n-scrollbar-rail-bottom-vertical-right); 
 left: var(--n-scrollbar-rail-left-vertical-right); 
 `),Y("disabled",[P(">",[G("scrollbar","pointer-events: none;")])]),P(">",[G("scrollbar",`
 z-index: 1;
 position: absolute;
 cursor: pointer;
 pointer-events: all;
 background-color: var(--n-scrollbar-color);
 transition: background-color .2s var(--n-scrollbar-bezier);
 `,[Ho(),P("&:hover","background-color: var(--n-scrollbar-color-hover);")])])])])]),Wo=Object.assign(Object.assign({},ye.props),{duration:{type:Number,default:0},scrollable:{type:Boolean,default:!0},xScrollable:Boolean,trigger:{type:String,default:"hover"},useUnifiedContainer:Boolean,triggerDisplayManually:Boolean,container:Function,content:Function,containerClass:String,containerStyle:[String,Object],contentClass:[String,Array],contentStyle:[String,Object],horizontalRailStyle:[String,Object],verticalRailStyle:[String,Object],onScroll:Function,onWheel:Function,onResize:Function,internalOnUpdateScrollLeft:Function,internalHoistYRail:Boolean,yPlacement:{type:String,default:"right"},xPlacement:{type:String,default:"bottom"}}),Mo=pe({name:"Scrollbar",props:Wo,inheritAttrs:!1,setup(e){const{mergedClsPrefixRef:r,inlineThemeDisabled:t,mergedRtlRef:n}=er(e),o=Nr("Scrollbar",n,r),l=j(null),u=j(null),H=j(null),R=j(null),T=j(null),D=j(null),b=j(null),q=j(null),p=j(null),E=j(null),Z=j(null),F=j(0),v=j(0),N=j(!1),W=j(!1);let f=!1,m=!1,y,i,s=0,d=0,g=0,w=0;const C=Vt(),V=ye("Scrollbar","-scrollbar",Do,Io,e,r),O=I(()=>{const{value:a}=q,{value:c}=D,{value:h}=E;return a===null||c===null||h===null?0:Math.min(a,h*a/c+fr(V.value.self.width)*1.5)}),X=I(()=>`${O.value}px`),M=I(()=>{const{value:a}=p,{value:c}=b,{value:h}=Z;return a===null||c===null||h===null?0:h*a/c+fr(V.value.self.height)*1.5}),S=I(()=>`${M.value}px`),K=I(()=>{const{value:a}=q,{value:c}=F,{value:h}=D,{value:k}=E;if(a===null||h===null||k===null)return 0;{const A=h-a;return A?c/A*(k-O.value):0}}),ee=I(()=>`${K.value}px`),Q=I(()=>{const{value:a}=p,{value:c}=v,{value:h}=b,{value:k}=Z;if(a===null||h===null||k===null)return 0;{const A=h-a;return A?c/A*(k-M.value):0}}),oe=I(()=>`${Q.value}px`),ce=I(()=>{const{value:a}=q,{value:c}=D;return a!==null&&c!==null&&c>a}),He=I(()=>{const{value:a}=p,{value:c}=b;return a!==null&&c!==null&&c>a}),_e=I(()=>{const{trigger:a}=e;return a==="none"||N.value}),Ne=I(()=>{const{trigger:a}=e;return a==="none"||W.value}),B=I(()=>{const{container:a}=e;return a?a():u.value}),ge=I(()=>{const{content:a}=e;return a?a():H.value}),Ce=(a,c)=>{if(!e.scrollable)return;if(typeof a=="number"){ae(a,c??0,0,!1,"auto");return}const{left:h,top:k,index:A,elSize:J,position:te,behavior:_,el:re,debounce:se=!0}=a;(h!==void 0||k!==void 0)&&ae(h??0,k??0,0,!1,_),re!==void 0?ae(0,re.offsetTop,re.offsetHeight,se,_):A!==void 0&&J!==void 0?ae(0,A*J,J,se,_):te==="bottom"?ae(0,Number.MAX_SAFE_INTEGER,0,!1,_):te==="top"&&ae(0,0,0,!1,_)},z=Xt(()=>{e.container||Ce({top:F.value,left:v.value})}),Se=()=>{z.isDeactivated||ue()},ze=a=>{if(z.isDeactivated)return;const{onResize:c}=e;c&&c(a),ue()},Ge=(a,c)=>{if(!e.scrollable)return;const{value:h}=B;h&&(typeof a=="object"?h.scrollBy(a):h.scrollBy(a,c||0))};function ae(a,c,h,k,A){const{value:J}=B;if(J){if(k){const{scrollTop:te,offsetHeight:_}=J;if(c>te){c+h<=te+_||J.scrollTo({left:a,top:c+h-_,behavior:A});return}}J.scrollTo({left:a,top:c,behavior:A})}}function Ve(){Ye(),L(),ue()}function Xe(){Te()}function Te(){je(),Ae()}function je(){i!==void 0&&window.clearTimeout(i),i=window.setTimeout(()=>{W.value=!1},e.duration)}function Ae(){y!==void 0&&window.clearTimeout(y),y=window.setTimeout(()=>{N.value=!1},e.duration)}function Ye(){y!==void 0&&window.clearTimeout(y),N.value=!0}function L(){i!==void 0&&window.clearTimeout(i),W.value=!0}function U(a){const{onScroll:c}=e;c&&c(a),de()}function de(){const{value:a}=B;a&&(F.value=a.scrollTop,v.value=a.scrollLeft*(o?.value?-1:1))}function Zr(){const{value:a}=ge;a&&(D.value=a.offsetHeight,b.value=a.offsetWidth);const{value:c}=B;c&&(q.value=c.offsetHeight,p.value=c.offsetWidth);const{value:h}=T,{value:k}=R;h&&(Z.value=h.offsetWidth),k&&(E.value=k.offsetHeight)}function nr(){const{value:a}=B;a&&(F.value=a.scrollTop,v.value=a.scrollLeft*(o?.value?-1:1),q.value=a.offsetHeight,p.value=a.offsetWidth,D.value=a.scrollHeight,b.value=a.scrollWidth);const{value:c}=T,{value:h}=R;c&&(Z.value=c.offsetWidth),h&&(E.value=h.offsetHeight)}function ue(){e.scrollable&&(e.useUnifiedContainer?nr():(Zr(),de()))}function ir(a){var c;return!(!((c=l.value)===null||c===void 0)&&c.contains(Mt(a)))}function Jr(a){a.preventDefault(),a.stopPropagation(),m=!0,Pe("mousemove",window,ar,!0),Pe("mouseup",window,sr,!0),d=v.value,g=o?.value?window.innerWidth-a.clientX:a.clientX}function ar(a){if(!m)return;y!==void 0&&window.clearTimeout(y),i!==void 0&&window.clearTimeout(i);const{value:c}=p,{value:h}=b,{value:k}=M;if(c===null||h===null)return;const J=(o?.value?window.innerWidth-a.clientX-g:a.clientX-g)*(h-c)/(c-k),te=h-c;let _=d+J;_=Math.min(te,_),_=Math.max(_,0);const{value:re}=B;if(re){re.scrollLeft=_*(o?.value?-1:1);const{internalOnUpdateScrollLeft:se}=e;se&&se(_)}}function sr(a){a.preventDefault(),a.stopPropagation(),he("mousemove",window,ar,!0),he("mouseup",window,sr,!0),m=!1,ue(),ir(a)&&Te()}function et(a){a.preventDefault(),a.stopPropagation(),f=!0,Pe("mousemove",window,Ue,!0),Pe("mouseup",window,qe,!0),s=F.value,w=a.clientY}function Ue(a){if(!f)return;y!==void 0&&window.clearTimeout(y),i!==void 0&&window.clearTimeout(i);const{value:c}=q,{value:h}=D,{value:k}=O;if(c===null||h===null)return;const J=(a.clientY-w)*(h-c)/(c-k),te=h-c;let _=s+J;_=Math.min(te,_),_=Math.max(_,0);const{value:re}=B;re&&(re.scrollTop=_)}function qe(a){a.preventDefault(),a.stopPropagation(),he("mousemove",window,Ue,!0),he("mouseup",window,qe,!0),f=!1,ue(),ir(a)&&Te()}Rt(()=>{const{value:a}=He,{value:c}=ce,{value:h}=r,{value:k}=T,{value:A}=R;k&&(a?k.classList.remove(`${h}-scrollbar-rail--disabled`):k.classList.add(`${h}-scrollbar-rail--disabled`)),A&&(c?A.classList.remove(`${h}-scrollbar-rail--disabled`):A.classList.add(`${h}-scrollbar-rail--disabled`))}),Ir(()=>{e.container||ue()}),Fe(()=>{y!==void 0&&window.clearTimeout(y),i!==void 0&&window.clearTimeout(i),he("mousemove",window,Ue,!0),he("mouseup",window,qe,!0)});const lr=I(()=>{const{common:{cubicBezierEaseInOut:a},self:{color:c,colorHover:h,height:k,width:A,borderRadius:J,railInsetHorizontalTop:te,railInsetHorizontalBottom:_,railInsetVerticalRight:re,railInsetVerticalLeft:se,railColor:rt}}=V.value,{top:tt,right:ot,bottom:nt,left:it}=Re(te),{top:at,right:st,bottom:lt,left:ct}=Re(_),{top:dt,right:ut,bottom:ft,left:ht}=Re(o?.value?Pr(re):re),{top:vt,right:bt,bottom:pt,left:gt}=Re(o?.value?Pr(se):se);return{"--n-scrollbar-bezier":a,"--n-scrollbar-color":c,"--n-scrollbar-color-hover":h,"--n-scrollbar-border-radius":J,"--n-scrollbar-width":A,"--n-scrollbar-height":k,"--n-scrollbar-rail-top-horizontal-top":tt,"--n-scrollbar-rail-right-horizontal-top":ot,"--n-scrollbar-rail-bottom-horizontal-top":nt,"--n-scrollbar-rail-left-horizontal-top":it,"--n-scrollbar-rail-top-horizontal-bottom":at,"--n-scrollbar-rail-right-horizontal-bottom":st,"--n-scrollbar-rail-bottom-horizontal-bottom":lt,"--n-scrollbar-rail-left-horizontal-bottom":ct,"--n-scrollbar-rail-top-vertical-right":dt,"--n-scrollbar-rail-right-vertical-right":ut,"--n-scrollbar-rail-bottom-vertical-right":ft,"--n-scrollbar-rail-left-vertical-right":ht,"--n-scrollbar-rail-top-vertical-left":vt,"--n-scrollbar-rail-right-vertical-left":bt,"--n-scrollbar-rail-bottom-vertical-left":pt,"--n-scrollbar-rail-left-vertical-left":gt,"--n-scrollbar-rail-color":rt}}),xe=t?rr("scrollbar",void 0,lr,e):void 0;return Object.assign(Object.assign({},{scrollTo:Ce,scrollBy:Ge,sync:ue,syncUnifiedContainer:nr,handleMouseEnterWrapper:Ve,handleMouseLeaveWrapper:Xe}),{mergedClsPrefix:r,rtlEnabled:o,containerScrollTop:F,wrapperRef:l,containerRef:u,contentRef:H,yRailRef:R,xRailRef:T,needYBar:ce,needXBar:He,yBarSizePx:X,xBarSizePx:S,yBarTopPx:ee,xBarLeftPx:oe,isShowXBar:_e,isShowYBar:Ne,isIos:C,handleScroll:U,handleContentResize:Se,handleContainerResize:ze,handleYScrollMouseDown:et,handleXScrollMouseDown:Jr,cssVars:t?void 0:lr,themeClass:xe?.themeClass,onRender:xe?.onRender})},render(){var e;const{$slots:r,mergedClsPrefix:t,triggerDisplayManually:n,rtlEnabled:o,internalHoistYRail:l,yPlacement:u,xPlacement:H,xScrollable:R}=this;if(!this.scrollable)return(e=r.default)===null||e===void 0?void 0:e.call(r);const T=this.trigger==="none",D=(p,E)=>$("div",{ref:"yRailRef",class:[`${t}-scrollbar-rail`,`${t}-scrollbar-rail--vertical`,`${t}-scrollbar-rail--vertical--${u}`,p],"data-scrollbar-rail":!0,style:[E||"",this.verticalRailStyle],"aria-hidden":!0},$(T?Er:dr,T?null:{name:"fade-in-transition"},{default:()=>this.needYBar&&this.isShowYBar&&!this.isIos?$("div",{class:`${t}-scrollbar-rail__scrollbar`,style:{height:this.yBarSizePx,top:this.yBarTopPx},onMousedown:this.handleYScrollMouseDown}):null})),b=()=>{var p,E;return(p=this.onRender)===null||p===void 0||p.call(this),$("div",Gr(this.$attrs,{role:"none",ref:"wrapperRef",class:[`${t}-scrollbar`,this.themeClass,o&&`${t}-scrollbar--rtl`],style:this.cssVars,onMouseenter:n?void 0:this.handleMouseEnterWrapper,onMouseleave:n?void 0:this.handleMouseLeaveWrapper}),[this.container?(E=r.default)===null||E===void 0?void 0:E.call(r):$("div",{role:"none",ref:"containerRef",class:[`${t}-scrollbar-container`,this.containerClass],style:this.containerStyle,onScroll:this.handleScroll,onWheel:this.onWheel},$(zr,{onResize:this.handleContentResize},{default:()=>$("div",{ref:"contentRef",role:"none",style:[{width:this.xScrollable?"fit-content":null},this.contentStyle],class:[`${t}-scrollbar-content`,this.contentClass]},r)})),l?null:D(void 0,void 0),R&&$("div",{ref:"xRailRef",class:[`${t}-scrollbar-rail`,`${t}-scrollbar-rail--horizontal`,`${t}-scrollbar-rail--horizontal--${H}`],style:this.horizontalRailStyle,"data-scrollbar-rail":!0,"aria-hidden":!0},$(T?Er:dr,T?null:{name:"fade-in-transition"},{default:()=>this.needXBar&&this.isShowXBar&&!this.isIos?$("div",{class:`${t}-scrollbar-rail__scrollbar`,style:{width:this.xBarSizePx,right:o?this.xBarLeftPx:void 0,left:o?void 0:this.xBarLeftPx},onMousedown:this.handleXScrollMouseDown}):null}))])},q=this.container?b():$(zr,{onResize:this.handleContainerResize},{default:b});return l?$(Dr,null,q,D(this.themeClass,this.cssVars)):q}}),hn=Mo,{cubicBezierEaseInOut:le}=_r;function ko({duration:e=".2s",delay:r=".1s"}={}){return[P("&.fade-in-width-expand-transition-leave-from, &.fade-in-width-expand-transition-enter-to",{opacity:1}),P("&.fade-in-width-expand-transition-leave-to, &.fade-in-width-expand-transition-enter-from",`
 opacity: 0!important;
 margin-left: 0!important;
 margin-right: 0!important;
 `),P("&.fade-in-width-expand-transition-leave-active",`
 overflow: hidden;
 transition:
 opacity ${e} ${le},
 max-width ${e} ${le} ${r},
 margin-left ${e} ${le} ${r},
 margin-right ${e} ${le} ${r};
 `),P("&.fade-in-width-expand-transition-enter-active",`
 overflow: hidden;
 transition:
 opacity ${e} ${le} ${r},
 max-width ${e} ${le},
 margin-left ${e} ${le},
 margin-right ${e} ${le};
 `)]}const Fo=ie("base-wave",`
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 border-radius: inherit;
`),Lo=pe({name:"BaseWave",props:{clsPrefix:{type:String,required:!0}},setup(e){Pt("-base-wave",Fo,Bt(e,"clsPrefix"));const r=j(null),t=j(!1);let n=null;return Fe(()=>{n!==null&&window.clearTimeout(n)}),{active:t,selfRef:r,play(){n!==null&&(window.clearTimeout(n),t.value=!1,n=null),Et(()=>{var o;(o=r.value)===null||o===void 0||o.offsetHeight,t.value=!0,n=window.setTimeout(()=>{t.value=!1,n=null},1e3)})}}},render(){const{clsPrefix:e}=this;return $("div",{ref:"selfRef","aria-hidden":!0,class:[`${e}-base-wave`,this.active&&`${e}-base-wave--active`]})}}),_o=Le&&"chrome"in window;Le&&navigator.userAgent.includes("Firefox");const No=Le&&navigator.userAgent.includes("Safari")&&!_o;function fe(e){return Vr(e,[255,255,255,.16])}function We(e){return Vr(e,[0,0,0,.12])}const Go=Mr("n-button-group"),Vo={paddingTiny:"0 6px",paddingSmall:"0 10px",paddingMedium:"0 14px",paddingLarge:"0 18px",paddingRoundTiny:"0 10px",paddingRoundSmall:"0 14px",paddingRoundMedium:"0 18px",paddingRoundLarge:"0 22px",iconMarginTiny:"6px",iconMarginSmall:"6px",iconMarginMedium:"6px",iconMarginLarge:"6px",iconSizeTiny:"14px",iconSizeSmall:"18px",iconSizeMedium:"18px",iconSizeLarge:"20px",rippleDuration:".6s"};function Xo(e){const{heightTiny:r,heightSmall:t,heightMedium:n,heightLarge:o,borderRadius:l,fontSizeTiny:u,fontSizeSmall:H,fontSizeMedium:R,fontSizeLarge:T,opacityDisabled:D,textColor2:b,textColor3:q,primaryColorHover:p,primaryColorPressed:E,borderColor:Z,primaryColor:F,baseColor:v,infoColor:N,infoColorHover:W,infoColorPressed:f,successColor:m,successColorHover:y,successColorPressed:i,warningColor:s,warningColorHover:d,warningColorPressed:g,errorColor:w,errorColorHover:C,errorColorPressed:V,fontWeight:O,buttonColor2:X,buttonColor2Hover:M,buttonColor2Pressed:S,fontWeightStrong:K}=e;return Object.assign(Object.assign({},Vo),{heightTiny:r,heightSmall:t,heightMedium:n,heightLarge:o,borderRadiusTiny:l,borderRadiusSmall:l,borderRadiusMedium:l,borderRadiusLarge:l,fontSizeTiny:u,fontSizeSmall:H,fontSizeMedium:R,fontSizeLarge:T,opacityDisabled:D,colorOpacitySecondary:"0.16",colorOpacitySecondaryHover:"0.22",colorOpacitySecondaryPressed:"0.28",colorSecondary:X,colorSecondaryHover:M,colorSecondaryPressed:S,colorTertiary:X,colorTertiaryHover:M,colorTertiaryPressed:S,colorQuaternary:"#0000",colorQuaternaryHover:M,colorQuaternaryPressed:S,color:"#0000",colorHover:"#0000",colorPressed:"#0000",colorFocus:"#0000",colorDisabled:"#0000",textColor:b,textColorTertiary:q,textColorHover:p,textColorPressed:E,textColorFocus:p,textColorDisabled:b,textColorText:b,textColorTextHover:p,textColorTextPressed:E,textColorTextFocus:p,textColorTextDisabled:b,textColorGhost:b,textColorGhostHover:p,textColorGhostPressed:E,textColorGhostFocus:p,textColorGhostDisabled:b,border:`1px solid ${Z}`,borderHover:`1px solid ${p}`,borderPressed:`1px solid ${E}`,borderFocus:`1px solid ${p}`,borderDisabled:`1px solid ${Z}`,rippleColor:F,colorPrimary:F,colorHoverPrimary:p,colorPressedPrimary:E,colorFocusPrimary:p,colorDisabledPrimary:F,textColorPrimary:v,textColorHoverPrimary:v,textColorPressedPrimary:v,textColorFocusPrimary:v,textColorDisabledPrimary:v,textColorTextPrimary:F,textColorTextHoverPrimary:p,textColorTextPressedPrimary:E,textColorTextFocusPrimary:p,textColorTextDisabledPrimary:b,textColorGhostPrimary:F,textColorGhostHoverPrimary:p,textColorGhostPressedPrimary:E,textColorGhostFocusPrimary:p,textColorGhostDisabledPrimary:F,borderPrimary:`1px solid ${F}`,borderHoverPrimary:`1px solid ${p}`,borderPressedPrimary:`1px solid ${E}`,borderFocusPrimary:`1px solid ${p}`,borderDisabledPrimary:`1px solid ${F}`,rippleColorPrimary:F,colorInfo:N,colorHoverInfo:W,colorPressedInfo:f,colorFocusInfo:W,colorDisabledInfo:N,textColorInfo:v,textColorHoverInfo:v,textColorPressedInfo:v,textColorFocusInfo:v,textColorDisabledInfo:v,textColorTextInfo:N,textColorTextHoverInfo:W,textColorTextPressedInfo:f,textColorTextFocusInfo:W,textColorTextDisabledInfo:b,textColorGhostInfo:N,textColorGhostHoverInfo:W,textColorGhostPressedInfo:f,textColorGhostFocusInfo:W,textColorGhostDisabledInfo:N,borderInfo:`1px solid ${N}`,borderHoverInfo:`1px solid ${W}`,borderPressedInfo:`1px solid ${f}`,borderFocusInfo:`1px solid ${W}`,borderDisabledInfo:`1px solid ${N}`,rippleColorInfo:N,colorSuccess:m,colorHoverSuccess:y,colorPressedSuccess:i,colorFocusSuccess:y,colorDisabledSuccess:m,textColorSuccess:v,textColorHoverSuccess:v,textColorPressedSuccess:v,textColorFocusSuccess:v,textColorDisabledSuccess:v,textColorTextSuccess:m,textColorTextHoverSuccess:y,textColorTextPressedSuccess:i,textColorTextFocusSuccess:y,textColorTextDisabledSuccess:b,textColorGhostSuccess:m,textColorGhostHoverSuccess:y,textColorGhostPressedSuccess:i,textColorGhostFocusSuccess:y,textColorGhostDisabledSuccess:m,borderSuccess:`1px solid ${m}`,borderHoverSuccess:`1px solid ${y}`,borderPressedSuccess:`1px solid ${i}`,borderFocusSuccess:`1px solid ${y}`,borderDisabledSuccess:`1px solid ${m}`,rippleColorSuccess:m,colorWarning:s,colorHoverWarning:d,colorPressedWarning:g,colorFocusWarning:d,colorDisabledWarning:s,textColorWarning:v,textColorHoverWarning:v,textColorPressedWarning:v,textColorFocusWarning:v,textColorDisabledWarning:v,textColorTextWarning:s,textColorTextHoverWarning:d,textColorTextPressedWarning:g,textColorTextFocusWarning:d,textColorTextDisabledWarning:b,textColorGhostWarning:s,textColorGhostHoverWarning:d,textColorGhostPressedWarning:g,textColorGhostFocusWarning:d,textColorGhostDisabledWarning:s,borderWarning:`1px solid ${s}`,borderHoverWarning:`1px solid ${d}`,borderPressedWarning:`1px solid ${g}`,borderFocusWarning:`1px solid ${d}`,borderDisabledWarning:`1px solid ${s}`,rippleColorWarning:s,colorError:w,colorHoverError:C,colorPressedError:V,colorFocusError:C,colorDisabledError:w,textColorError:v,textColorHoverError:v,textColorPressedError:v,textColorFocusError:v,textColorDisabledError:v,textColorTextError:w,textColorTextHoverError:C,textColorTextPressedError:V,textColorTextFocusError:C,textColorTextDisabledError:b,textColorGhostError:w,textColorGhostHoverError:C,textColorGhostPressedError:V,textColorGhostFocusError:C,textColorGhostDisabledError:w,borderError:`1px solid ${w}`,borderHoverError:`1px solid ${C}`,borderPressedError:`1px solid ${V}`,borderFocusError:`1px solid ${C}`,borderDisabledError:`1px solid ${w}`,rippleColorError:w,waveOpacity:"0.6",fontWeight:O,fontWeightStrong:K})}const jo={name:"Button",common:Je,self:Xo},Ao=P([ie("button",`
 margin: 0;
 font-weight: var(--n-font-weight);
 line-height: 1;
 font-family: inherit;
 padding: var(--n-padding);
 height: var(--n-height);
 font-size: var(--n-font-size);
 border-radius: var(--n-border-radius);
 color: var(--n-text-color);
 background-color: var(--n-color);
 width: var(--n-width);
 white-space: nowrap;
 outline: none;
 position: relative;
 z-index: auto;
 border: none;
 display: inline-flex;
 flex-wrap: nowrap;
 flex-shrink: 0;
 align-items: center;
 justify-content: center;
 user-select: none;
 -webkit-user-select: none;
 text-align: center;
 cursor: pointer;
 text-decoration: none;
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[Y("color",[G("border",{borderColor:"var(--n-border-color)"}),Y("disabled",[G("border",{borderColor:"var(--n-border-color-disabled)"})]),ur("disabled",[P("&:focus",[G("state-border",{borderColor:"var(--n-border-color-focus)"})]),P("&:hover",[G("state-border",{borderColor:"var(--n-border-color-hover)"})]),P("&:active",[G("state-border",{borderColor:"var(--n-border-color-pressed)"})]),Y("pressed",[G("state-border",{borderColor:"var(--n-border-color-pressed)"})])])]),Y("disabled",{backgroundColor:"var(--n-color-disabled)",color:"var(--n-text-color-disabled)"},[G("border",{border:"var(--n-border-disabled)"})]),ur("disabled",[P("&:focus",{backgroundColor:"var(--n-color-focus)",color:"var(--n-text-color-focus)"},[G("state-border",{border:"var(--n-border-focus)"})]),P("&:hover",{backgroundColor:"var(--n-color-hover)",color:"var(--n-text-color-hover)"},[G("state-border",{border:"var(--n-border-hover)"})]),P("&:active",{backgroundColor:"var(--n-color-pressed)",color:"var(--n-text-color-pressed)"},[G("state-border",{border:"var(--n-border-pressed)"})]),Y("pressed",{backgroundColor:"var(--n-color-pressed)",color:"var(--n-text-color-pressed)"},[G("state-border",{border:"var(--n-border-pressed)"})])]),Y("loading","cursor: wait;"),ie("base-wave",`
 pointer-events: none;
 top: 0;
 right: 0;
 bottom: 0;
 left: 0;
 animation-iteration-count: 1;
 animation-duration: var(--n-ripple-duration);
 animation-timing-function: var(--n-bezier-ease-out), var(--n-bezier-ease-out);
 `,[Y("active",{zIndex:1,animationName:"button-wave-spread, button-wave-opacity"})]),Le&&"MozBoxSizing"in document.createElement("div").style?P("&::moz-focus-inner",{border:0}):null,G("border, state-border",`
 position: absolute;
 left: 0;
 top: 0;
 right: 0;
 bottom: 0;
 border-radius: inherit;
 transition: border-color .3s var(--n-bezier);
 pointer-events: none;
 `),G("border",{border:"var(--n-border)"}),G("state-border",{border:"var(--n-border)",borderColor:"#0000",zIndex:1}),G("icon",`
 margin: var(--n-icon-margin);
 margin-left: 0;
 height: var(--n-icon-size);
 width: var(--n-icon-size);
 max-width: var(--n-icon-size);
 font-size: var(--n-icon-size);
 position: relative;
 flex-shrink: 0;
 `,[ie("icon-slot",`
 height: var(--n-icon-size);
 width: var(--n-icon-size);
 position: absolute;
 left: 0;
 top: 50%;
 transform: translateY(-50%);
 display: flex;
 align-items: center;
 justify-content: center;
 `,[Ht({top:"50%",originalTransform:"translateY(-50%)"})]),ko()]),G("content",`
 display: flex;
 align-items: center;
 flex-wrap: nowrap;
 min-width: 0;
 `,[P("~",[G("icon",{margin:"var(--n-icon-margin)",marginRight:0})])]),Y("block",`
 display: flex;
 width: 100%;
 `),Y("dashed",[G("border, state-border",{borderStyle:"dashed !important"})]),Y("disabled",{cursor:"not-allowed",opacity:"var(--n-opacity-disabled)"})]),P("@keyframes button-wave-spread",{from:{boxShadow:"0 0 0.5px 0 var(--n-ripple-color)"},to:{boxShadow:"0 0 0.5px 4.5px var(--n-ripple-color)"}}),P("@keyframes button-wave-opacity",{from:{opacity:"var(--n-wave-opacity)"},to:{opacity:0}})]),Yo=Object.assign(Object.assign({},ye.props),{color:String,textColor:String,text:Boolean,block:Boolean,loading:Boolean,disabled:Boolean,circle:Boolean,size:String,ghost:Boolean,round:Boolean,secondary:Boolean,tertiary:Boolean,quaternary:Boolean,strong:Boolean,focusable:{type:Boolean,default:!0},keyboard:{type:Boolean,default:!0},tag:{type:String,default:"button"},type:{type:String,default:"default"},dashed:Boolean,renderIcon:Function,iconPlacement:{type:String,default:"left"},attrType:{type:String,default:"button"},bordered:{type:Boolean,default:!0},onClick:[Function,Array],nativeFocusBehavior:{type:Boolean,default:!No}}),Uo=pe({name:"Button",props:Yo,slots:Object,setup(e){const r=j(null),t=j(null),n=j(!1),o=Dt(()=>!e.quaternary&&!e.tertiary&&!e.secondary&&!e.text&&(!e.color||e.ghost||e.dashed)&&e.bordered),l=Wr(Go,{}),{mergedSizeRef:u}=go({},{defaultSize:"medium",mergedSize:f=>{const{size:m}=e;if(m)return m;const{size:y}=l;if(y)return y;const{mergedSize:i}=f||{};return i?i.value:"medium"}}),H=I(()=>e.focusable&&!e.disabled),R=f=>{var m;H.value||f.preventDefault(),!e.nativeFocusBehavior&&(f.preventDefault(),!e.disabled&&H.value&&((m=r.value)===null||m===void 0||m.focus({preventScroll:!0})))},T=f=>{var m;if(!e.disabled&&!e.loading){const{onClick:y}=e;y&&Qr(y,f),e.text||(m=t.value)===null||m===void 0||m.play()}},D=f=>{switch(f.key){case"Enter":if(!e.keyboard)return;n.value=!1}},b=f=>{switch(f.key){case"Enter":if(!e.keyboard||e.loading){f.preventDefault();return}n.value=!0}},q=()=>{n.value=!1},{inlineThemeDisabled:p,mergedClsPrefixRef:E,mergedRtlRef:Z}=er(e),F=ye("Button","-button",Ao,jo,e,E),v=Nr("Button",Z,E),N=I(()=>{const f=F.value,{common:{cubicBezierEaseInOut:m,cubicBezierEaseOut:y},self:i}=f,{rippleDuration:s,opacityDisabled:d,fontWeight:g,fontWeightStrong:w}=i,C=u.value,{dashed:V,type:O,ghost:X,text:M,color:S,round:K,circle:ee,textColor:Q,secondary:oe,tertiary:ce,quaternary:He,strong:_e}=e,Ne={"--n-font-weight":_e?w:g};let B={"--n-color":"initial","--n-color-hover":"initial","--n-color-pressed":"initial","--n-color-focus":"initial","--n-color-disabled":"initial","--n-ripple-color":"initial","--n-text-color":"initial","--n-text-color-hover":"initial","--n-text-color-pressed":"initial","--n-text-color-focus":"initial","--n-text-color-disabled":"initial"};const ge=O==="tertiary",Ce=O==="default",z=ge?"default":O;if(M){const L=Q||S;B={"--n-color":"#0000","--n-color-hover":"#0000","--n-color-pressed":"#0000","--n-color-focus":"#0000","--n-color-disabled":"#0000","--n-ripple-color":"#0000","--n-text-color":L||i[x("textColorText",z)],"--n-text-color-hover":L?fe(L):i[x("textColorTextHover",z)],"--n-text-color-pressed":L?We(L):i[x("textColorTextPressed",z)],"--n-text-color-focus":L?fe(L):i[x("textColorTextHover",z)],"--n-text-color-disabled":L||i[x("textColorTextDisabled",z)]}}else if(X||V){const L=Q||S;B={"--n-color":"#0000","--n-color-hover":"#0000","--n-color-pressed":"#0000","--n-color-focus":"#0000","--n-color-disabled":"#0000","--n-ripple-color":S||i[x("rippleColor",z)],"--n-text-color":L||i[x("textColorGhost",z)],"--n-text-color-hover":L?fe(L):i[x("textColorGhostHover",z)],"--n-text-color-pressed":L?We(L):i[x("textColorGhostPressed",z)],"--n-text-color-focus":L?fe(L):i[x("textColorGhostHover",z)],"--n-text-color-disabled":L||i[x("textColorGhostDisabled",z)]}}else if(oe){const L=Ce?i.textColor:ge?i.textColorTertiary:i[x("color",z)],U=S||L,de=O!=="default"&&O!=="tertiary";B={"--n-color":de?$e(U,{alpha:Number(i.colorOpacitySecondary)}):i.colorSecondary,"--n-color-hover":de?$e(U,{alpha:Number(i.colorOpacitySecondaryHover)}):i.colorSecondaryHover,"--n-color-pressed":de?$e(U,{alpha:Number(i.colorOpacitySecondaryPressed)}):i.colorSecondaryPressed,"--n-color-focus":de?$e(U,{alpha:Number(i.colorOpacitySecondaryHover)}):i.colorSecondaryHover,"--n-color-disabled":i.colorSecondary,"--n-ripple-color":"#0000","--n-text-color":U,"--n-text-color-hover":U,"--n-text-color-pressed":U,"--n-text-color-focus":U,"--n-text-color-disabled":U}}else if(ce||He){const L=Ce?i.textColor:ge?i.textColorTertiary:i[x("color",z)],U=S||L;ce?(B["--n-color"]=i.colorTertiary,B["--n-color-hover"]=i.colorTertiaryHover,B["--n-color-pressed"]=i.colorTertiaryPressed,B["--n-color-focus"]=i.colorSecondaryHover,B["--n-color-disabled"]=i.colorTertiary):(B["--n-color"]=i.colorQuaternary,B["--n-color-hover"]=i.colorQuaternaryHover,B["--n-color-pressed"]=i.colorQuaternaryPressed,B["--n-color-focus"]=i.colorQuaternaryHover,B["--n-color-disabled"]=i.colorQuaternary),B["--n-ripple-color"]="#0000",B["--n-text-color"]=U,B["--n-text-color-hover"]=U,B["--n-text-color-pressed"]=U,B["--n-text-color-focus"]=U,B["--n-text-color-disabled"]=U}else B={"--n-color":S||i[x("color",z)],"--n-color-hover":S?fe(S):i[x("colorHover",z)],"--n-color-pressed":S?We(S):i[x("colorPressed",z)],"--n-color-focus":S?fe(S):i[x("colorFocus",z)],"--n-color-disabled":S||i[x("colorDisabled",z)],"--n-ripple-color":S||i[x("rippleColor",z)],"--n-text-color":Q||(S?i.textColorPrimary:ge?i.textColorTertiary:i[x("textColor",z)]),"--n-text-color-hover":Q||(S?i.textColorHoverPrimary:i[x("textColorHover",z)]),"--n-text-color-pressed":Q||(S?i.textColorPressedPrimary:i[x("textColorPressed",z)]),"--n-text-color-focus":Q||(S?i.textColorFocusPrimary:i[x("textColorFocus",z)]),"--n-text-color-disabled":Q||(S?i.textColorDisabledPrimary:i[x("textColorDisabled",z)])};let Se={"--n-border":"initial","--n-border-hover":"initial","--n-border-pressed":"initial","--n-border-focus":"initial","--n-border-disabled":"initial"};M?Se={"--n-border":"none","--n-border-hover":"none","--n-border-pressed":"none","--n-border-focus":"none","--n-border-disabled":"none"}:Se={"--n-border":i[x("border",z)],"--n-border-hover":i[x("borderHover",z)],"--n-border-pressed":i[x("borderPressed",z)],"--n-border-focus":i[x("borderFocus",z)],"--n-border-disabled":i[x("borderDisabled",z)]};const{[x("height",C)]:ze,[x("fontSize",C)]:Ge,[x("padding",C)]:ae,[x("paddingRound",C)]:Ve,[x("iconSize",C)]:Xe,[x("borderRadius",C)]:Te,[x("iconMargin",C)]:je,waveOpacity:Ae}=i,Ye={"--n-width":ee&&!M?ze:"initial","--n-height":M?"initial":ze,"--n-font-size":Ge,"--n-padding":ee||M?"initial":K?Ve:ae,"--n-icon-size":Xe,"--n-icon-margin":je,"--n-border-radius":M?"initial":ee||K?ze:Te};return Object.assign(Object.assign(Object.assign(Object.assign({"--n-bezier":m,"--n-bezier-ease-out":y,"--n-ripple-duration":s,"--n-opacity-disabled":d,"--n-wave-opacity":Ae},Ne),B),Se),Ye)}),W=p?rr("button",I(()=>{let f="";const{dashed:m,type:y,ghost:i,text:s,color:d,round:g,circle:w,textColor:C,secondary:V,tertiary:O,quaternary:X,strong:M}=e;m&&(f+="a"),i&&(f+="b"),s&&(f+="c"),g&&(f+="d"),w&&(f+="e"),V&&(f+="f"),O&&(f+="g"),X&&(f+="h"),M&&(f+="i"),d&&(f+=`j${Tr(d)}`),C&&(f+=`k${Tr(C)}`);const{value:S}=u;return f+=`l${S[0]}`,f+=`m${y[0]}`,f}),N,e):void 0;return{selfElRef:r,waveElRef:t,mergedClsPrefix:E,mergedFocusable:H,mergedSize:u,showBorder:o,enterPressed:n,rtlEnabled:v,handleMousedown:R,handleKeydown:b,handleBlur:q,handleKeyup:D,handleClick:T,customColorCssVars:I(()=>{const{color:f}=e;if(!f)return null;const m=fe(f);return{"--n-border-color":f,"--n-border-color-hover":m,"--n-border-color-pressed":We(f),"--n-border-color-focus":m,"--n-border-color-disabled":f}}),cssVars:p?void 0:N,themeClass:W?.themeClass,onRender:W?.onRender}},render(){const{mergedClsPrefix:e,tag:r,onRender:t}=this;t?.();const n=Br(this.$slots.default,o=>o&&$("span",{class:`${e}-button__content`},o));return $(r,{ref:"selfElRef",class:[this.themeClass,`${e}-button`,`${e}-button--${this.type}-type`,`${e}-button--${this.mergedSize}-type`,this.rtlEnabled&&`${e}-button--rtl`,this.disabled&&`${e}-button--disabled`,this.block&&`${e}-button--block`,this.enterPressed&&`${e}-button--pressed`,!this.text&&this.dashed&&`${e}-button--dashed`,this.color&&`${e}-button--color`,this.secondary&&`${e}-button--secondary`,this.loading&&`${e}-button--loading`,this.ghost&&`${e}-button--ghost`],tabindex:this.mergedFocusable?0:-1,type:this.attrType,style:this.cssVars,disabled:this.disabled,onClick:this.handleClick,onBlur:this.handleBlur,onMousedown:this.handleMousedown,onKeyup:this.handleKeyup,onKeydown:this.handleKeydown},this.iconPlacement==="right"&&n,$($t,{width:!0},{default:()=>Br(this.$slots.icon,o=>(this.loading||this.renderIcon||o)&&$("span",{class:`${e}-button__icon`,style:{margin:po(this.$slots.default)?"0":""}},$(Ot,null,{default:()=>this.loading?$(It,{clsPrefix:e,key:"loading",class:`${e}-icon-slot`,strokeWidth:20}):$("div",{key:"icon",class:`${e}-icon-slot`,role:"none"},this.renderIcon?this.renderIcon():o)})))}),this.iconPlacement==="left"&&n,this.text?null:$(Lo,{ref:"waveElRef",clsPrefix:e}),this.showBorder?$("div",{"aria-hidden":!0,class:`${e}-button__border`,style:this.customColorCssVars}):null,this.showBorder?$("div",{"aria-hidden":!0,class:`${e}-button__state-border`,style:this.customColorCssVars}):null)}}),vn=Uo;function qo(e){const{textColorBase:r,opacity1:t,opacity2:n,opacity3:o,opacity4:l,opacity5:u}=e;return{color:r,opacity1Depth:t,opacity2Depth:n,opacity3Depth:o,opacity4Depth:l,opacity5Depth:u}}const Ko={common:Je,self:qo},Qo=ie("icon",`
 height: 1em;
 width: 1em;
 line-height: 1em;
 text-align: center;
 display: inline-block;
 position: relative;
 fill: currentColor;
`,[Y("color-transition",{transition:"color .3s var(--n-bezier)"}),Y("depth",{color:"var(--n-color)"},[P("svg",{opacity:"var(--n-opacity)",transition:"opacity .3s var(--n-bezier)"})]),P("svg",{height:"1em",width:"1em"})]),Zo=Object.assign(Object.assign({},ye.props),{depth:[String,Number],size:[Number,String],color:String,component:[Object,Function]}),bn=pe({_n_icon__:!0,name:"Icon",inheritAttrs:!1,props:Zo,setup(e){const{mergedClsPrefixRef:r,inlineThemeDisabled:t}=er(e),n=ye("Icon","-icon",Qo,Ko,e,r),o=I(()=>{const{depth:u}=e,{common:{cubicBezierEaseInOut:H},self:R}=n.value;if(u!==void 0){const{color:T,[`opacity${u}Depth`]:D}=R;return{"--n-bezier":H,"--n-color":T,"--n-opacity":D}}return{"--n-bezier":H,"--n-color":"","--n-opacity":""}}),l=t?rr("icon",I(()=>`${e.depth||"d"}`),o,e):void 0;return{mergedClsPrefix:r,mergedStyle:I(()=>{const{size:u,color:H}=e;return{fontSize:bo(u),color:H}}),cssVars:t?void 0:o,themeClass:l?.themeClass,onRender:l?.onRender}},render(){var e;const{$parent:r,depth:t,mergedClsPrefix:n,component:o,onRender:l,themeClass:u}=this;return!((e=r?.$options)===null||e===void 0)&&e._n_icon__&&Wt("icon","don't wrap `n-icon` inside `n-icon`"),l?.(),$("i",Gr(this.$attrs,{role:"img",class:[`${n}-icon`,u,{[`${n}-icon--depth`]:t,[`${n}-icon--color-transition`]:t!==void 0}],style:[this.cssVars,this.mergedStyle]}),o?$(o):this.$slots)}}),pn=(e,r)=>{const t=e.__vccOpts||e;for(const[n,o]of r)t[n]=o;return t};export{Sr as A,Uo as B,fn as C,Tr as D,No as E,we as F,Po as G,Bo as H,yo as I,Eo as J,po as K,hn as L,bn as N,Mo as S,zr as V,Er as W,vn as X,pn as _,nn as a,dn as b,Qr as c,Xt as d,jo as e,bo as f,Br as g,rn as h,Mt as i,ln as j,sn as k,go as l,Le as m,on as n,fr as o,Go as p,Ho as q,cn as r,Io as s,tn as t,an as u,Re as v,Pe as w,he as x,un as y,Hr as z};
