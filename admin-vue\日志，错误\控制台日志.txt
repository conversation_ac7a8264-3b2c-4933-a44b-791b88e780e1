检查认证状态: {user: {…}, isAuth: true}
auth.ts:78 认证状态更新后: {token: '已设置', username: '1060352824', isAuthenticated: true}
auth.ts:68 检查认证状态: {user: {…}, isAuth: true}
auth.ts:78 认证状态更新后: {token: '已设置', username: '1060352824', isAuthenticated: true}
auth.ts:68 检查认证状态: {user: {…}, isAuth: true}
auth.ts:78 认证状态更新后: {token: '已设置', username: '1060352824', isAuthenticated: true}
index.ts:49 路由守卫检查: {to: '/users', isAuthenticated: true, requiresAuth: true}
runtime-core.esm-bundler.js:51  [Vue warn]: Invalid prop: type check failed for prop "rowKey". Expected Function, got String with value "username". 
  at <DataTable columns= (6) [{…}, {…}, {…}, {…}, {…}, {…}] data= [] loading=false  ... > 
  at <DataTable data= [] columns= (6) [{…}, {…}, {…}, {…}, {…}, {…}] loading=false  ... > 
  at <UsersView onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > > 
  at <RouterView > 
  at <LayoutContent class="app-content" > 
  at <Layout class="right-layout" > 
  at <Layout class="app-layout" has-sider="" > 
  at <AppLayout onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< undefined > > 
  at <KeepAlive > 
  at <RouterView > 
  at <ConfigProvider locale= {name: 'zh-CN', global: {…}, Popconfirm: {…}, Cascader: {…}, Time: {…}, …} date-locale= {name: 'zh-CN', locale: {…}} > 
  at <App>
warn$1 @ runtime-core.esm-bundler.js:51
validateProp @ runtime-core.esm-bundler.js:4409
validateProps @ runtime-core.esm-bundler.js:4381
initProps @ runtime-core.esm-bundler.js:4082
setupComponent @ runtime-core.esm-bundler.js:7924
mountComponent @ runtime-core.esm-bundler.js:5247
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
mountChildren @ runtime-core.esm-bundler.js:4963
mountElement @ runtime-core.esm-bundler.js:4886
processElement @ runtime-core.esm-bundler.js:4851
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5358
run @ reactivity.esm-bundler.js:237
setupRenderEffect @ runtime-core.esm-bundler.js:5486
mountComponent @ runtime-core.esm-bundler.js:5260
processComponent @ runtime-core.esm-bundler.js:5213
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7379
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6255
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:12
UsersView.vue:200 UsersView mounted - 加载用户数据
UsersView.vue:206 UsersView activated - 重新加载用户数据
runtime-core.esm-bundler.js:51  [Vue warn]: Invalid prop: type check failed for prop "rowKey". Expected Function, got String with value "username". 
  at <DataTable columns= (6) [{…}, {…}, {…}, {…}, {…}, {…}] data= [] loading=true  ... > 
  at <DataTable data= [] columns= (6) [{…}, {…}, {…}, {…}, {…}, {…}] loading=true  ... > 
  at <UsersView onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <RouterView > 
  at <LayoutContent class="app-content" > 
  at <Layout class="right-layout" > 
  at <Layout class="app-layout" has-sider="" > 
  at <AppLayout onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <KeepAlive > 
  at <RouterView > 
  at <ConfigProvider locale= {name: 'zh-CN', global: {…}, Popconfirm: {…}, Cascader: {…}, Time: {…}, …} date-locale= {name: 'zh-CN', locale: {…}} > 
  at <App>
warn$1 @ runtime-core.esm-bundler.js:51
validateProp @ runtime-core.esm-bundler.js:4409
validateProps @ runtime-core.esm-bundler.js:4381
updateProps @ runtime-core.esm-bundler.js:4190
updateComponentPreRender @ runtime-core.esm-bundler.js:5493
componentUpdateFn @ runtime-core.esm-bundler.js:5415
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
flushJobs @ runtime-core.esm-bundler.js:430
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7379
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6255
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:12
pinia.mjs:281 🍍 "users" store installed 🆕
runtime-core.esm-bundler.js:51  [Vue warn]: Invalid prop: type check failed for prop "rowKey". Expected Function, got String with value "username". 
  at <DataTable columns= (6) [{…}, {…}, {…}, {…}, {…}, {…}] data= (306) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), …] loading=false  ... > 
  at <DataTable data= (306) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), …] columns= (6) [{…}, {…}, {…}, {…}, {…}, {…}] loading=false  ... > 
  at <UsersView onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <RouterView > 
  at <LayoutContent class="app-content" > 
  at <Layout class="right-layout" > 
  at <Layout class="app-layout" has-sider="" > 
  at <AppLayout onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <KeepAlive > 
  at <RouterView > 
  at <ConfigProvider locale= {name: 'zh-CN', global: {…}, Popconfirm: {…}, Cascader: {…}, Time: {…}, …} date-locale= {name: 'zh-CN', locale: {…}} > 
  at <App>
warn$1 @ runtime-core.esm-bundler.js:51
validateProp @ runtime-core.esm-bundler.js:4409
validateProps @ runtime-core.esm-bundler.js:4381
updateProps @ runtime-core.esm-bundler.js:4190
updateComponentPreRender @ runtime-core.esm-bundler.js:5493
componentUpdateFn @ runtime-core.esm-bundler.js:5415
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
loadUsers @ users.ts:140
await in loadUsers
wrappedAction @ pinia.mjs:1394
store.<computed> @ pinia.mjs:946
(匿名) @ UsersView.vue:207
hook.__wdc.hook.__wdc @ runtime-core.esm-bundler.js:2777
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7379
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6255
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:12
runtime-core.esm-bundler.js:51  [Vue warn]: Unhandled error during execution of component update 
  at <UsersView onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <RouterView > 
  at <LayoutContent class="app-content" > 
  at <Layout class="right-layout" > 
  at <Layout class="app-layout" has-sider="" > 
  at <AppLayout onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <KeepAlive > 
  at <RouterView > 
  at <ConfigProvider locale= {name: 'zh-CN', global: {…}, Popconfirm: {…}, Cascader: {…}, Time: {…}, …} date-locale= {name: 'zh-CN', locale: {…}} > 
  at <App>
warn$1 @ runtime-core.esm-bundler.js:51
logError @ runtime-core.esm-bundler.js:263
handleError @ runtime-core.esm-bundler.js:255
callWithErrorHandling @ runtime-core.esm-bundler.js:201
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
loadUsers @ users.ts:140
await in loadUsers
wrappedAction @ pinia.mjs:1394
store.<computed> @ pinia.mjs:946
(匿名) @ UsersView.vue:207
hook.__wdc.hook.__wdc @ runtime-core.esm-bundler.js:2777
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7379
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6255
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:12
create.js:58  Uncaught (in promise) TypeError: getKey is not a function
    at get key (create.js:58:20)
    at create.js:30:34
    at Array.forEach (<anonymous>)
    at createTreeNodes (create.js:9:14)
    at createTreeMate (create.js:79:23)
    at ComputedRefImpl.fn (use-table-data.mjs:30:12)
    at refreshComputed (reactivity.esm-bundler.js:391:28)
    at isDirty (reactivity.esm-bundler.js:362:68)
    at refreshComputed (reactivity.esm-bundler.js:380:90)
    at isDirty (reactivity.esm-bundler.js:362:68)
get key @ create.js:58
(匿名) @ create.js:30
createTreeNodes @ create.js:9
createTreeMate @ create.js:79
(匿名) @ use-table-data.mjs:30
refreshComputed @ reactivity.esm-bundler.js:391
isDirty @ reactivity.esm-bundler.js:362
refreshComputed @ reactivity.esm-bundler.js:380
isDirty @ reactivity.esm-bundler.js:362
refreshComputed @ reactivity.esm-bundler.js:380
isDirty @ reactivity.esm-bundler.js:362
get dirty @ reactivity.esm-bundler.js:279
job @ reactivity.esm-bundler.js:1812
flushPreFlushCbs @ runtime-core.esm-bundler.js:356
updateComponentPreRender @ runtime-core.esm-bundler.js:5496
componentUpdateFn @ runtime-core.esm-bundler.js:5415
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
loadUsers @ users.ts:140
await in loadUsers
wrappedAction @ pinia.mjs:1394
store.<computed> @ pinia.mjs:946
(匿名) @ UsersView.vue:207
hook.__wdc.hook.__wdc @ runtime-core.esm-bundler.js:2777
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7379
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6255
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:12
runtime-core.esm-bundler.js:51  [Vue warn]: Invalid prop: type check failed for prop "rowKey". Expected Function, got String with value "username". 
  at <DataTable columns= (6) [{…}, {…}, {…}, {…}, {…}, {…}] data= (306) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), …] loading=false  ... > 
  at <DataTable data= (306) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object), …] columns= (6) [{…}, {…}, {…}, {…}, {…}, {…}] loading=false  ... > 
  at <UsersView onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <RouterView > 
  at <LayoutContent class="app-content" > 
  at <Layout class="right-layout" > 
  at <Layout class="app-layout" has-sider="" > 
  at <AppLayout onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <KeepAlive > 
  at <RouterView > 
  at <ConfigProvider locale= {name: 'zh-CN', global: {…}, Popconfirm: {…}, Cascader: {…}, Time: {…}, …} date-locale= {name: 'zh-CN', locale: {…}} > 
  at <App>
warn$1 @ runtime-core.esm-bundler.js:51
validateProp @ runtime-core.esm-bundler.js:4409
validateProps @ runtime-core.esm-bundler.js:4381
updateProps @ runtime-core.esm-bundler.js:4190
updateComponentPreRender @ runtime-core.esm-bundler.js:5493
componentUpdateFn @ runtime-core.esm-bundler.js:5415
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
updateComponent @ runtime-core.esm-bundler.js:5289
processComponent @ runtime-core.esm-bundler.js:5224
patch @ runtime-core.esm-bundler.js:4729
patchBlockChildren @ runtime-core.esm-bundler.js:5085
patchElement @ runtime-core.esm-bundler.js:5003
processElement @ runtime-core.esm-bundler.js:4862
patch @ runtime-core.esm-bundler.js:4717
componentUpdateFn @ runtime-core.esm-bundler.js:5438
run @ reactivity.esm-bundler.js:237
runIfDirty @ reactivity.esm-bundler.js:275
callWithErrorHandling @ runtime-core.esm-bundler.js:199
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
loadUsers @ users.ts:140
await in loadUsers
wrappedAction @ pinia.mjs:1394
store.<computed> @ pinia.mjs:946
(匿名) @ UsersView.vue:201
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7379
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6255
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:12
runtime-core.esm-bundler.js:51  [Vue warn]: Unhandled error during execution of component update 
  at <UsersView onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <RouterView > 
  at <LayoutContent class="app-content" > 
  at <Layout class="right-layout" > 
  at <Layout class="app-layout" has-sider="" > 
  at <AppLayout onVnodeUnmounted=fn<onVnodeUnmounted> ref=Ref< Proxy(Object) {__v_skip: true} > > 
  at <KeepAlive > 
  at <RouterView > 
  at <ConfigProvider locale= {name: 'zh-CN', global: {…}, Popconfirm: {…}, Cascader: {…}, Time: {…}, …} date-locale= {name: 'zh-CN', locale: {…}} > 
  at <App>
warn$1 @ runtime-core.esm-bundler.js:51
logError @ runtime-core.esm-bundler.js:263
handleError @ runtime-core.esm-bundler.js:255
callWithErrorHandling @ runtime-core.esm-bundler.js:201
flushJobs @ runtime-core.esm-bundler.js:408
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queueJob @ runtime-core.esm-bundler.js:317
effect2.scheduler @ runtime-core.esm-bundler.js:5480
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
loadUsers @ users.ts:140
await in loadUsers
wrappedAction @ pinia.mjs:1394
store.<computed> @ pinia.mjs:946
(匿名) @ UsersView.vue:201
(匿名) @ runtime-core.esm-bundler.js:2836
callWithErrorHandling @ runtime-core.esm-bundler.js:199
callWithAsyncErrorHandling @ runtime-core.esm-bundler.js:206
hook.__weh.hook.__weh @ runtime-core.esm-bundler.js:2816
flushPostFlushCbs @ runtime-core.esm-bundler.js:385
flushJobs @ runtime-core.esm-bundler.js:427
Promise.then
queueFlush @ runtime-core.esm-bundler.js:322
queuePostFlushCb @ runtime-core.esm-bundler.js:336
queueEffectWithSuspense @ runtime-core.esm-bundler.js:7379
baseWatchOptions.scheduler @ runtime-core.esm-bundler.js:6255
effect2.scheduler @ reactivity.esm-bundler.js:1847
trigger @ reactivity.esm-bundler.js:265
endBatch @ reactivity.esm-bundler.js:323
notify @ reactivity.esm-bundler.js:614
trigger @ reactivity.esm-bundler.js:588
set value @ reactivity.esm-bundler.js:1465
finalizeNavigation @ vue-router.mjs:3503
(匿名) @ vue-router.mjs:3368
Promise.then
pushWithRedirect @ vue-router.mjs:3335
push @ vue-router.mjs:3260
install @ vue-router.mjs:3704
use @ runtime-core.esm-bundler.js:3886
(匿名) @ main.ts:12
create.js:58  Uncaught (in promise) TypeError: getKey is not a function
    at get key (create.js:58:20)
    at create.js:30:34
    at Array.forEach (<anonymous>)
    at createTreeNodes (create.js:9:14)
    at createTreeMate (create.js:79:23)
    at ComputedRefImpl.fn (use-table-data.mjs:30:12)
    at refreshComputed (reactivity.esm-bundler.js:391:28)
    at get value (reactivity.esm-bundler.js:1648:5)
    at ComputedRefImpl.fn (use-table-data.mjs:94:14)
    at refreshComputed (reactivity.esm-bundler.js:391:28)