import{ai as ne,ah as ie,aj as kr,r as A,j as I,ad as Pt,d as q,h as i,ak as Pr,v as Je,al as $r,am as Be,an as _r,Q as Rr,a as Ke,n as Ze,x as Ae,ao as Tr,W as Fe,u as xe,q as Pe,X as Ce,p as et,t as J,ap as Ue,y as Ne,aq as tt,m as rt,c as f,f as F,e as V,ar as $t,k as ve,as as _t,at as Rt,au as Tt,b as R,aa as ee,s as zr,T as Br,U as zt,av as Er,g as Ar,a6 as Ie,V as Nr,w as Lr,aw as Dr,O as We,z as oe,A as O,B as Q,ax as Ir,D as Bt,E as T,J as _e,H as ye,G as C,F as B,L as U,M as se,ag as Wr,ac as Et}from"./index-bBUuTVMS.js";import{e as jr,t as ge,V as Re,m as Mr,k as Or,j as ct,B as X,g as qe,c as be,f as pe,u as Gr,v as $e,o as Vr,N as K,_ as at}from"./_plugin-vue_export-helper-JcRYbv4V.js";import{l as Hr,m as ut,n as Fr,p as Ur,j as Te,o as qr,q as Xr,k as Yr,r as Qr,u as pt,s as Jr,a as Kr}from"./Dropdown-CPvaWprP.js";import{g as Zr,f as ea,S as ta,R as ra,D as aa,N as ft}from"./PageLoading.vue_vue_type_style_index_0_scoped_ea6456ad_lang-C8XH62qh.js";import{u as gt,N as je,a as At,b as Ee}from"./Card-DjpRSm_f.js";import{N as Nt,a as ze}from"./FormItem-yHcy6zAK.js";import{A as oa,a as na,N as Lt,u as ia,C as bt}from"./CreateOutline-DIurpOIq.js";import{C as sa}from"./CardOutline-B-jn5H-Z.js";class ae{static CARD_TYPES=[{value:"M",label:"标准月套餐",description:"30天 | ¥25 | 80,000字符"},{value:"Q",label:"标准季度套餐",description:"90天 | ¥55 | 250,000字符"},{value:"H",label:"标准半年套餐",description:"180天 | ¥99 | 550,000字符"},{value:"PM",label:"PRO月套餐",description:"30天 | ¥45 | 250,000字符"},{value:"PQ",label:"PRO季度套餐",description:"90天 | ¥120 | 800,000字符"},{value:"PH",label:"PRO半年套餐",description:"180天 | ¥220 | 2,000,000字符"},{value:"PT",label:"测试套餐",description:"30分钟 | ¥0 | 5,000字符"}];static getCardTypeName(t){const r=this.CARD_TYPES.find(a=>a.value===t);return r?r.label:t}static async getPackageTypes(){try{const t=await ne.get(ie.ADMIN_CARDS_PACKAGES);if(t.success||t.packages)return t.packages||t.data?.packages||[];throw new Error(t.message||t.error||"获取套餐类型失败")}catch(t){return console.warn("获取套餐类型API失败，使用静态配置:",t),this.CARD_TYPES.map(r=>({type:r.value,description:r.description||r.label,days:this.getDefaultDays(r.value),price:this.getDefaultPrice(r.value),chars:this.getDefaultChars(r.value)}))}}static getDefaultDays(t){return{M:30,Q:90,H:180,PM:30,PQ:90,PH:180,PT:.02}[t]||30}static getDefaultPrice(t){return{M:25,Q:55,H:99,PM:45,PQ:120,PH:220,PT:0}[t]||0}static getDefaultChars(t){return{M:8e4,Q:25e4,H:55e4,PM:25e4,PQ:8e5,PH:2e6,PT:5e3}[t]||8e4}static async getCards(t){try{const r=new URLSearchParams;t?.page&&r.append("page",t.page.toString()),t?.limit&&r.append("limit",t.limit.toString()),t?.status&&r.append("status",t.status),t?.packageType&&r.append("packageType",t.packageType);const a=`${ie.ADMIN_CARDS}${r.toString()?"?"+r.toString():""}`,o=await ne.get(a);if(o.success||o.cards)return(o.cards||o.data?.cards||[]).map(n=>{const l=d=>d?typeof d=="string"?new Date(d).getTime():d:0;return{id:n.id,code:n.code,package_type:n.package_type||n.packageType,package_info:n.package_info||n.packageInfo,created_at:n.created_at,used_at:n.used_at,used_by:n.used_by,userUsage:n.userUsage?{totalChars:n.userUsage.totalChars||0,monthlyChars:n.userUsage.monthlyChars||0,monthlyResetAt:n.userUsage.monthlyResetAt||0}:void 0,type:n.package_type||n.packageType||n.type,status:n.status,usedBy:n.used_by||n.user||n.usedBy,activatedAt:l(n.used_at||n.activatedAt),createdAt:l(n.created_at||n.createdAt)||Date.now(),totalChars:n.userUsage?.totalChars||n.totalChars||0}});throw new Error(o.message||o.error||"获取卡密列表失败")}catch(r){console.warn("新卡密API失败，尝试使用旧API:",r);try{const a=await ne.get(ie.CARDS);if(a.success)return(a.cards||a.data||[]).map(s=>({...s,usedBy:s.user||s.usedBy}))}catch(a){console.error("备用卡密API也失败:",a)}throw new Error(r instanceof Error?r.message:"获取卡密列表失败")}}static async getPackageTypes(){try{const t=await ne.get(ie.ADMIN_CARDS_PACKAGES);return t.success||t.packages?(t.packages||t.data?.packages||[]).map(a=>({value:a.type,label:a.description||`${a.type}套餐`,description:`${a.days}天 | ¥${a.price} | ${a.chars.toLocaleString()}字符`})):(console.warn("获取套餐类型API失败，使用默认配置"),this.CARD_TYPES)}catch(t){return console.warn("获取套餐类型失败，使用默认配置:",t),this.CARD_TYPES}}static async generatePreview(t){try{const r=await ne.post(ie.CARD_GENERATE,{type:t,preview:!0});if(r.success&&(r.data?.code||r.code))return r.data?.code||r.code||"";throw new Error(r.message||r.error||"生成预览失败")}catch(r){throw new Error(r instanceof Error?r.message:"生成预览失败")}}static async generateCard(t){try{const r={packageType:t.packageType||t.type||"PT",quantity:1,...t.customCode||t.code?{customCode:t.customCode||t.code}:{}},a=await ne.post(ie.ADMIN_CARDS_GENERATE,r);if(a.success&&a.cards&&a.cards.length>0)return a.cards[0].code;if(a.error||a.errors){const o=a.error||a.errors?.[0]||"生成卡密失败";throw new Error(o)}throw new Error(a.message||a.error||"生成卡密失败")}catch(r){console.warn("新生成API失败，尝试使用旧API:",r);try{const a=await ne.post(ie.CARD_GENERATE,{...t,preview:!1});if(a.success&&(a.data?.code||a.code))return a.data?.code||a.code||""}catch(a){console.error("备用生成API也失败:",a)}throw new Error(r instanceof Error?r.message:"生成卡密失败")}}static async generateCards(t,r){try{const a={packageType:t,quantity:r},o=await ne.post(ie.ADMIN_CARDS_GENERATE,a);if(o.success&&o.cards)return o.cards.map(s=>s.code);if(o.generated>0&&o.cards){const s=o.cards.map(l=>l.code),n=o.errors||[];return console.warn(`批量生成部分成功: ${o.generated}/${o.requested}`,n),s}throw new Error(o.error||o.message||o.error||"批量生成卡密失败")}catch(a){console.warn("新批量生成API失败，回退到逐个生成:",a);const o=[];for(let s=0;s<r;s++)try{const n=await this.generateCard({packageType:t,type:t});o.push(n)}catch(n){throw console.error(`生成第${s+1}个卡密失败:`,n),new Error(`生成第${s+1}个卡密失败: ${n instanceof Error?n.message:"未知错误"}`)}return o}}static async editCard(t){try{const r=await ne.post(ie.CARD_EDIT,t);if(!r.success)throw new Error(r.message||"编辑卡密失败")}catch(r){throw new Error(r instanceof Error?r.message:"编辑卡密失败")}}static async deleteCard(t){try{const r=await ne.post(ie.CARD_DELETE,{code:t});if(!r.success)throw new Error(r.message||"删除卡密失败")}catch(r){throw new Error(r instanceof Error?r.message:"删除卡密失败")}}}const Dt=kr("cards",()=>{const e=A([]),t=A(!1),r=A(null),a=A(!1),o=A([]),s=A(0),n=A(0),l=A(""),d=A([]),p=A({field:"createdAt",direction:"desc"}),x=I(()=>{let c=[...e.value];if(l.value){const P=l.value.toLowerCase();c=c.filter(u=>u.code.toLowerCase().includes(P)||ae.getCardTypeName(u.type).toLowerCase().includes(P)||u.status.toLowerCase().includes(P)||u.usedBy&&u.usedBy.toLowerCase().includes(P))}return p.value.field&&c.sort((P,u)=>{const v=p.value.field;let G=P[v],we=u[v];if(G===we)return 0;const Se=p.value.direction==="asc"?1:-1;return G>we?Se:-Se}),c}),w=A(ae.CARD_TYPES),m=A(!1),k=async()=>{if(!m.value){m.value=!0;try{const c=await ae.getPackageTypes();w.value=c}catch(c){console.warn("加载套餐类型失败，使用默认配置:",c),w.value=ae.CARD_TYPES}finally{m.value=!1}}},h=I(()=>x.value.length>0&&d.value.length===x.value.length),y=async()=>{t.value=!0,r.value=null;try{const c=await ae.getCards();e.value=c}catch(c){r.value=c instanceof Error?c.message:"加载卡密失败"}finally{t.value=!1}},N=async c=>{try{return await ae.generatePreview(c)}catch(P){throw r.value=P instanceof Error?P.message:"生成预览失败",P}},E=async(c,P,u)=>{a.value=!0,s.value=0,n.value=P,o.value=[],r.value=null;try{if(P===1&&u){const v=await ae.generateCard({packageType:c,type:c,customCode:u});o.value=[v]}else{const v=await ae.generateCards(c,P);o.value=v}s.value=o.value.length,await y()}catch(v){throw r.value=v instanceof Error?v.message:"生成卡密失败",v}finally{a.value=!1}},S=async(c,P,u)=>{try{await ae.editCard({oldCode:c,newCode:P,type:u});const v=e.value.findIndex(G=>G.code===c);v!==-1&&(e.value[v].code=P)}catch(v){throw r.value=v instanceof Error?v.message:"编辑卡密失败",v}},j=async c=>{try{await ae.deleteCard(c);const P=e.value.findIndex(v=>v.code===c);P!==-1&&e.value.splice(P,1);const u=d.value.indexOf(c);u!==-1&&d.value.splice(u,1)}catch(P){throw r.value=P instanceof Error?P.message:"删除卡密失败",P}};return{cards:e,isLoading:t,error:r,isGenerating:a,generatedCards:o,generateProgress:s,generateTotal:n,searchTerm:l,selectedCards:d,sortState:p,filteredCards:x,isAllSelected:h,cardTypes:w,isLoadingTypes:m,loadCards:y,loadCardTypes:k,generatePreview:N,generateCards:E,editCard:S,deleteCard:j,deleteSelectedCards:async()=>{const c=[...d.value];for(const P of c)try{await j(P)}catch(u){console.error(`删除卡密 ${P} 失败:`,u)}d.value=[]},setSort:c=>{p.value.field===c?p.value.direction=p.value.direction==="asc"?"desc":"asc":(p.value.field=c,p.value.direction="asc")},setSearch:c=>{l.value=c},toggleCardSelection:c=>{const P=d.value.indexOf(c);P===-1?d.value.push(c):d.value.splice(P,1)},toggleSelectAll:()=>{h.value?d.value=[]:d.value=x.value.map(c=>c.code)},clearGeneratedCards:()=>{o.value=[],s.value=0,n.value=0},clearError:()=>{r.value=null}}});function la(e){if(typeof e=="number")return{"":e.toString()};const t={};return e.split(/ +/).forEach(r=>{if(r==="")return;const[a,o]=r.split(":");o===void 0?t[""]=a:t[a]=o}),t}function fe(e,t){var r;if(e==null)return;const a=la(e);if(t===void 0)return a[""];if(typeof t=="string")return(r=a[t])!==null&&r!==void 0?r:a[""];if(Array.isArray(t)){for(let o=t.length-1;o>=0;--o){const s=t[o];if(s in a)return a[s]}return a[""]}else{let o,s=-1;return Object.keys(a).forEach(n=>{const l=Number(n);!Number.isNaN(l)&&t>=l&&l>=s&&(s=l,o=a[n])}),o}}const da={xs:0,s:640,m:1024,l:1280,xl:1536,"2xl":1920};function ca(e){return`(min-width: ${e}px)`}const me={};function ua(e=da){if(!Hr)return I(()=>[]);if(typeof window.matchMedia!="function")return I(()=>[]);const t=A({}),r=Object.keys(e),a=(o,s)=>{o.matches?t.value[s]=!0:t.value[s]=!1};return r.forEach(o=>{const s=e[o];let n,l;me[s]===void 0?(n=window.matchMedia(ca(s)),n.addEventListener?n.addEventListener("change",d=>{l.forEach(p=>{p(d,o)})}):n.addListener&&n.addListener(d=>{l.forEach(p=>{p(d,o)})}),l=new Set,me[s]={mql:n,cbs:l}):(n=me[s].mql,l=me[s].cbs),l.add(a),n.matches&&l.forEach(d=>{d(n,o)})}),Pt(()=>{r.forEach(o=>{const{cbs:s}=me[e[o]];s.has(a)&&s.delete(a)})}),I(()=>{const{value:o}=t;return r.filter(s=>o[s])})}const pa=ut(".v-x-scroll",{overflow:"auto",scrollbarWidth:"none"},[ut("&::-webkit-scrollbar",{width:0,height:0})]),fa=q({name:"XScroll",props:{disabled:Boolean,onScroll:Function},setup(){const e=A(null);function t(o){!(o.currentTarget.offsetWidth<o.currentTarget.scrollWidth)||o.deltaY===0||(o.currentTarget.scrollLeft+=o.deltaY+o.deltaX,o.preventDefault())}const r=Pr();return pa.mount({id:"vueuc/x-scroll",head:!0,anchorMetaName:Fr,ssr:r}),Object.assign({selfRef:e,handleWheel:t},{scrollTo(...o){var s;(s=e.value)===null||s===void 0||s.scrollTo(...o)}})},render(){return i("div",{ref:"selfRef",onScroll:this.onScroll,onWheel:this.disabled?void 0:this.handleWheel,class:"v-x-scroll"},this.$slots)}});function ga(e){var t;const r=(t=e.dirs)===null||t===void 0?void 0:t.find(({dir:a})=>a===Je);return!!(r&&r.value===!1)}var ba=/\s/;function va(e){for(var t=e.length;t--&&ba.test(e.charAt(t)););return t}var ha=/^\s+/;function ma(e){return e&&e.slice(0,va(e)+1).replace(ha,"")}var vt=NaN,ya=/^[-+]0x[0-9a-f]+$/i,xa=/^0b[01]+$/i,Ca=/^0o[0-7]+$/i,wa=parseInt;function ht(e){if(typeof e=="number")return e;if($r(e))return vt;if(Be(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Be(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=ma(e);var r=xa.test(e);return r||Ca.test(e)?wa(e.slice(2),r?2:8):ya.test(e)?vt:+e}var Me=function(){return _r.Date.now()},Sa="Expected a function",ka=Math.max,Pa=Math.min;function $a(e,t,r){var a,o,s,n,l,d,p=0,x=!1,w=!1,m=!0;if(typeof e!="function")throw new TypeError(Sa);t=ht(t)||0,Be(r)&&(x=!!r.leading,w="maxWait"in r,s=w?ka(ht(r.maxWait)||0,t):s,m="trailing"in r?!!r.trailing:m);function k($){var M=a,Y=o;return a=o=void 0,p=$,n=e.apply(Y,M),n}function h($){return p=$,l=setTimeout(E,t),x?k($):n}function y($){var M=$-d,Y=$-p,Z=t-M;return w?Pa(Z,s-Y):Z}function N($){var M=$-d,Y=$-p;return d===void 0||M>=t||M<0||w&&Y>=s}function E(){var $=Me();if(N($))return S($);l=setTimeout(E,y($))}function S($){return l=void 0,m&&a?k($):(a=o=void 0,n)}function j(){l!==void 0&&clearTimeout(l),p=0,a=d=o=l=void 0}function W(){return l===void 0?n:S(Me())}function z(){var $=Me(),M=N($);if(a=arguments,o=this,d=$,M){if(l===void 0)return h(d);if(w)return clearTimeout(l),l=setTimeout(E,t),k(d)}return l===void 0&&(l=setTimeout(E,t)),n}return z.cancel=j,z.flush=W,z}var _a="Expected a function";function Oe(e,t,r){var a=!0,o=!0;if(typeof e!="function")throw new TypeError(_a);return Be(r)&&(a="leading"in r?!!r.leading:a,o="trailing"in r?!!r.trailing:o),$a(e,t,{leading:a,maxWait:t,trailing:o})}const Ra={iconSize:"22px"};function Ta(e){const{fontSize:t,warningColor:r}=e;return Object.assign(Object.assign({},Ra),{fontSize:t,iconColor:r})}const za=Rr({name:"Popconfirm",common:Ke,peers:{Button:jr,Popover:Ur},self:Ta});function Ba(e){const{infoColor:t,successColor:r,warningColor:a,errorColor:o,textColor2:s,progressRailColor:n,fontSize:l,fontWeight:d}=e;return{fontSize:l,fontSizeCircle:"28px",fontWeightCircle:d,railColor:n,railHeight:"8px",iconSizeCircle:"36px",iconSizeLine:"18px",iconColor:t,iconColorInfo:t,iconColorSuccess:r,iconColorWarning:a,iconColorError:o,textColorCircle:s,textColorLineInner:"rgb(255, 255, 255)",textColorLineOuter:s,fillColor:t,fillColorInfo:t,fillColorSuccess:r,fillColorWarning:a,fillColorError:o,lineBgProcessing:"linear-gradient(90deg, rgba(255, 255, 255, .3) 0%, rgba(255, 255, 255, .5) 100%)"}}const Ea={common:Ke,self:Ba},Aa={tabFontSizeSmall:"14px",tabFontSizeMedium:"14px",tabFontSizeLarge:"16px",tabGapSmallLine:"36px",tabGapMediumLine:"36px",tabGapLargeLine:"36px",tabGapSmallLineVertical:"8px",tabGapMediumLineVertical:"8px",tabGapLargeLineVertical:"8px",tabPaddingSmallLine:"6px 0",tabPaddingMediumLine:"10px 0",tabPaddingLargeLine:"14px 0",tabPaddingVerticalSmallLine:"6px 12px",tabPaddingVerticalMediumLine:"8px 16px",tabPaddingVerticalLargeLine:"10px 20px",tabGapSmallBar:"36px",tabGapMediumBar:"36px",tabGapLargeBar:"36px",tabGapSmallBarVertical:"8px",tabGapMediumBarVertical:"8px",tabGapLargeBarVertical:"8px",tabPaddingSmallBar:"4px 0",tabPaddingMediumBar:"6px 0",tabPaddingLargeBar:"10px 0",tabPaddingVerticalSmallBar:"6px 12px",tabPaddingVerticalMediumBar:"8px 16px",tabPaddingVerticalLargeBar:"10px 20px",tabGapSmallCard:"4px",tabGapMediumCard:"4px",tabGapLargeCard:"4px",tabGapSmallCardVertical:"4px",tabGapMediumCardVertical:"4px",tabGapLargeCardVertical:"4px",tabPaddingSmallCard:"8px 16px",tabPaddingMediumCard:"10px 20px",tabPaddingLargeCard:"12px 24px",tabPaddingSmallSegment:"4px 0",tabPaddingMediumSegment:"6px 0",tabPaddingLargeSegment:"8px 0",tabPaddingVerticalLargeSegment:"0 8px",tabPaddingVerticalSmallCard:"8px 12px",tabPaddingVerticalMediumCard:"10px 16px",tabPaddingVerticalLargeCard:"12px 20px",tabPaddingVerticalSmallSegment:"0 4px",tabPaddingVerticalMediumSegment:"0 6px",tabGapSmallSegment:"0",tabGapMediumSegment:"0",tabGapLargeSegment:"0",tabGapSmallSegmentVertical:"0",tabGapMediumSegmentVertical:"0",tabGapLargeSegmentVertical:"0",panePaddingSmall:"8px 0 0 0",panePaddingMedium:"12px 0 0 0",panePaddingLarge:"16px 0 0 0",closeSize:"18px",closeIconSize:"14px"};function Na(e){const{textColor2:t,primaryColor:r,textColorDisabled:a,closeIconColor:o,closeIconColorHover:s,closeIconColorPressed:n,closeColorHover:l,closeColorPressed:d,tabColor:p,baseColor:x,dividerColor:w,fontWeight:m,textColor1:k,borderRadius:h,fontSize:y,fontWeightStrong:N}=e;return Object.assign(Object.assign({},Aa),{colorSegment:p,tabFontSizeCard:y,tabTextColorLine:k,tabTextColorActiveLine:r,tabTextColorHoverLine:r,tabTextColorDisabledLine:a,tabTextColorSegment:k,tabTextColorActiveSegment:t,tabTextColorHoverSegment:t,tabTextColorDisabledSegment:a,tabTextColorBar:k,tabTextColorActiveBar:r,tabTextColorHoverBar:r,tabTextColorDisabledBar:a,tabTextColorCard:k,tabTextColorHoverCard:k,tabTextColorActiveCard:r,tabTextColorDisabledCard:a,barColor:r,closeIconColor:o,closeIconColorHover:s,closeIconColorPressed:n,closeColorHover:l,closeColorPressed:d,closeBorderRadius:h,tabColor:p,tabColorSegment:x,tabBorderColor:w,tabFontWeightActive:m,tabFontWeight:m,tabBorderRadius:h,paneTextColor:t,fontWeightStrong:N})}const La={common:Ke,self:Na},mt=1,It=Ze("n-grid"),Wt=1,Da={span:{type:[Number,String],default:Wt},offset:{type:[Number,String],default:0},suffix:Boolean,privateOffset:Number,privateSpan:Number,privateColStart:Number,privateShow:{type:Boolean,default:!0}},Ge=q({__GRID_ITEM__:!0,name:"GridItem",alias:["Gi"],props:Da,setup(){const{isSsrRef:e,xGapRef:t,itemStyleRef:r,overflowRef:a,layoutShiftDisabledRef:o}=Ae(It),s=Tr();return{overflow:a,itemStyle:r,layoutShiftDisabled:o,mergedXGap:I(()=>ge(t.value||0)),deriveStyle:()=>{e.value;const{privateSpan:n=Wt,privateShow:l=!0,privateColStart:d=void 0,privateOffset:p=0}=s.vnode.props,{value:x}=t,w=ge(x||0);return{display:l?"":"none",gridColumn:`${d??`span ${n}`} / span ${n}`,marginLeft:p?`calc((100% - (${n} - 1) * ${w}) / ${n} * ${p} + ${w} * ${p})`:""}}}},render(){var e,t;if(this.layoutShiftDisabled){const{span:r,offset:a,mergedXGap:o}=this;return i("div",{style:{gridColumn:`span ${r} / span ${r}`,marginLeft:a?`calc((100% - (${r} - 1) * ${o}) / ${r} * ${a} + ${o} * ${a})`:""}},this.$slots)}return i("div",{style:[this.itemStyle,this.deriveStyle()]},(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e,{overflow:this.overflow}))}}),Ia={xs:0,s:640,m:1024,l:1280,xl:1536,xxl:1920},jt=24,Ve="__ssr__",Wa={layoutShiftDisabled:Boolean,responsive:{type:[String,Boolean],default:"self"},cols:{type:[Number,String],default:jt},itemResponsive:Boolean,collapsed:Boolean,collapsedRows:{type:Number,default:1},itemStyle:[Object,String],xGap:{type:[Number,String],default:0},yGap:{type:[Number,String],default:0}},ja=q({name:"Grid",inheritAttrs:!1,props:Wa,setup(e){const{mergedClsPrefixRef:t,mergedBreakpointsRef:r}=xe(e),a=/^\d+$/,o=A(void 0),s=ua(r?.value||Ia),n=Pe(()=>!!(e.itemResponsive||!a.test(e.cols.toString())||!a.test(e.xGap.toString())||!a.test(e.yGap.toString()))),l=I(()=>{if(n.value)return e.responsive==="self"?o.value:s.value}),d=Pe(()=>{var E;return(E=Number(fe(e.cols.toString(),l.value)))!==null&&E!==void 0?E:jt}),p=Pe(()=>fe(e.xGap.toString(),l.value)),x=Pe(()=>fe(e.yGap.toString(),l.value)),w=E=>{o.value=E.contentRect.width},m=E=>{qr(w,E)},k=A(!1),h=I(()=>{if(e.responsive==="self")return m}),y=A(!1),N=A();return Ce(()=>{const{value:E}=N;E&&E.hasAttribute(Ve)&&(E.removeAttribute(Ve),y.value=!0)}),et(It,{layoutShiftDisabledRef:J(e,"layoutShiftDisabled"),isSsrRef:y,itemStyleRef:J(e,"itemStyle"),xGapRef:p,overflowRef:k}),{isSsr:!Mr,contentEl:N,mergedClsPrefix:t,style:I(()=>e.layoutShiftDisabled?{width:"100%",display:"grid",gridTemplateColumns:`repeat(${e.cols}, minmax(0, 1fr))`,columnGap:ge(e.xGap),rowGap:ge(e.yGap)}:{width:"100%",display:"grid",gridTemplateColumns:`repeat(${d.value}, minmax(0, 1fr))`,columnGap:ge(p.value),rowGap:ge(x.value)}),isResponsive:n,responsiveQuery:l,responsiveCols:d,handleResize:h,overflow:k}},render(){if(this.layoutShiftDisabled)return i("div",Fe({ref:"contentEl",class:`${this.mergedClsPrefix}-grid`,style:this.style},this.$attrs),this.$slots);const e=()=>{var t,r,a,o,s,n,l;this.overflow=!1;const d=Te(Zr(this)),p=[],{collapsed:x,collapsedRows:w,responsiveCols:m,responsiveQuery:k}=this;d.forEach(S=>{var j,W,z,$,M;if(((j=S?.type)===null||j===void 0?void 0:j.__GRID_ITEM__)!==!0)return;if(ga(S)){const L=Ue(S);L.props?L.props.privateShow=!1:L.props={privateShow:!1},p.push({child:L,rawChildSpan:0});return}S.dirs=((W=S.dirs)===null||W===void 0?void 0:W.filter(({dir:L})=>L!==Je))||null,((z=S.dirs)===null||z===void 0?void 0:z.length)===0&&(S.dirs=null);const Y=Ue(S),Z=Number((M=fe(($=Y.props)===null||$===void 0?void 0:$.span,k))!==null&&M!==void 0?M:mt);Z!==0&&p.push({child:Y,rawChildSpan:Z})});let h=0;const y=(t=p[p.length-1])===null||t===void 0?void 0:t.child;if(y?.props){const S=(r=y.props)===null||r===void 0?void 0:r.suffix;S!==void 0&&S!==!1&&(h=Number((o=fe((a=y.props)===null||a===void 0?void 0:a.span,k))!==null&&o!==void 0?o:mt),y.props.privateSpan=h,y.props.privateColStart=m+1-h,y.props.privateShow=(s=y.props.privateShow)!==null&&s!==void 0?s:!0)}let N=0,E=!1;for(const{child:S,rawChildSpan:j}of p){if(E&&(this.overflow=!0),!E){const W=Number((l=fe((n=S.props)===null||n===void 0?void 0:n.offset,k))!==null&&l!==void 0?l:0),z=Math.min(j+W,m);if(S.props?(S.props.privateSpan=z,S.props.privateOffset=W):S.props={privateSpan:z,privateOffset:W},x){const $=N%m;z+$>m&&(N+=m-$),z+N+h>w*m?E=!0:N+=z}}E&&(S.props?S.props.privateShow!==!0&&(S.props.privateShow=!1):S.props={privateShow:!1})}return i("div",Fe({ref:"contentEl",class:`${this.mergedClsPrefix}-grid`,style:this.style,[Ve]:this.isSsr||void 0},this.$attrs),p.map(({child:S})=>S))};return this.isResponsive&&this.responsive==="self"?i(Re,{onResize:this.handleResize},{default:e}):e()}}),Mt=Ze("n-popconfirm"),Ot={positiveText:String,negativeText:String,showIcon:{type:Boolean,default:!0},onPositiveClick:{type:Function,required:!0},onNegativeClick:{type:Function,required:!0}},yt=Or(Ot),Ma=q({name:"NPopconfirmPanel",props:Ot,setup(e){const{localeRef:t}=gt("Popconfirm"),{inlineThemeDisabled:r}=xe(),{mergedClsPrefixRef:a,mergedThemeRef:o,props:s}=Ae(Mt),n=I(()=>{const{common:{cubicBezierEaseInOut:d},self:{fontSize:p,iconSize:x,iconColor:w}}=o.value;return{"--n-bezier":d,"--n-font-size":p,"--n-icon-size":x,"--n-icon-color":w}}),l=r?rt("popconfirm-panel",void 0,n,s):void 0;return Object.assign(Object.assign({},gt("Popconfirm")),{mergedClsPrefix:a,cssVars:r?void 0:n,localizedPositiveText:I(()=>e.positiveText||t.value.positiveText),localizedNegativeText:I(()=>e.negativeText||t.value.negativeText),positiveButtonProps:J(s,"positiveButtonProps"),negativeButtonProps:J(s,"negativeButtonProps"),handlePositiveClick(d){e.onPositiveClick(d)},handleNegativeClick(d){e.onNegativeClick(d)},themeClass:l?.themeClass,onRender:l?.onRender})},render(){var e;const{mergedClsPrefix:t,showIcon:r,$slots:a}=this,o=ct(a.action,()=>this.negativeText===null&&this.positiveText===null?[]:[this.negativeText!==null&&i(X,Object.assign({size:"small",onClick:this.handleNegativeClick},this.negativeButtonProps),{default:()=>this.localizedNegativeText}),this.positiveText!==null&&i(X,Object.assign({size:"small",type:"primary",onClick:this.handlePositiveClick},this.positiveButtonProps),{default:()=>this.localizedPositiveText})]);return(e=this.onRender)===null||e===void 0||e.call(this),i("div",{class:[`${t}-popconfirm__panel`,this.themeClass],style:this.cssVars},qe(a.default,s=>r||s?i("div",{class:`${t}-popconfirm__body`},r?i("div",{class:`${t}-popconfirm__icon`},ct(a.icon,()=>[i(Ne,{clsPrefix:t},{default:()=>i(tt,null)})])):null,s):null),o?i("div",{class:[`${t}-popconfirm__action`]},o):null)}}),Oa=f("popconfirm",[F("body",`
 font-size: var(--n-font-size);
 display: flex;
 align-items: center;
 flex-wrap: nowrap;
 position: relative;
 `,[F("icon",`
 display: flex;
 font-size: var(--n-icon-size);
 color: var(--n-icon-color);
 transition: color .3s var(--n-bezier);
 margin: 0 8px 0 0;
 `)]),F("action",`
 display: flex;
 justify-content: flex-end;
 `,[V("&:not(:first-child)","margin-top: 8px"),f("button",[V("&:not(:last-child)","margin-right: 8px;")])])]),Ga=Object.assign(Object.assign(Object.assign({},ve.props),Qr),{positiveText:String,negativeText:String,showIcon:{type:Boolean,default:!0},trigger:{type:String,default:"click"},positiveButtonProps:Object,negativeButtonProps:Object,onPositiveClick:Function,onNegativeClick:Function}),Va=q({name:"Popconfirm",props:Ga,slots:Object,__popover__:!0,setup(e){const{mergedClsPrefixRef:t}=xe(),r=ve("Popconfirm","-popconfirm",Oa,za,e,t),a=A(null);function o(l){var d;if(!(!((d=a.value)===null||d===void 0)&&d.getMergedShow()))return;const{onPositiveClick:p,"onUpdate:show":x}=e;Promise.resolve(p?p(l):!0).then(w=>{var m;w!==!1&&((m=a.value)===null||m===void 0||m.setShow(!1),x&&be(x,!1))})}function s(l){var d;if(!(!((d=a.value)===null||d===void 0)&&d.getMergedShow()))return;const{onNegativeClick:p,"onUpdate:show":x}=e;Promise.resolve(p?p(l):!0).then(w=>{var m;w!==!1&&((m=a.value)===null||m===void 0||m.setShow(!1),x&&be(x,!1))})}return et(Mt,{mergedThemeRef:r,mergedClsPrefixRef:t,props:e}),{setShow(l){var d;(d=a.value)===null||d===void 0||d.setShow(l)},syncPosition(){var l;(l=a.value)===null||l===void 0||l.syncPosition()},mergedTheme:r,popoverInstRef:a,handlePositiveClick:o,handleNegativeClick:s}},render(){const{$slots:e,$props:t,mergedTheme:r}=this;return i(Xr,$t(t,yt,{theme:r.peers.Popover,themeOverrides:r.peerOverrides.Popover,internalExtraClass:["popconfirm"],ref:"popoverInstRef"}),{trigger:e.trigger,default:()=>{const a=Yr(t,yt);return i(Ma,Object.assign(Object.assign({},a),{onPositiveClick:this.handlePositiveClick,onNegativeClick:this.handleNegativeClick}),e)}})}}),Ha={success:i(Tt,null),error:i(Rt,null),warning:i(tt,null),info:i(_t,null)},Fa=q({name:"ProgressCircle",props:{clsPrefix:{type:String,required:!0},status:{type:String,required:!0},strokeWidth:{type:Number,required:!0},fillColor:[String,Object],railColor:String,railStyle:[String,Object],percentage:{type:Number,default:0},offsetDegree:{type:Number,default:0},showIndicator:{type:Boolean,required:!0},indicatorTextColor:String,unit:String,viewBoxWidth:{type:Number,required:!0},gapDegree:{type:Number,required:!0},gapOffsetDegree:{type:Number,default:0}},setup(e,{slots:t}){function r(o,s,n,l){const{gapDegree:d,viewBoxWidth:p,strokeWidth:x}=e,w=50,m=0,k=w,h=0,y=2*w,N=50+x/2,E=`M ${N},${N} m ${m},${k}
      a ${w},${w} 0 1 1 ${h},${-y}
      a ${w},${w} 0 1 1 ${-h},${y}`,S=Math.PI*2*w,j={stroke:l==="rail"?n:typeof e.fillColor=="object"?"url(#gradient)":n,strokeDasharray:`${o/100*(S-d)}px ${p*8}px`,strokeDashoffset:`-${d/2}px`,transformOrigin:s?"center":void 0,transform:s?`rotate(${s}deg)`:void 0};return{pathString:E,pathStyle:j}}const a=()=>{const o=typeof e.fillColor=="object",s=o?e.fillColor.stops[0]:"",n=o?e.fillColor.stops[1]:"";return o&&i("defs",null,i("linearGradient",{id:"gradient",x1:"0%",y1:"100%",x2:"100%",y2:"0%"},i("stop",{offset:"0%","stop-color":s}),i("stop",{offset:"100%","stop-color":n})))};return()=>{const{fillColor:o,railColor:s,strokeWidth:n,offsetDegree:l,status:d,percentage:p,showIndicator:x,indicatorTextColor:w,unit:m,gapOffsetDegree:k,clsPrefix:h}=e,{pathString:y,pathStyle:N}=r(100,0,s,"rail"),{pathString:E,pathStyle:S}=r(p,l,o,"fill"),j=100+n;return i("div",{class:`${h}-progress-content`,role:"none"},i("div",{class:`${h}-progress-graph`,"aria-hidden":!0},i("div",{class:`${h}-progress-graph-circle`,style:{transform:k?`rotate(${k}deg)`:void 0}},i("svg",{viewBox:`0 0 ${j} ${j}`},a(),i("g",null,i("path",{class:`${h}-progress-graph-circle-rail`,d:y,"stroke-width":n,"stroke-linecap":"round",fill:"none",style:N})),i("g",null,i("path",{class:[`${h}-progress-graph-circle-fill`,p===0&&`${h}-progress-graph-circle-fill--empty`],d:E,"stroke-width":n,"stroke-linecap":"round",fill:"none",style:S}))))),x?i("div",null,t.default?i("div",{class:`${h}-progress-custom-content`,role:"none"},t.default()):d!=="default"?i("div",{class:`${h}-progress-icon`,"aria-hidden":!0},i(Ne,{clsPrefix:h},{default:()=>Ha[d]})):i("div",{class:`${h}-progress-text`,style:{color:w},role:"none"},i("span",{class:`${h}-progress-text__percentage`},p),i("span",{class:`${h}-progress-text__unit`},m))):null)}}}),Ua={success:i(Tt,null),error:i(Rt,null),warning:i(tt,null),info:i(_t,null)},qa=q({name:"ProgressLine",props:{clsPrefix:{type:String,required:!0},percentage:{type:Number,default:0},railColor:String,railStyle:[String,Object],fillColor:[String,Object],status:{type:String,required:!0},indicatorPlacement:{type:String,required:!0},indicatorTextColor:String,unit:{type:String,default:"%"},processing:{type:Boolean,required:!0},showIndicator:{type:Boolean,required:!0},height:[String,Number],railBorderRadius:[String,Number],fillBorderRadius:[String,Number]},setup(e,{slots:t}){const r=I(()=>pe(e.height)),a=I(()=>{var n,l;return typeof e.fillColor=="object"?`linear-gradient(to right, ${(n=e.fillColor)===null||n===void 0?void 0:n.stops[0]} , ${(l=e.fillColor)===null||l===void 0?void 0:l.stops[1]})`:e.fillColor}),o=I(()=>e.railBorderRadius!==void 0?pe(e.railBorderRadius):e.height!==void 0?pe(e.height,{c:.5}):""),s=I(()=>e.fillBorderRadius!==void 0?pe(e.fillBorderRadius):e.railBorderRadius!==void 0?pe(e.railBorderRadius):e.height!==void 0?pe(e.height,{c:.5}):"");return()=>{const{indicatorPlacement:n,railColor:l,railStyle:d,percentage:p,unit:x,indicatorTextColor:w,status:m,showIndicator:k,processing:h,clsPrefix:y}=e;return i("div",{class:`${y}-progress-content`,role:"none"},i("div",{class:`${y}-progress-graph`,"aria-hidden":!0},i("div",{class:[`${y}-progress-graph-line`,{[`${y}-progress-graph-line--indicator-${n}`]:!0}]},i("div",{class:`${y}-progress-graph-line-rail`,style:[{backgroundColor:l,height:r.value,borderRadius:o.value},d]},i("div",{class:[`${y}-progress-graph-line-fill`,h&&`${y}-progress-graph-line-fill--processing`],style:{maxWidth:`${e.percentage}%`,background:a.value,height:r.value,lineHeight:r.value,borderRadius:s.value}},n==="inside"?i("div",{class:`${y}-progress-graph-line-indicator`,style:{color:w}},t.default?t.default():`${p}${x}`):null)))),k&&n==="outside"?i("div",null,t.default?i("div",{class:`${y}-progress-custom-content`,style:{color:w},role:"none"},t.default()):m==="default"?i("div",{role:"none",class:`${y}-progress-icon ${y}-progress-icon--as-text`,style:{color:w}},p,x):i("div",{class:`${y}-progress-icon`,"aria-hidden":!0},i(Ne,{clsPrefix:y},{default:()=>Ua[m]}))):null)}}});function xt(e,t,r=100){return`m ${r/2} ${r/2-e} a ${e} ${e} 0 1 1 0 ${2*e} a ${e} ${e} 0 1 1 0 -${2*e}`}const Xa=q({name:"ProgressMultipleCircle",props:{clsPrefix:{type:String,required:!0},viewBoxWidth:{type:Number,required:!0},percentage:{type:Array,default:[0]},strokeWidth:{type:Number,required:!0},circleGap:{type:Number,required:!0},showIndicator:{type:Boolean,required:!0},fillColor:{type:Array,default:()=>[]},railColor:{type:Array,default:()=>[]},railStyle:{type:Array,default:()=>[]}},setup(e,{slots:t}){const r=I(()=>e.percentage.map((s,n)=>`${Math.PI*s/100*(e.viewBoxWidth/2-e.strokeWidth/2*(1+2*n)-e.circleGap*n)*2}, ${e.viewBoxWidth*8}`)),a=(o,s)=>{const n=e.fillColor[s],l=typeof n=="object"?n.stops[0]:"",d=typeof n=="object"?n.stops[1]:"";return typeof e.fillColor[s]=="object"&&i("linearGradient",{id:`gradient-${s}`,x1:"100%",y1:"0%",x2:"0%",y2:"100%"},i("stop",{offset:"0%","stop-color":l}),i("stop",{offset:"100%","stop-color":d}))};return()=>{const{viewBoxWidth:o,strokeWidth:s,circleGap:n,showIndicator:l,fillColor:d,railColor:p,railStyle:x,percentage:w,clsPrefix:m}=e;return i("div",{class:`${m}-progress-content`,role:"none"},i("div",{class:`${m}-progress-graph`,"aria-hidden":!0},i("div",{class:`${m}-progress-graph-circle`},i("svg",{viewBox:`0 0 ${o} ${o}`},i("defs",null,w.map((k,h)=>a(k,h))),w.map((k,h)=>i("g",{key:h},i("path",{class:`${m}-progress-graph-circle-rail`,d:xt(o/2-s/2*(1+2*h)-n*h,s,o),"stroke-width":s,"stroke-linecap":"round",fill:"none",style:[{strokeDashoffset:0,stroke:p[h]},x[h]]}),i("path",{class:[`${m}-progress-graph-circle-fill`,k===0&&`${m}-progress-graph-circle-fill--empty`],d:xt(o/2-s/2*(1+2*h)-n*h,s,o),"stroke-width":s,"stroke-linecap":"round",fill:"none",style:{strokeDasharray:r.value[h],strokeDashoffset:0,stroke:typeof d[h]=="object"?`url(#gradient-${h})`:d[h]}})))))),l&&t.default?i("div",null,i("div",{class:`${m}-progress-text`},t.default())):null)}}}),Ya=V([f("progress",{display:"inline-block"},[f("progress-icon",`
 color: var(--n-icon-color);
 transition: color .3s var(--n-bezier);
 `),R("line",`
 width: 100%;
 display: block;
 `,[f("progress-content",`
 display: flex;
 align-items: center;
 `,[f("progress-graph",{flex:1})]),f("progress-custom-content",{marginLeft:"14px"}),f("progress-icon",`
 width: 30px;
 padding-left: 14px;
 height: var(--n-icon-size-line);
 line-height: var(--n-icon-size-line);
 font-size: var(--n-icon-size-line);
 `,[R("as-text",`
 color: var(--n-text-color-line-outer);
 text-align: center;
 width: 40px;
 font-size: var(--n-font-size);
 padding-left: 4px;
 transition: color .3s var(--n-bezier);
 `)])]),R("circle, dashboard",{width:"120px"},[f("progress-custom-content",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 justify-content: center;
 `),f("progress-text",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 color: inherit;
 font-size: var(--n-font-size-circle);
 color: var(--n-text-color-circle);
 font-weight: var(--n-font-weight-circle);
 transition: color .3s var(--n-bezier);
 white-space: nowrap;
 `),f("progress-icon",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 color: var(--n-icon-color);
 font-size: var(--n-icon-size-circle);
 `)]),R("multiple-circle",`
 width: 200px;
 color: inherit;
 `,[f("progress-text",`
 font-weight: var(--n-font-weight-circle);
 color: var(--n-text-color-circle);
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 justify-content: center;
 transition: color .3s var(--n-bezier);
 `)]),f("progress-content",{position:"relative"}),f("progress-graph",{position:"relative"},[f("progress-graph-circle",[V("svg",{verticalAlign:"bottom"}),f("progress-graph-circle-fill",`
 stroke: var(--n-fill-color);
 transition:
 opacity .3s var(--n-bezier),
 stroke .3s var(--n-bezier),
 stroke-dasharray .3s var(--n-bezier);
 `,[R("empty",{opacity:0})]),f("progress-graph-circle-rail",`
 transition: stroke .3s var(--n-bezier);
 overflow: hidden;
 stroke: var(--n-rail-color);
 `)]),f("progress-graph-line",[R("indicator-inside",[f("progress-graph-line-rail",`
 height: 16px;
 line-height: 16px;
 border-radius: 10px;
 `,[f("progress-graph-line-fill",`
 height: inherit;
 border-radius: 10px;
 `),f("progress-graph-line-indicator",`
 background: #0000;
 white-space: nowrap;
 text-align: right;
 margin-left: 14px;
 margin-right: 14px;
 height: inherit;
 font-size: 12px;
 color: var(--n-text-color-line-inner);
 transition: color .3s var(--n-bezier);
 `)])]),R("indicator-inside-label",`
 height: 16px;
 display: flex;
 align-items: center;
 `,[f("progress-graph-line-rail",`
 flex: 1;
 transition: background-color .3s var(--n-bezier);
 `),f("progress-graph-line-indicator",`
 background: var(--n-fill-color);
 font-size: 12px;
 transform: translateZ(0);
 display: flex;
 vertical-align: middle;
 height: 16px;
 line-height: 16px;
 padding: 0 10px;
 border-radius: 10px;
 position: absolute;
 white-space: nowrap;
 color: var(--n-text-color-line-inner);
 transition:
 right .2s var(--n-bezier),
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 `)]),f("progress-graph-line-rail",`
 position: relative;
 overflow: hidden;
 height: var(--n-rail-height);
 border-radius: 5px;
 background-color: var(--n-rail-color);
 transition: background-color .3s var(--n-bezier);
 `,[f("progress-graph-line-fill",`
 background: var(--n-fill-color);
 position: relative;
 border-radius: 5px;
 height: inherit;
 width: 100%;
 max-width: 0%;
 transition:
 background-color .3s var(--n-bezier),
 max-width .2s var(--n-bezier);
 `,[R("processing",[V("&::after",`
 content: "";
 background-image: var(--n-line-bg-processing);
 animation: progress-processing-animation 2s var(--n-bezier) infinite;
 `)])])])])])]),V("@keyframes progress-processing-animation",`
 0% {
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 right: 100%;
 opacity: 1;
 }
 66% {
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 right: 0;
 opacity: 0;
 }
 100% {
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 right: 0;
 opacity: 0;
 }
 `)]),Qa=Object.assign(Object.assign({},ve.props),{processing:Boolean,type:{type:String,default:"line"},gapDegree:Number,gapOffsetDegree:Number,status:{type:String,default:"default"},railColor:[String,Array],railStyle:[String,Array],color:[String,Array,Object],viewBoxWidth:{type:Number,default:100},strokeWidth:{type:Number,default:7},percentage:[Number,Array],unit:{type:String,default:"%"},showIndicator:{type:Boolean,default:!0},indicatorPosition:{type:String,default:"outside"},indicatorPlacement:{type:String,default:"outside"},indicatorTextColor:String,circleGap:{type:Number,default:1},height:Number,borderRadius:[String,Number],fillBorderRadius:[String,Number],offsetDegree:Number}),Ja=q({name:"Progress",props:Qa,setup(e){const t=I(()=>e.indicatorPlacement||e.indicatorPosition),r=I(()=>{if(e.gapDegree||e.gapDegree===0)return e.gapDegree;if(e.type==="dashboard")return 75}),{mergedClsPrefixRef:a,inlineThemeDisabled:o}=xe(e),s=ve("Progress","-progress",Ya,Ea,e,a),n=I(()=>{const{status:d}=e,{common:{cubicBezierEaseInOut:p},self:{fontSize:x,fontSizeCircle:w,railColor:m,railHeight:k,iconSizeCircle:h,iconSizeLine:y,textColorCircle:N,textColorLineInner:E,textColorLineOuter:S,lineBgProcessing:j,fontWeightCircle:W,[ee("iconColor",d)]:z,[ee("fillColor",d)]:$}}=s.value;return{"--n-bezier":p,"--n-fill-color":$,"--n-font-size":x,"--n-font-size-circle":w,"--n-font-weight-circle":W,"--n-icon-color":z,"--n-icon-size-circle":h,"--n-icon-size-line":y,"--n-line-bg-processing":j,"--n-rail-color":m,"--n-rail-height":k,"--n-text-color-circle":N,"--n-text-color-line-inner":E,"--n-text-color-line-outer":S}}),l=o?rt("progress",I(()=>e.status[0]),n,e):void 0;return{mergedClsPrefix:a,mergedIndicatorPlacement:t,gapDeg:r,cssVars:o?void 0:n,themeClass:l?.themeClass,onRender:l?.onRender}},render(){const{type:e,cssVars:t,indicatorTextColor:r,showIndicator:a,status:o,railColor:s,railStyle:n,color:l,percentage:d,viewBoxWidth:p,strokeWidth:x,mergedIndicatorPlacement:w,unit:m,borderRadius:k,fillBorderRadius:h,height:y,processing:N,circleGap:E,mergedClsPrefix:S,gapDeg:j,gapOffsetDegree:W,themeClass:z,$slots:$,onRender:M}=this;return M?.(),i("div",{class:[z,`${S}-progress`,`${S}-progress--${e}`,`${S}-progress--${o}`],style:t,"aria-valuemax":100,"aria-valuemin":0,"aria-valuenow":d,role:e==="circle"||e==="line"||e==="dashboard"?"progressbar":"none"},e==="circle"||e==="dashboard"?i(Fa,{clsPrefix:S,status:o,showIndicator:a,indicatorTextColor:r,railColor:s,fillColor:l,railStyle:n,offsetDegree:this.offsetDegree,percentage:d,viewBoxWidth:p,strokeWidth:x,gapDegree:j===void 0?e==="dashboard"?75:0:j,gapOffsetDegree:W,unit:m},$):e==="line"?i(qa,{clsPrefix:S,status:o,showIndicator:a,indicatorTextColor:r,railColor:s,fillColor:l,railStyle:n,percentage:d,processing:N,indicatorPlacement:w,unit:m,fillBorderRadius:h,railBorderRadius:k,height:y},$):e==="multiple-circle"?i(Xa,{clsPrefix:S,strokeWidth:x,railColor:s,fillColor:l,railStyle:n,viewBoxWidth:p,percentage:d,showIndicator:a,circleGap:E},$):null)}}),ot=Ze("n-tabs"),Gt={tab:[String,Number,Object,Function],name:{type:[String,Number],required:!0},disabled:Boolean,displayDirective:{type:String,default:"if"},closable:{type:Boolean,default:void 0},tabProps:Object,label:[String,Number,Object,Function]},Ct=q({__TAB_PANE__:!0,name:"TabPane",alias:["TabPanel"],props:Gt,slots:Object,setup(e){const t=Ae(ot,null);return t||zr("tab-pane","`n-tab-pane` must be placed inside `n-tabs`."),{style:t.paneStyleRef,class:t.paneClassRef,mergedClsPrefix:t.mergedClsPrefixRef}},render(){return i("div",{class:[`${this.mergedClsPrefix}-tab-pane`,this.class],style:this.style},this.$slots)}}),Ka=Object.assign({internalLeftPadded:Boolean,internalAddable:Boolean,internalCreatedByPane:Boolean},$t(Gt,["displayDirective"])),Xe=q({__TAB__:!0,inheritAttrs:!1,name:"Tab",props:Ka,setup(e){const{mergedClsPrefixRef:t,valueRef:r,typeRef:a,closableRef:o,tabStyleRef:s,addTabStyleRef:n,tabClassRef:l,addTabClassRef:d,tabChangeIdRef:p,onBeforeLeaveRef:x,triggerRef:w,handleAdd:m,activateTab:k,handleClose:h}=Ae(ot);return{trigger:w,mergedClosable:I(()=>{if(e.internalAddable)return!1;const{closable:y}=e;return y===void 0?o.value:y}),style:s,addStyle:n,tabClass:l,addTabClass:d,clsPrefix:t,value:r,type:a,handleClose(y){y.stopPropagation(),!e.disabled&&h(e.name)},activateTab(){if(e.disabled)return;if(e.internalAddable){m();return}const{name:y}=e,N=++p.id;if(y!==r.value){const{value:E}=x;E?Promise.resolve(E(e.name,r.value)).then(S=>{S&&p.id===N&&k(y)}):k(y)}}}},render(){const{internalAddable:e,clsPrefix:t,name:r,disabled:a,label:o,tab:s,value:n,mergedClosable:l,trigger:d,$slots:{default:p}}=this,x=o??s;return i("div",{class:`${t}-tabs-tab-wrapper`},this.internalLeftPadded?i("div",{class:`${t}-tabs-tab-pad`}):null,i("div",Object.assign({key:r,"data-name":r,"data-disabled":a?!0:void 0},Fe({class:[`${t}-tabs-tab`,n===r&&`${t}-tabs-tab--active`,a&&`${t}-tabs-tab--disabled`,l&&`${t}-tabs-tab--closable`,e&&`${t}-tabs-tab--addable`,e?this.addTabClass:this.tabClass],onClick:d==="click"?this.activateTab:void 0,onMouseenter:d==="hover"?this.activateTab:void 0,style:e?this.addStyle:this.style},this.internalCreatedByPane?this.tabProps||{}:this.$attrs)),i("span",{class:`${t}-tabs-tab__label`},e?i(zt,null,i("div",{class:`${t}-tabs-tab__height-placeholder`}," "),i(Ne,{clsPrefix:t},{default:()=>i(oa,null)})):p?p():typeof x=="object"?x:Br(x??r)),l&&this.type==="card"?i(Er,{clsPrefix:t,class:`${t}-tabs-tab__close`,onClick:this.handleClose,disabled:a}):null))}}),Za=f("tabs",`
 box-sizing: border-box;
 width: 100%;
 display: flex;
 flex-direction: column;
 transition:
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
`,[R("segment-type",[f("tabs-rail",[V("&.transition-disabled",[f("tabs-capsule",`
 transition: none;
 `)])])]),R("top",[f("tab-pane",`
 padding: var(--n-pane-padding-top) var(--n-pane-padding-right) var(--n-pane-padding-bottom) var(--n-pane-padding-left);
 `)]),R("left",[f("tab-pane",`
 padding: var(--n-pane-padding-right) var(--n-pane-padding-bottom) var(--n-pane-padding-left) var(--n-pane-padding-top);
 `)]),R("left, right",`
 flex-direction: row;
 `,[f("tabs-bar",`
 width: 2px;
 right: 0;
 transition:
 top .2s var(--n-bezier),
 max-height .2s var(--n-bezier),
 background-color .3s var(--n-bezier);
 `),f("tabs-tab",`
 padding: var(--n-tab-padding-vertical); 
 `)]),R("right",`
 flex-direction: row-reverse;
 `,[f("tab-pane",`
 padding: var(--n-pane-padding-left) var(--n-pane-padding-top) var(--n-pane-padding-right) var(--n-pane-padding-bottom);
 `),f("tabs-bar",`
 left: 0;
 `)]),R("bottom",`
 flex-direction: column-reverse;
 justify-content: flex-end;
 `,[f("tab-pane",`
 padding: var(--n-pane-padding-bottom) var(--n-pane-padding-right) var(--n-pane-padding-top) var(--n-pane-padding-left);
 `),f("tabs-bar",`
 top: 0;
 `)]),f("tabs-rail",`
 position: relative;
 padding: 3px;
 border-radius: var(--n-tab-border-radius);
 width: 100%;
 background-color: var(--n-color-segment);
 transition: background-color .3s var(--n-bezier);
 display: flex;
 align-items: center;
 `,[f("tabs-capsule",`
 border-radius: var(--n-tab-border-radius);
 position: absolute;
 pointer-events: none;
 background-color: var(--n-tab-color-segment);
 box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .08);
 transition: transform 0.3s var(--n-bezier);
 `),f("tabs-tab-wrapper",`
 flex-basis: 0;
 flex-grow: 1;
 display: flex;
 align-items: center;
 justify-content: center;
 `,[f("tabs-tab",`
 overflow: hidden;
 border-radius: var(--n-tab-border-radius);
 width: 100%;
 display: flex;
 align-items: center;
 justify-content: center;
 `,[R("active",`
 font-weight: var(--n-font-weight-strong);
 color: var(--n-tab-text-color-active);
 `),V("&:hover",`
 color: var(--n-tab-text-color-hover);
 `)])])]),R("flex",[f("tabs-nav",`
 width: 100%;
 position: relative;
 `,[f("tabs-wrapper",`
 width: 100%;
 `,[f("tabs-tab",`
 margin-right: 0;
 `)])])]),f("tabs-nav",`
 box-sizing: border-box;
 line-height: 1.5;
 display: flex;
 transition: border-color .3s var(--n-bezier);
 `,[F("prefix, suffix",`
 display: flex;
 align-items: center;
 `),F("prefix","padding-right: 16px;"),F("suffix","padding-left: 16px;")]),R("top, bottom",[f("tabs-nav-scroll-wrapper",[V("&::before",`
 top: 0;
 bottom: 0;
 left: 0;
 width: 20px;
 `),V("&::after",`
 top: 0;
 bottom: 0;
 right: 0;
 width: 20px;
 `),R("shadow-start",[V("&::before",`
 box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, .12);
 `)]),R("shadow-end",[V("&::after",`
 box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, .12);
 `)])])]),R("left, right",[f("tabs-nav-scroll-content",`
 flex-direction: column;
 `),f("tabs-nav-scroll-wrapper",[V("&::before",`
 top: 0;
 left: 0;
 right: 0;
 height: 20px;
 `),V("&::after",`
 bottom: 0;
 left: 0;
 right: 0;
 height: 20px;
 `),R("shadow-start",[V("&::before",`
 box-shadow: inset 0 10px 8px -8px rgba(0, 0, 0, .12);
 `)]),R("shadow-end",[V("&::after",`
 box-shadow: inset 0 -10px 8px -8px rgba(0, 0, 0, .12);
 `)])])]),f("tabs-nav-scroll-wrapper",`
 flex: 1;
 position: relative;
 overflow: hidden;
 `,[f("tabs-nav-y-scroll",`
 height: 100%;
 width: 100%;
 overflow-y: auto; 
 scrollbar-width: none;
 `,[V("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",`
 width: 0;
 height: 0;
 display: none;
 `)]),V("&::before, &::after",`
 transition: box-shadow .3s var(--n-bezier);
 pointer-events: none;
 content: "";
 position: absolute;
 z-index: 1;
 `)]),f("tabs-nav-scroll-content",`
 display: flex;
 position: relative;
 min-width: 100%;
 min-height: 100%;
 width: fit-content;
 box-sizing: border-box;
 `),f("tabs-wrapper",`
 display: inline-flex;
 flex-wrap: nowrap;
 position: relative;
 `),f("tabs-tab-wrapper",`
 display: flex;
 flex-wrap: nowrap;
 flex-shrink: 0;
 flex-grow: 0;
 `),f("tabs-tab",`
 cursor: pointer;
 white-space: nowrap;
 flex-wrap: nowrap;
 display: inline-flex;
 align-items: center;
 color: var(--n-tab-text-color);
 font-size: var(--n-tab-font-size);
 background-clip: padding-box;
 padding: var(--n-tab-padding);
 transition:
 box-shadow .3s var(--n-bezier),
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[R("disabled",{cursor:"not-allowed"}),F("close",`
 margin-left: 6px;
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 `),F("label",`
 display: flex;
 align-items: center;
 z-index: 1;
 `)]),f("tabs-bar",`
 position: absolute;
 bottom: 0;
 height: 2px;
 border-radius: 1px;
 background-color: var(--n-bar-color);
 transition:
 left .2s var(--n-bezier),
 max-width .2s var(--n-bezier),
 opacity .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 `,[V("&.transition-disabled",`
 transition: none;
 `),R("disabled",`
 background-color: var(--n-tab-text-color-disabled)
 `)]),f("tabs-pane-wrapper",`
 position: relative;
 overflow: hidden;
 transition: max-height .2s var(--n-bezier);
 `),f("tab-pane",`
 color: var(--n-pane-text-color);
 width: 100%;
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 opacity .2s var(--n-bezier);
 left: 0;
 right: 0;
 top: 0;
 `,[V("&.next-transition-leave-active, &.prev-transition-leave-active, &.next-transition-enter-active, &.prev-transition-enter-active",`
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 transform .2s var(--n-bezier),
 opacity .2s var(--n-bezier);
 `),V("&.next-transition-leave-active, &.prev-transition-leave-active",`
 position: absolute;
 `),V("&.next-transition-enter-from, &.prev-transition-leave-to",`
 transform: translateX(32px);
 opacity: 0;
 `),V("&.next-transition-leave-to, &.prev-transition-enter-from",`
 transform: translateX(-32px);
 opacity: 0;
 `),V("&.next-transition-leave-from, &.next-transition-enter-to, &.prev-transition-leave-from, &.prev-transition-enter-to",`
 transform: translateX(0);
 opacity: 1;
 `)]),f("tabs-tab-pad",`
 box-sizing: border-box;
 width: var(--n-tab-gap);
 flex-grow: 0;
 flex-shrink: 0;
 `),R("line-type, bar-type",[f("tabs-tab",`
 font-weight: var(--n-tab-font-weight);
 box-sizing: border-box;
 vertical-align: bottom;
 `,[V("&:hover",{color:"var(--n-tab-text-color-hover)"}),R("active",`
 color: var(--n-tab-text-color-active);
 font-weight: var(--n-tab-font-weight-active);
 `),R("disabled",{color:"var(--n-tab-text-color-disabled)"})])]),f("tabs-nav",[R("line-type",[R("top",[F("prefix, suffix",`
 border-bottom: 1px solid var(--n-tab-border-color);
 `),f("tabs-nav-scroll-content",`
 border-bottom: 1px solid var(--n-tab-border-color);
 `),f("tabs-bar",`
 bottom: -1px;
 `)]),R("left",[F("prefix, suffix",`
 border-right: 1px solid var(--n-tab-border-color);
 `),f("tabs-nav-scroll-content",`
 border-right: 1px solid var(--n-tab-border-color);
 `),f("tabs-bar",`
 right: -1px;
 `)]),R("right",[F("prefix, suffix",`
 border-left: 1px solid var(--n-tab-border-color);
 `),f("tabs-nav-scroll-content",`
 border-left: 1px solid var(--n-tab-border-color);
 `),f("tabs-bar",`
 left: -1px;
 `)]),R("bottom",[F("prefix, suffix",`
 border-top: 1px solid var(--n-tab-border-color);
 `),f("tabs-nav-scroll-content",`
 border-top: 1px solid var(--n-tab-border-color);
 `),f("tabs-bar",`
 top: -1px;
 `)]),F("prefix, suffix",`
 transition: border-color .3s var(--n-bezier);
 `),f("tabs-nav-scroll-content",`
 transition: border-color .3s var(--n-bezier);
 `),f("tabs-bar",`
 border-radius: 0;
 `)]),R("card-type",[F("prefix, suffix",`
 transition: border-color .3s var(--n-bezier);
 `),f("tabs-pad",`
 flex-grow: 1;
 transition: border-color .3s var(--n-bezier);
 `),f("tabs-tab-pad",`
 transition: border-color .3s var(--n-bezier);
 `),f("tabs-tab",`
 font-weight: var(--n-tab-font-weight);
 border: 1px solid var(--n-tab-border-color);
 background-color: var(--n-tab-color);
 box-sizing: border-box;
 position: relative;
 vertical-align: bottom;
 display: flex;
 justify-content: space-between;
 font-size: var(--n-tab-font-size);
 color: var(--n-tab-text-color);
 `,[R("addable",`
 padding-left: 8px;
 padding-right: 8px;
 font-size: 16px;
 justify-content: center;
 `,[F("height-placeholder",`
 width: 0;
 font-size: var(--n-tab-font-size);
 `),Ar("disabled",[V("&:hover",`
 color: var(--n-tab-text-color-hover);
 `)])]),R("closable","padding-right: 8px;"),R("active",`
 background-color: #0000;
 font-weight: var(--n-tab-font-weight-active);
 color: var(--n-tab-text-color-active);
 `),R("disabled","color: var(--n-tab-text-color-disabled);")])]),R("left, right",`
 flex-direction: column; 
 `,[F("prefix, suffix",`
 padding: var(--n-tab-padding-vertical);
 `),f("tabs-wrapper",`
 flex-direction: column;
 `),f("tabs-tab-wrapper",`
 flex-direction: column;
 `,[f("tabs-tab-pad",`
 height: var(--n-tab-gap-vertical);
 width: 100%;
 `)])]),R("top",[R("card-type",[f("tabs-scroll-padding","border-bottom: 1px solid var(--n-tab-border-color);"),F("prefix, suffix",`
 border-bottom: 1px solid var(--n-tab-border-color);
 `),f("tabs-tab",`
 border-top-left-radius: var(--n-tab-border-radius);
 border-top-right-radius: var(--n-tab-border-radius);
 `,[R("active",`
 border-bottom: 1px solid #0000;
 `)]),f("tabs-tab-pad",`
 border-bottom: 1px solid var(--n-tab-border-color);
 `),f("tabs-pad",`
 border-bottom: 1px solid var(--n-tab-border-color);
 `)])]),R("left",[R("card-type",[f("tabs-scroll-padding","border-right: 1px solid var(--n-tab-border-color);"),F("prefix, suffix",`
 border-right: 1px solid var(--n-tab-border-color);
 `),f("tabs-tab",`
 border-top-left-radius: var(--n-tab-border-radius);
 border-bottom-left-radius: var(--n-tab-border-radius);
 `,[R("active",`
 border-right: 1px solid #0000;
 `)]),f("tabs-tab-pad",`
 border-right: 1px solid var(--n-tab-border-color);
 `),f("tabs-pad",`
 border-right: 1px solid var(--n-tab-border-color);
 `)])]),R("right",[R("card-type",[f("tabs-scroll-padding","border-left: 1px solid var(--n-tab-border-color);"),F("prefix, suffix",`
 border-left: 1px solid var(--n-tab-border-color);
 `),f("tabs-tab",`
 border-top-right-radius: var(--n-tab-border-radius);
 border-bottom-right-radius: var(--n-tab-border-radius);
 `,[R("active",`
 border-left: 1px solid #0000;
 `)]),f("tabs-tab-pad",`
 border-left: 1px solid var(--n-tab-border-color);
 `),f("tabs-pad",`
 border-left: 1px solid var(--n-tab-border-color);
 `)])]),R("bottom",[R("card-type",[f("tabs-scroll-padding","border-top: 1px solid var(--n-tab-border-color);"),F("prefix, suffix",`
 border-top: 1px solid var(--n-tab-border-color);
 `),f("tabs-tab",`
 border-bottom-left-radius: var(--n-tab-border-radius);
 border-bottom-right-radius: var(--n-tab-border-radius);
 `,[R("active",`
 border-top: 1px solid #0000;
 `)]),f("tabs-tab-pad",`
 border-top: 1px solid var(--n-tab-border-color);
 `),f("tabs-pad",`
 border-top: 1px solid var(--n-tab-border-color);
 `)])])])]),eo=Object.assign(Object.assign({},ve.props),{value:[String,Number],defaultValue:[String,Number],trigger:{type:String,default:"click"},type:{type:String,default:"bar"},closable:Boolean,justifyContent:String,size:{type:String,default:"medium"},placement:{type:String,default:"top"},tabStyle:[String,Object],tabClass:String,addTabStyle:[String,Object],addTabClass:String,barWidth:Number,paneClass:String,paneStyle:[String,Object],paneWrapperClass:String,paneWrapperStyle:[String,Object],addable:[Boolean,Object],tabsPadding:{type:Number,default:0},animated:Boolean,onBeforeLeave:Function,onAdd:Function,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],onClose:[Function,Array],labelSize:String,activeName:[String,Number],onActiveNameChange:[Function,Array]}),to=q({name:"Tabs",props:eo,slots:Object,setup(e,{slots:t}){var r,a,o,s;const{mergedClsPrefixRef:n,inlineThemeDisabled:l}=xe(e),d=ve("Tabs","-tabs",Za,La,e,n),p=A(null),x=A(null),w=A(null),m=A(null),k=A(null),h=A(null),y=A(!0),N=A(!0),E=pt(e,["labelSize","size"]),S=pt(e,["activeName","value"]),j=A((a=(r=S.value)!==null&&r!==void 0?r:e.defaultValue)!==null&&a!==void 0?a:t.default?(s=(o=Te(t.default())[0])===null||o===void 0?void 0:o.props)===null||s===void 0?void 0:s.name:null),W=Gr(S,j),z={id:0},$=I(()=>{if(!(!e.justifyContent||e.type==="card"))return{display:"flex",justifyContent:e.justifyContent}});Ie(W,()=>{z.id=0,c(),P()});function M(){var g;const{value:b}=W;return b===null?null:(g=p.value)===null||g===void 0?void 0:g.querySelector(`[data-name="${b}"]`)}function Y(g){if(e.type==="card")return;const{value:b}=x;if(!b)return;const _=b.style.opacity==="0";if(g){const D=`${n.value}-tabs-bar--disabled`,{barWidth:H,placement:te}=e;if(g.dataset.disabled==="true"?b.classList.add(D):b.classList.remove(D),["top","bottom"].includes(te)){if(L(["top","maxHeight","height"]),typeof H=="number"&&g.offsetWidth>=H){const re=Math.floor((g.offsetWidth-H)/2)+g.offsetLeft;b.style.left=`${re}px`,b.style.maxWidth=`${H}px`}else b.style.left=`${g.offsetLeft}px`,b.style.maxWidth=`${g.offsetWidth}px`;b.style.width="8192px",_&&(b.style.transition="none"),b.offsetWidth,_&&(b.style.transition="",b.style.opacity="1")}else{if(L(["left","maxWidth","width"]),typeof H=="number"&&g.offsetHeight>=H){const re=Math.floor((g.offsetHeight-H)/2)+g.offsetTop;b.style.top=`${re}px`,b.style.maxHeight=`${H}px`}else b.style.top=`${g.offsetTop}px`,b.style.maxHeight=`${g.offsetHeight}px`;b.style.height="8192px",_&&(b.style.transition="none"),b.offsetHeight,_&&(b.style.transition="",b.style.opacity="1")}}}function Z(){if(e.type==="card")return;const{value:g}=x;g&&(g.style.opacity="0")}function L(g){const{value:b}=x;if(b)for(const _ of g)b.style[_]=""}function c(){if(e.type==="card")return;const g=M();g?Y(g):Z()}function P(){var g;const b=(g=k.value)===null||g===void 0?void 0:g.$el;if(!b)return;const _=M();if(!_)return;const{scrollLeft:D,offsetWidth:H}=b,{offsetLeft:te,offsetWidth:re}=_;D>te?b.scrollTo({top:0,left:te,behavior:"smooth"}):te+re>D+H&&b.scrollTo({top:0,left:te+re-H,behavior:"smooth"})}const u=A(null);let v=0,G=null;function we(g){const b=u.value;if(b){v=g.getBoundingClientRect().height;const _=`${v}px`,D=()=>{b.style.height=_,b.style.maxHeight=_};G?(D(),G(),G=null):G=D}}function Se(g){const b=u.value;if(b){const _=g.getBoundingClientRect().height,D=()=>{document.body.offsetHeight,b.style.maxHeight=`${_}px`,b.style.height=`${Math.max(v,_)}px`};G?(G(),G=null,D()):G=D}}function Vt(){const g=u.value;if(g){g.style.maxHeight="",g.style.height="";const{paneWrapperStyle:b}=e;if(typeof b=="string")g.style.cssText=b;else if(b){const{maxHeight:_,height:D}=b;_!==void 0&&(g.style.maxHeight=_),D!==void 0&&(g.style.height=D)}}}const nt={value:[]},it=A("next");function Ht(g){const b=W.value;let _="next";for(const D of nt.value){if(D===b)break;if(D===g){_="prev";break}}it.value=_,Ft(g)}function Ft(g){const{onActiveNameChange:b,onUpdateValue:_,"onUpdate:value":D}=e;b&&be(b,g),_&&be(_,g),D&&be(D,g),j.value=g}function Ut(g){const{onClose:b}=e;b&&be(b,g)}function st(){const{value:g}=x;if(!g)return;const b="transition-disabled";g.classList.add(b),c(),g.classList.remove(b)}const le=A(null);function Le({transitionDisabled:g}){const b=p.value;if(!b)return;g&&b.classList.add("transition-disabled");const _=M();_&&le.value&&(le.value.style.width=`${_.offsetWidth}px`,le.value.style.height=`${_.offsetHeight}px`,le.value.style.transform=`translateX(${_.offsetLeft-Vr(getComputedStyle(b).paddingLeft)}px)`,g&&le.value.offsetWidth),g&&b.classList.remove("transition-disabled")}Ie([W],()=>{e.type==="segment"&&We(()=>{Le({transitionDisabled:!1})})}),Ce(()=>{e.type==="segment"&&Le({transitionDisabled:!0})});let lt=0;function qt(g){var b;if(g.contentRect.width===0&&g.contentRect.height===0||lt===g.contentRect.width)return;lt=g.contentRect.width;const{type:_}=e;if((_==="line"||_==="bar")&&st(),_!=="segment"){const{placement:D}=e;De((D==="top"||D==="bottom"?(b=k.value)===null||b===void 0?void 0:b.$el:h.value)||null)}}const Xt=Oe(qt,64);Ie([()=>e.justifyContent,()=>e.size],()=>{We(()=>{const{type:g}=e;(g==="line"||g==="bar")&&st()})});const de=A(!1);function Yt(g){var b;const{target:_,contentRect:{width:D,height:H}}=g,te=_.parentElement.parentElement.offsetWidth,re=_.parentElement.parentElement.offsetHeight,{placement:ue}=e;if(!de.value)ue==="top"||ue==="bottom"?te<D&&(de.value=!0):re<H&&(de.value=!0);else{const{value:he}=m;if(!he)return;ue==="top"||ue==="bottom"?te-D>he.$el.offsetWidth&&(de.value=!1):re-H>he.$el.offsetHeight&&(de.value=!1)}De(((b=k.value)===null||b===void 0?void 0:b.$el)||null)}const Qt=Oe(Yt,64);function Jt(){const{onAdd:g}=e;g&&g(),We(()=>{const b=M(),{value:_}=k;!b||!_||_.scrollTo({left:b.offsetLeft,top:0,behavior:"smooth"})})}function De(g){if(!g)return;const{placement:b}=e;if(b==="top"||b==="bottom"){const{scrollLeft:_,scrollWidth:D,offsetWidth:H}=g;y.value=_<=0,N.value=_+H>=D}else{const{scrollTop:_,scrollHeight:D,offsetHeight:H}=g;y.value=_<=0,N.value=_+H>=D}}const Kt=Oe(g=>{De(g.target)},64);et(ot,{triggerRef:J(e,"trigger"),tabStyleRef:J(e,"tabStyle"),tabClassRef:J(e,"tabClass"),addTabStyleRef:J(e,"addTabStyle"),addTabClassRef:J(e,"addTabClass"),paneClassRef:J(e,"paneClass"),paneStyleRef:J(e,"paneStyle"),mergedClsPrefixRef:n,typeRef:J(e,"type"),closableRef:J(e,"closable"),valueRef:W,tabChangeIdRef:z,onBeforeLeaveRef:J(e,"onBeforeLeave"),activateTab:Ht,handleClose:Ut,handleAdd:Jt}),Jr(()=>{c(),P()}),Nr(()=>{const{value:g}=w;if(!g)return;const{value:b}=n,_=`${b}-tabs-nav-scroll-wrapper--shadow-start`,D=`${b}-tabs-nav-scroll-wrapper--shadow-end`;y.value?g.classList.remove(_):g.classList.add(_),N.value?g.classList.remove(D):g.classList.add(D)});const Zt={syncBarPosition:()=>{c()}},er=()=>{Le({transitionDisabled:!0})},dt=I(()=>{const{value:g}=E,{type:b}=e,_={card:"Card",bar:"Bar",line:"Line",segment:"Segment"}[b],D=`${g}${_}`,{self:{barColor:H,closeIconColor:te,closeIconColorHover:re,closeIconColorPressed:ue,tabColor:he,tabBorderColor:tr,paneTextColor:rr,tabFontWeight:ar,tabBorderRadius:or,tabFontWeightActive:nr,colorSegment:ir,fontWeightStrong:sr,tabColorSegment:lr,closeSize:dr,closeIconSize:cr,closeColorHover:ur,closeColorPressed:pr,closeBorderRadius:fr,[ee("panePadding",g)]:ke,[ee("tabPadding",D)]:gr,[ee("tabPaddingVertical",D)]:br,[ee("tabGap",D)]:vr,[ee("tabGap",`${D}Vertical`)]:hr,[ee("tabTextColor",b)]:mr,[ee("tabTextColorActive",b)]:yr,[ee("tabTextColorHover",b)]:xr,[ee("tabTextColorDisabled",b)]:Cr,[ee("tabFontSize",g)]:wr},common:{cubicBezierEaseInOut:Sr}}=d.value;return{"--n-bezier":Sr,"--n-color-segment":ir,"--n-bar-color":H,"--n-tab-font-size":wr,"--n-tab-text-color":mr,"--n-tab-text-color-active":yr,"--n-tab-text-color-disabled":Cr,"--n-tab-text-color-hover":xr,"--n-pane-text-color":rr,"--n-tab-border-color":tr,"--n-tab-border-radius":or,"--n-close-size":dr,"--n-close-icon-size":cr,"--n-close-color-hover":ur,"--n-close-color-pressed":pr,"--n-close-border-radius":fr,"--n-close-icon-color":te,"--n-close-icon-color-hover":re,"--n-close-icon-color-pressed":ue,"--n-tab-color":he,"--n-tab-font-weight":ar,"--n-tab-font-weight-active":nr,"--n-tab-padding":gr,"--n-tab-padding-vertical":br,"--n-tab-gap":vr,"--n-tab-gap-vertical":hr,"--n-pane-padding-left":$e(ke,"left"),"--n-pane-padding-right":$e(ke,"right"),"--n-pane-padding-top":$e(ke,"top"),"--n-pane-padding-bottom":$e(ke,"bottom"),"--n-font-weight-strong":sr,"--n-tab-color-segment":lr}}),ce=l?rt("tabs",I(()=>`${E.value[0]}${e.type[0]}`),dt,e):void 0;return Object.assign({mergedClsPrefix:n,mergedValue:W,renderedNames:new Set,segmentCapsuleElRef:le,tabsPaneWrapperRef:u,tabsElRef:p,barElRef:x,addTabInstRef:m,xScrollInstRef:k,scrollWrapperElRef:w,addTabFixed:de,tabWrapperStyle:$,handleNavResize:Xt,mergedSize:E,handleScroll:Kt,handleTabsResize:Qt,cssVars:l?void 0:dt,themeClass:ce?.themeClass,animationDirection:it,renderNameListRef:nt,yScrollElRef:h,handleSegmentResize:er,onAnimationBeforeLeave:we,onAnimationEnter:Se,onAnimationAfterEnter:Vt,onRender:ce?.onRender},Zt)},render(){const{mergedClsPrefix:e,type:t,placement:r,addTabFixed:a,addable:o,mergedSize:s,renderNameListRef:n,onRender:l,paneWrapperClass:d,paneWrapperStyle:p,$slots:{default:x,prefix:w,suffix:m}}=this;l?.();const k=x?Te(x()).filter(z=>z.type.__TAB_PANE__===!0):[],h=x?Te(x()).filter(z=>z.type.__TAB__===!0):[],y=!h.length,N=t==="card",E=t==="segment",S=!N&&!E&&this.justifyContent;n.value=[];const j=()=>{const z=i("div",{style:this.tabWrapperStyle,class:`${e}-tabs-wrapper`},S?null:i("div",{class:`${e}-tabs-scroll-padding`,style:r==="top"||r==="bottom"?{width:`${this.tabsPadding}px`}:{height:`${this.tabsPadding}px`}}),y?k.map(($,M)=>(n.value.push($.props.name),He(i(Xe,Object.assign({},$.props,{internalCreatedByPane:!0,internalLeftPadded:M!==0&&(!S||S==="center"||S==="start"||S==="end")}),$.children?{default:$.children.tab}:void 0)))):h.map(($,M)=>(n.value.push($.props.name),He(M!==0&&!S?kt($):$))),!a&&o&&N?St(o,(y?k.length:h.length)!==0):null,S?null:i("div",{class:`${e}-tabs-scroll-padding`,style:{width:`${this.tabsPadding}px`}}));return i("div",{ref:"tabsElRef",class:`${e}-tabs-nav-scroll-content`},N&&o?i(Re,{onResize:this.handleTabsResize},{default:()=>z}):z,N?i("div",{class:`${e}-tabs-pad`}):null,N?null:i("div",{ref:"barElRef",class:`${e}-tabs-bar`}))},W=E?"top":r;return i("div",{class:[`${e}-tabs`,this.themeClass,`${e}-tabs--${t}-type`,`${e}-tabs--${s}-size`,S&&`${e}-tabs--flex`,`${e}-tabs--${W}`],style:this.cssVars},i("div",{class:[`${e}-tabs-nav--${t}-type`,`${e}-tabs-nav--${W}`,`${e}-tabs-nav`]},qe(w,z=>z&&i("div",{class:`${e}-tabs-nav__prefix`},z)),E?i(Re,{onResize:this.handleSegmentResize},{default:()=>i("div",{class:`${e}-tabs-rail`,ref:"tabsElRef"},i("div",{class:`${e}-tabs-capsule`,ref:"segmentCapsuleElRef"},i("div",{class:`${e}-tabs-wrapper`},i("div",{class:`${e}-tabs-tab`}))),y?k.map((z,$)=>(n.value.push(z.props.name),i(Xe,Object.assign({},z.props,{internalCreatedByPane:!0,internalLeftPadded:$!==0}),z.children?{default:z.children.tab}:void 0))):h.map((z,$)=>(n.value.push(z.props.name),$===0?z:kt(z))))}):i(Re,{onResize:this.handleNavResize},{default:()=>i("div",{class:`${e}-tabs-nav-scroll-wrapper`,ref:"scrollWrapperElRef"},["top","bottom"].includes(W)?i(fa,{ref:"xScrollInstRef",onScroll:this.handleScroll},{default:j}):i("div",{class:`${e}-tabs-nav-y-scroll`,onScroll:this.handleScroll,ref:"yScrollElRef"},j()))}),a&&o&&N?St(o,!0):null,qe(m,z=>z&&i("div",{class:`${e}-tabs-nav__suffix`},z))),y&&(this.animated&&(W==="top"||W==="bottom")?i("div",{ref:"tabsPaneWrapperRef",style:p,class:[`${e}-tabs-pane-wrapper`,d]},wt(k,this.mergedValue,this.renderedNames,this.onAnimationBeforeLeave,this.onAnimationEnter,this.onAnimationAfterEnter,this.animationDirection)):wt(k,this.mergedValue,this.renderedNames)))}});function wt(e,t,r,a,o,s,n){const l=[];return e.forEach(d=>{const{name:p,displayDirective:x,"display-directive":w}=d.props,m=h=>x===h||w===h,k=t===p;if(d.key!==void 0&&(d.key=p),k||m("show")||m("show:lazy")&&r.has(p)){r.has(p)||r.add(p);const h=!m("if");l.push(h?Lr(d,[[Je,k]]):d)}}),n?i(Dr,{name:`${n}-transition`,onBeforeLeave:a,onEnter:o,onAfterEnter:s},{default:()=>l}):l}function St(e,t){return i(Xe,{ref:"addTabInstRef",key:"__addable",name:"__addable",internalCreatedByPane:!0,internalAddable:!0,internalLeftPadded:t,disabled:typeof e=="object"&&e.disabled})}function kt(e){const t=Ue(e);return t.props?t.props.internalLeftPadded=!0:t.props={internalLeftPadded:!0},t}function He(e){return Array.isArray(e.dynamicProps)?e.dynamicProps.includes("internalLeftPadded")||e.dynamicProps.push("internalLeftPadded"):e.dynamicProps=["internalLeftPadded"],e}const ro={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ao=q({name:"AddOutline",render:function(t,r){return Q(),oe("svg",ro,r[0]||(r[0]=[O("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 112v288"},null,-1),O("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M400 256H112"},null,-1)]))}}),oo={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},no=q({name:"ChevronDownOutline",render:function(t,r){return Q(),oe("svg",oo,r[0]||(r[0]=[O("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"48",d:"M112 184l144 144l144-144"},null,-1)]))}}),io={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ye=q({name:"CopyOutline",render:function(t,r){return Q(),oe("svg",io,r[0]||(r[0]=[O("rect",{x:"128",y:"128",width:"336",height:"336",rx:"57",ry:"57",fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"32"},null,-1),O("path",{d:"M383.5 128l.5-24a56.16 56.16 0 0 0-56-56H112a64.19 64.19 0 0 0-64 64v216a56.16 56.16 0 0 0 56 56h24",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1)]))}}),so={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},lo=q({name:"EyeOutline",render:function(t,r){return Q(),oe("svg",so,r[0]||(r[0]=[O("path",{d:"M255.66 112c-77.94 0-157.89 45.11-220.83 135.33a16 16 0 0 0-.27 17.77C82.92 340.8 161.8 400 255.66 400c92.84 0 173.34-59.38 221.79-135.25a16.14 16.14 0 0 0 0-17.47C428.89 172.28 347.8 112 255.66 112z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),O("circle",{cx:"256",cy:"256",r:"80",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1)]))}}),co={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Qe=q({name:"TrashOutline",render:function(t,r){return Q(),oe("svg",co,r[0]||(r[0]=[Ir('<path d="M112 112l20 320c.95 18.49 14.4 32 32 32h184c17.67 0 30.87-13.51 32-32l20-320" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="32" d="M80 112h352" fill="currentColor"></path><path d="M192 112V72h0a23.93 23.93 0 0 1 24-24h80a23.93 23.93 0 0 1 24 24h0v40" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M256 176v224"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M184 176l8 224"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M328 176l-8 224"></path>',6)]))}}),uo={class:"card-generator"},po={class:"form-actions"},fo={class:"preview-content"},go={class:"preview-item"},bo={class:"preview-item"},vo={class:"progress-content"},ho={class:"progress-text"},mo={class:"result-content"},yo={class:"result-summary"},xo={class:"result-list"},Co={class:"card-info"},wo={class:"card-code"},So={class:"card-type"},ko={class:"result-actions"},Po={style:{display:"flex","justify-content":"flex-end",gap:"12px"}},$o=q({__name:"CardGenerator",setup(e){const t=Dt(),r=A(null),a=Bt({type:"M",count:1,customCode:""}),o={type:[{required:!0,message:"请选择卡密类型",trigger:["change","blur"]}],count:[{required:!0,type:"number",min:1,max:100,message:"生成数量必须在1-100之间",trigger:["input","blur"]}],customCode:[{validator:(L,c)=>c?c.length!==32?new Error("自定义卡密必须是32位字符"):/^[A-Za-z0-9]+$/.test(c)?a.count>1?new Error("自定义卡密仅在生成单个卡密时有效"):!0:new Error("自定义卡密只能包含字母和数字"):!0,trigger:["input","blur"]}]},s=A(""),n=A(!1),l=A(!1),d=I(()=>t.cardTypes.map(L=>({label:`${L.label} - ${L.description}`,value:L.value}))),p=I(()=>t.isGenerating),x=I(()=>t.generateProgress),w=I(()=>t.generateTotal),m=I(()=>t.generatedCards),k=I(()=>t.error),h=I(()=>w.value===0?0:Math.round(x.value/w.value*100)),y=[{label:"仅卡密号",key:"codes"},{label:"卡密+类型",key:"with-type"},{label:"CSV格式",key:"csv"},{label:"JSON格式",key:"json"}],N=L=>{const c=t.cardTypes.find(P=>P.value===L);return c?c.label:L},E=()=>{s.value=""},S=async()=>{if(r.value)try{await r.value.validate(void 0,L=>L?.key==="type"),n.value=!0,s.value=await t.generatePreview(a.type)}catch(L){console.error("预览失败:",L)}finally{n.value=!1}},j=async()=>{if(r.value)try{await r.value.validate();const L=a.customCode.trim()||void 0;await t.generateCards(a.type,a.count,L);const c=L?`成功生成自定义卡密: ${L}`:`成功生成 ${a.count} 个卡密`;console.log(c),l.value=!0,L&&(a.customCode="")}catch(L){console.error("生成卡密失败:",L)}},W=async L=>{try{await navigator.clipboard.writeText(L),console.log("卡密已复制到剪贴板")}catch(c){console.error("复制失败:",c)}},z=async()=>{try{const L=m.value.join(`
`);await navigator.clipboard.writeText(L),console.log("所有卡密已复制到剪贴板")}catch(L){console.error("复制失败:",L)}},$=async L=>{try{let c="";const P=m.value,u=N(a.type);switch(L){case"codes":c=P.join(`
`);break;case"with-type":c=P.map(v=>`${v} - ${u}`).join(`
`);break;case"csv":c=`卡密号,类型
`+P.map(v=>`${v},${u}`).join(`
`);break;case"json":c=JSON.stringify(P.map(v=>({code:v,type:a.type,typeName:u})),null,2);break}await navigator.clipboard.writeText(c),console.log("已复制到剪贴板")}catch(c){console.error("复制失败:",c)}},M=()=>{t.clearGeneratedCards(),s.value="",l.value=!1},Y=()=>{l.value=!1},Z=()=>{t.clearError()};return Ce(async()=>{await t.loadCardTypes()}),(L,c)=>(Q(),oe("div",uo,[T(C(je),{title:"生成卡密",size:"large"},{default:B(()=>[T(C(Nt),{ref_key:"formRef",ref:r,model:a,rules:o,"label-placement":"top",size:"large"},{default:B(()=>[T(C(ja),{cols:2,"x-gap":24,responsive:"screen"},{default:B(()=>[T(C(Ge),{span:2},{default:B(()=>[T(C(ze),{label:"卡密类型",path:"type"},{default:B(()=>[T(C(ea),{value:a.type,"onUpdate:value":[c[0]||(c[0]=P=>a.type=P),E],options:d.value,placeholder:"请选择卡密类型"},null,8,["value","options"])]),_:1})]),_:1}),T(C(Ge),{span:2},{default:B(()=>[T(C(ze),{label:"生成数量",path:"count"},{feedback:B(()=>c[4]||(c[4]=[U(" 单次最多可生成 100 个卡密 (新API支持批量生成) ")])),default:B(()=>[T(C(na),{value:a.count,"onUpdate:value":c[1]||(c[1]=P=>a.count=P),min:1,max:100,placeholder:"请输入生成数量",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),T(C(Ge),{span:2},{default:B(()=>[T(C(ze),{label:"自定义卡密 (可选)",path:"customCode"},{feedback:B(()=>c[5]||(c[5]=[U(" 自定义卡密必须是32位字符，仅在生成单个卡密时有效 ")])),default:B(()=>[T(C(At),{value:a.customCode,"onUpdate:value":c[2]||(c[2]=P=>a.customCode=P),placeholder:"输入32位自定义卡密，留空则自动生成",maxlength:"32","show-count":"",clearable:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),O("div",po,[T(C(X),{type:"primary",size:"large",onClick:j,loading:p.value,disabled:p.value},{icon:B(()=>[T(C(K),{component:C(ao)},null,8,["component"])]),default:B(()=>[c[6]||(c[6]=U(" 生成卡密 "))]),_:1,__:[6]},8,["loading","disabled"]),T(C(X),{size:"large",onClick:S,loading:n.value},{icon:B(()=>[T(C(K),{component:C(lo)},null,8,["component"])]),default:B(()=>[c[7]||(c[7]=U(" 预览格式 "))]),_:1,__:[7]},8,["loading"])])]),_:1},8,["model"])]),_:1}),s.value?(Q(),_e(C(je),{key:0,title:"预览",size:"large",style:{"margin-top":"24px"}},{default:B(()=>[O("div",fo,[O("div",go,[c[8]||(c[8]=O("strong",null,"卡密号：",-1)),U(se(s.value),1)]),O("div",bo,[c[9]||(c[9]=O("strong",null,"类型：",-1)),U(se(N(a.type)),1)])])]),_:1})):ye("",!0),p.value?(Q(),_e(C(je),{key:1,title:"生成进度",size:"large",style:{"margin-top":"24px"}},{default:B(()=>[O("div",vo,[O("div",ho," 正在生成: "+se(x.value)+"/"+se(w.value),1),T(C(Ja),{type:"line",percentage:h.value,"show-indicator":!1,style:{"margin-top":"12px"}},null,8,["percentage"])])]),_:1})):ye("",!0),T(C(Lt),{show:l.value,"onUpdate:show":c[3]||(c[3]=P=>l.value=P),preset:"card",title:"生成结果",size:"large",style:{width:"90%","max-width":"800px"},"mask-closable":!1},{footer:B(()=>[O("div",Po,[T(C(X),{onClick:Y},{default:B(()=>c[14]||(c[14]=[U("关闭")])),_:1,__:[14]})])]),default:B(()=>[O("div",mo,[O("div",yo,[T(C(Ee),{type:"success","show-icon":!1,style:{"margin-bottom":"16px"}},{default:B(()=>[U(" 成功生成 "+se(m.value.length)+" 个卡密 ",1)]),_:1})]),O("div",xo,[(Q(!0),oe(zt,null,Wr(m.value,(P,u)=>(Q(),oe("div",{key:u,class:"result-item"},[O("div",Co,[O("div",wo,se(P),1),O("div",So,se(N(a.type)),1)]),T(C(X),{size:"small",onClick:v=>W(P)},{icon:B(()=>[T(C(K),{component:C(Ye)},null,8,["component"])]),default:B(()=>[c[10]||(c[10]=U(" 复制 "))]),_:2,__:[10]},1032,["onClick"])]))),128))]),O("div",ko,[T(C(X),{type:"primary",onClick:z},{icon:B(()=>[T(C(K),{component:C(Ye)},null,8,["component"])]),default:B(()=>[c[11]||(c[11]=U(" 复制全部 "))]),_:1,__:[11]}),T(C(Kr),{options:y,onSelect:$},{default:B(()=>[T(C(X),null,{icon:B(()=>[T(C(K),{component:C(no)},null,8,["component"])]),default:B(()=>[c[12]||(c[12]=U(" 复制格式 "))]),_:1,__:[12]})]),_:1}),T(C(X),{onClick:M},{icon:B(()=>[T(C(K),{component:C(Qe)},null,8,["component"])]),default:B(()=>[c[13]||(c[13]=U(" 清空结果 "))]),_:1,__:[13]})])])]),_:1},8,["show"]),k.value?(Q(),_e(C(Ee),{key:2,type:"error",title:k.value,closable:"",onClose:Z,style:{"margin-top":"16px"}},null,8,["title"])):ye("",!0)]))}}),_o=at($o,[["__scopeId","data-v-0eea2483"]]),Ro={class:"card-query"},To={class:"search-section"},zo={key:0,class:"batch-actions"},Bo={class:"batch-buttons"},Eo={class:"table-section"},Ao={style:{display:"flex","align-items":"center",gap:"8px"}},No=q({__name:"CardQuery",setup(e){const t=Dt(),r=ia(),a=A(!1),o=A(!1),s=A(!1),n=A(null),l=A(1),d=A(20),p=Bt({oldCode:"",newCode:"",type:""}),x={newCode:[{required:!0,message:"请输入卡密号",trigger:["input","blur"]}]},w=I(()=>t.filteredCards),m=I(()=>t.isLoading),k=I(()=>t.error),h=I({get:()=>t.selectedCards,set:u=>{t.selectedCards=u}}),y=I({get:()=>t.searchTerm,set:u=>t.setSearch(u)}),N=[{key:"code",title:"卡密号",sortable:!0,width:250,minWidth:200,render:u=>i("span",{style:"font-family: monospace; font-weight: 600; color: #1f2937;"},u.code)},{key:"type",title:"类型",sortable:!0,width:140,minWidth:100,render:u=>i(ft,{type:"info",size:"small"},{default:()=>ae.getCardTypeName(u.type||u.package_type||"Unknown")})},{key:"status",title:"状态",sortable:!0,width:120,minWidth:80,render:u=>i(ft,{type:u.status==="unused"?"success":"error",size:"small"},{default:()=>u.status==="unused"?"未使用":"已使用"})},{key:"usedBy",title:"使用者",width:200,minWidth:150,render:u=>u.usedBy||"-"},{key:"totalChars",title:"历史总使用量",width:150,minWidth:120,render:u=>{if(u.status==="unused")return i("span",{style:"color: #9ca3af;"},"-");const v=u.userUsage?.totalChars??u.totalChars;return v!==void 0&&v>0?i("span",{style:"color: #1f2937; font-weight: 500;"},v.toLocaleString()):i("span",{style:"color: #f59e0b;"},"无数据")}},{key:"monthlyChars",title:"当月使用量",width:140,minWidth:120,render:u=>{if(u.status==="unused")return i("span",{style:"color: #9ca3af;"},"-");const v=u.userUsage?.monthlyChars;return v!==void 0&&v>0?i("span",{style:"color: #059669; font-weight: 500;"},v.toLocaleString()):u.userUsage?i("span",{style:"color: #9ca3af;"},"0"):i("span",{style:"color: #f59e0b;"},"无数据")}},{key:"activatedAt",title:"激活时间",sortable:!0,width:200,minWidth:160,render:u=>u.activatedAt?new Date(u.activatedAt).toLocaleString("zh-CN"):"-"},{key:"createdAt",title:"创建时间",sortable:!0,width:200,minWidth:160,render:u=>u.createdAt?new Date(u.createdAt).toLocaleString("zh-CN"):"-"},{key:"actions",title:"操作",width:260,minWidth:240,fixed:"right",render:u=>i("div",{style:"display: flex; gap: 6px; flex-wrap: nowrap; justify-content: flex-start;"},[i(X,{size:"small",onClick:()=>$(u.code),style:"flex-shrink: 0;"},{default:()=>"复制",icon:()=>i(K,{component:Ye})}),i(X,{size:"small",type:"primary",onClick:()=>M(u),style:"flex-shrink: 0;"},{default:()=>"编辑",icon:()=>i(K,{component:bt})}),i(Va,{onPositiveClick:()=>Z(u.code)},{default:()=>"确定要删除这个卡密吗？",trigger:()=>i(X,{size:"small",type:"error",style:"flex-shrink: 0;"},{default:()=>"删除",icon:()=>i(K,{component:Qe})})})])}],E=u=>{l.value=u,console.log("卡密查询页码变化:",u)},S=u=>{d.value=u,l.value=1,console.log("卡密查询页面大小变化:",u)},j=u=>{t.setSearch(u)},W=async()=>{try{await t.loadCards(),r.success("数据刷新成功")}catch(u){console.error("数据刷新失败:",u),r.error(u instanceof Error?u.message:"数据刷新失败")}},z=u=>{u.forEach(v=>{const G=String(v);t.selectedCards.includes(G)||t.toggleCardSelection(G)}),t.selectedCards.forEach(v=>{u.includes(v)||t.toggleCardSelection(v)})},$=async u=>{try{await navigator.clipboard.writeText(u),r.success("卡密已复制到剪贴板")}catch(v){console.error("复制失败:",v),r.error("复制失败，请手动复制")}},M=u=>{p.oldCode=u.code,p.newCode=u.code,p.type=u.type||u.package_type||"Unknown",a.value=!0},Y=async()=>{if(n.value)try{await n.value.validate(),o.value=!0,await t.editCard(p.oldCode,p.newCode,p.type),r.success("卡密编辑成功"),a.value=!1}catch(u){console.error("卡密编辑失败:",u),r.error(u instanceof Error?u.message:"卡密编辑失败")}finally{o.value=!1}},Z=async u=>{try{await t.deleteCard(u),r.success(`卡密 ${u} 删除成功`)}catch(v){console.error("卡密删除失败:",v),r.error(v instanceof Error?v.message:"卡密删除失败")}},L=async()=>{const u=h.value.length;s.value=!0;try{await t.deleteSelectedCards(),r.success(`成功删除 ${u} 个卡密`)}catch(v){console.error("批量删除失败:",v),r.error(v instanceof Error?v.message:"批量删除失败")}finally{s.value=!1}},c=()=>{t.selectedCards.forEach(u=>{t.toggleCardSelection(u)})},P=()=>{t.clearError()};return Ce(()=>{t.loadCards()}),Et(()=>{}),(u,v)=>(Q(),oe("div",Ro,[O("div",To,[T(C(ta),{modelValue:y.value,"onUpdate:modelValue":v[0]||(v[0]=G=>y.value=G),placeholder:"搜索卡密号、类型、状态、使用者...",onSearch:j},null,8,["modelValue"]),T(C(X),{type:"primary",onClick:W,loading:m.value},{icon:B(()=>[T(C(K),{component:C(ra)},null,8,["component"])]),default:B(()=>[v[4]||(v[4]=U(" 刷新数据 "))]),_:1,__:[4]},8,["loading"])]),h.value.length>0?(Q(),oe("div",zo,[T(C(Ee),{type:"info","show-icon":!1},{header:B(()=>[U(" 已选择 "+se(h.value.length)+" 个卡密 ",1)]),default:B(()=>[O("div",Bo,[T(C(X),{type:"error",size:"small",onClick:L,loading:s.value},{icon:B(()=>[T(C(K),{component:C(Qe)},null,8,["component"])]),default:B(()=>[v[5]||(v[5]=U(" 批量删除 "))]),_:1,__:[5]},8,["loading"]),T(C(X),{size:"small",onClick:c},{default:B(()=>v[6]||(v[6]=[U(" 取消选择 ")])),_:1,__:[6]})])]),_:1})])):ye("",!0),O("div",Eo,[T(C(aa),{data:w.value,columns:N,loading:m.value,checkable:!0,"checked-row-keys":h.value,"onUpdate:checkedRowKeys":z,"row-key":G=>`card-${G.code}-${G.type}-${G.status}`,"scroll-x":1700,pagination:{page:l.value,pageSize:d.value,showSizePicker:!0,pageSizes:[10,20,50,100],itemCount:w.value.length},"onUpdate:page":E,"onUpdate:pageSize":S},null,8,["data","loading","checked-row-keys","row-key","pagination"])]),T(C(Lt),{show:a.value,"onUpdate:show":v[3]||(v[3]=G=>a.value=G),preset:"dialog",title:"编辑卡密"},{header:B(()=>[O("div",Ao,[T(C(K),{component:C(bt)},null,8,["component"]),v[7]||(v[7]=U(" 编辑卡密 "))])]),action:B(()=>[T(C(X),{onClick:v[2]||(v[2]=G=>a.value=!1)},{default:B(()=>v[8]||(v[8]=[U("取消")])),_:1,__:[8]}),T(C(X),{type:"primary",onClick:Y,loading:o.value},{default:B(()=>v[9]||(v[9]=[U(" 保存 ")])),_:1,__:[9]},8,["loading"])]),default:B(()=>[T(C(Nt),{ref_key:"editFormRef",ref:n,model:p,rules:x,"label-placement":"top"},{default:B(()=>[T(C(ze),{label:"卡密号",path:"newCode"},{default:B(()=>[T(C(At),{value:p.newCode,"onUpdate:value":v[1]||(v[1]=G=>p.newCode=G),placeholder:"请输入新的卡密号"},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["show"]),k.value?(Q(),_e(C(Ee),{key:1,type:"error",title:k.value,closable:"",onClose:P,style:{"margin-top":"16px"}},null,8,["title"])):ye("",!0)]))}}),Lo=at(No,[["__scopeId","data-v-b5b664a4"]]),Do={class:"cards-view"},Io={class:"page-header"},Wo={class:"page-title"},jo=q({__name:"CardsView",setup(e){const t=A("generate");return Ce(()=>{}),Et(()=>{}),Pt(()=>{}),(r,a)=>(Q(),oe("div",Do,[O("div",Io,[O("h2",Wo,[T(C(K),{component:C(sa)},null,8,["component"]),a[1]||(a[1]=U(" 卡密管理 "))]),a[2]||(a[2]=O("p",{class:"page-description"},"生成和管理系统卡密",-1))]),T(C(to),{value:t.value,"onUpdate:value":a[0]||(a[0]=o=>t.value=o),type:"line",size:"large"},{default:B(()=>[T(C(Ct),{name:"generate",tab:"生成卡密"},{default:B(()=>[T(_o)]),_:1}),T(C(Ct),{name:"query",tab:"卡密查询"},{default:B(()=>[T(Lo)]),_:1})]),_:1},8,["value"])]))}}),Xo=at(jo,[["__scopeId","data-v-b18ea696"]]);export{Xo as default};
